# Generated by the Modal Protocol Buffers compiler. DO NOT EDIT!
# source: modal_proto/api.proto
# plugin: __main__

import modal._utils.grpc_utils
import modal_proto.api_grpc
import typing
if typing.TYPE_CHECKING:
    import modal.client


class ModalClientModal:
    @classmethod
    async def _create(cls, client: 'modal.client._Client', server_url: str):
        channel = await client._get_channel(server_url)
        grpclib_stub = modal_proto.api_grpc.ModalClientStub(channel)
        return cls(grpclib_stub, client, server_url)


    def __init__(self, grpclib_stub: modal_proto.api_grpc.ModalClientStub, client: "modal.client._Client", server_url: str) -> None:
        self.AppClientDisconnect = modal.client.UnaryUnaryWrapper(grpclib_stub.AppClientDisconnect, client, server_url)
        self.AppCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.AppCreate, client, server_url)
        self.AppDeploy = modal.client.UnaryUnaryWrapper(grpclib_stub.AppDeploy, client, server_url)
        self.AppDeploymentHistory = modal.client.UnaryUnaryWrapper(grpclib_stub.AppDeploymentHistory, client, server_url)
        self.AppGetByDeploymentName = modal.client.UnaryUnaryWrapper(grpclib_stub.AppGetByDeploymentName, client, server_url)
        self.AppGetLayout = modal.client.UnaryUnaryWrapper(grpclib_stub.AppGetLayout, client, server_url)
        self.AppGetLogs = modal.client.UnaryStreamWrapper(grpclib_stub.AppGetLogs, client, server_url)
        self.AppGetObjects = modal.client.UnaryUnaryWrapper(grpclib_stub.AppGetObjects, client, server_url)
        self.AppGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.AppGetOrCreate, client, server_url)
        self.AppHeartbeat = modal.client.UnaryUnaryWrapper(grpclib_stub.AppHeartbeat, client, server_url)
        self.AppList = modal.client.UnaryUnaryWrapper(grpclib_stub.AppList, client, server_url)
        self.AppLookup = modal.client.UnaryUnaryWrapper(grpclib_stub.AppLookup, client, server_url)
        self.AppPublish = modal.client.UnaryUnaryWrapper(grpclib_stub.AppPublish, client, server_url)
        self.AppRollback = modal.client.UnaryUnaryWrapper(grpclib_stub.AppRollback, client, server_url)
        self.AppSetObjects = modal.client.UnaryUnaryWrapper(grpclib_stub.AppSetObjects, client, server_url)
        self.AppStop = modal.client.UnaryUnaryWrapper(grpclib_stub.AppStop, client, server_url)
        self.AttemptAwait = modal.client.UnaryUnaryWrapper(grpclib_stub.AttemptAwait, client, server_url)
        self.AttemptRetry = modal.client.UnaryUnaryWrapper(grpclib_stub.AttemptRetry, client, server_url)
        self.AttemptStart = modal.client.UnaryUnaryWrapper(grpclib_stub.AttemptStart, client, server_url)
        self.BlobCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.BlobCreate, client, server_url)
        self.BlobGet = modal.client.UnaryUnaryWrapper(grpclib_stub.BlobGet, client, server_url)
        self.ClassCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.ClassCreate, client, server_url)
        self.ClassGet = modal.client.UnaryUnaryWrapper(grpclib_stub.ClassGet, client, server_url)
        self.ClientHello = modal.client.UnaryUnaryWrapper(grpclib_stub.ClientHello, client, server_url)
        self.ClusterGet = modal.client.UnaryUnaryWrapper(grpclib_stub.ClusterGet, client, server_url)
        self.ClusterList = modal.client.UnaryUnaryWrapper(grpclib_stub.ClusterList, client, server_url)
        self.ContainerCheckpoint = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerCheckpoint, client, server_url)
        self.ContainerExec = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerExec, client, server_url)
        self.ContainerExecGetOutput = modal.client.UnaryStreamWrapper(grpclib_stub.ContainerExecGetOutput, client, server_url)
        self.ContainerExecPutInput = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerExecPutInput, client, server_url)
        self.ContainerExecWait = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerExecWait, client, server_url)
        self.ContainerFilesystemExec = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerFilesystemExec, client, server_url)
        self.ContainerFilesystemExecGetOutput = modal.client.UnaryStreamWrapper(grpclib_stub.ContainerFilesystemExecGetOutput, client, server_url)
        self.ContainerHeartbeat = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerHeartbeat, client, server_url)
        self.ContainerHello = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerHello, client, server_url)
        self.ContainerLog = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerLog, client, server_url)
        self.ContainerStop = modal.client.UnaryUnaryWrapper(grpclib_stub.ContainerStop, client, server_url)
        self.DictClear = modal.client.UnaryUnaryWrapper(grpclib_stub.DictClear, client, server_url)
        self.DictContains = modal.client.UnaryUnaryWrapper(grpclib_stub.DictContains, client, server_url)
        self.DictContents = modal.client.UnaryStreamWrapper(grpclib_stub.DictContents, client, server_url)
        self.DictDelete = modal.client.UnaryUnaryWrapper(grpclib_stub.DictDelete, client, server_url)
        self.DictGet = modal.client.UnaryUnaryWrapper(grpclib_stub.DictGet, client, server_url)
        self.DictGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.DictGetOrCreate, client, server_url)
        self.DictHeartbeat = modal.client.UnaryUnaryWrapper(grpclib_stub.DictHeartbeat, client, server_url)
        self.DictLen = modal.client.UnaryUnaryWrapper(grpclib_stub.DictLen, client, server_url)
        self.DictList = modal.client.UnaryUnaryWrapper(grpclib_stub.DictList, client, server_url)
        self.DictPop = modal.client.UnaryUnaryWrapper(grpclib_stub.DictPop, client, server_url)
        self.DictUpdate = modal.client.UnaryUnaryWrapper(grpclib_stub.DictUpdate, client, server_url)
        self.DomainCertificateVerify = modal.client.UnaryUnaryWrapper(grpclib_stub.DomainCertificateVerify, client, server_url)
        self.DomainCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.DomainCreate, client, server_url)
        self.DomainList = modal.client.UnaryUnaryWrapper(grpclib_stub.DomainList, client, server_url)
        self.EnvironmentCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.EnvironmentCreate, client, server_url)
        self.EnvironmentDelete = modal.client.UnaryUnaryWrapper(grpclib_stub.EnvironmentDelete, client, server_url)
        self.EnvironmentGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.EnvironmentGetOrCreate, client, server_url)
        self.EnvironmentList = modal.client.UnaryUnaryWrapper(grpclib_stub.EnvironmentList, client, server_url)
        self.EnvironmentUpdate = modal.client.UnaryUnaryWrapper(grpclib_stub.EnvironmentUpdate, client, server_url)
        self.FunctionAsyncInvoke = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionAsyncInvoke, client, server_url)
        self.FunctionBindParams = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionBindParams, client, server_url)
        self.FunctionCallCancel = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionCallCancel, client, server_url)
        self.FunctionCallGetDataIn = modal.client.UnaryStreamWrapper(grpclib_stub.FunctionCallGetDataIn, client, server_url)
        self.FunctionCallGetDataOut = modal.client.UnaryStreamWrapper(grpclib_stub.FunctionCallGetDataOut, client, server_url)
        self.FunctionCallList = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionCallList, client, server_url)
        self.FunctionCallPutDataOut = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionCallPutDataOut, client, server_url)
        self.FunctionCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionCreate, client, server_url)
        self.FunctionGet = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionGet, client, server_url)
        self.FunctionGetCallGraph = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionGetCallGraph, client, server_url)
        self.FunctionGetCurrentStats = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionGetCurrentStats, client, server_url)
        self.FunctionGetDynamicConcurrency = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionGetDynamicConcurrency, client, server_url)
        self.FunctionGetInputs = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionGetInputs, client, server_url)
        self.FunctionGetOutputs = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionGetOutputs, client, server_url)
        self.FunctionGetSerialized = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionGetSerialized, client, server_url)
        self.FunctionMap = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionMap, client, server_url)
        self.FunctionPrecreate = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionPrecreate, client, server_url)
        self.FunctionPutInputs = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionPutInputs, client, server_url)
        self.FunctionPutOutputs = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionPutOutputs, client, server_url)
        self.FunctionRetryInputs = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionRetryInputs, client, server_url)
        self.FunctionStartPtyShell = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionStartPtyShell, client, server_url)
        self.FunctionUpdateSchedulingParams = modal.client.UnaryUnaryWrapper(grpclib_stub.FunctionUpdateSchedulingParams, client, server_url)
        self.ImageFromId = modal.client.UnaryUnaryWrapper(grpclib_stub.ImageFromId, client, server_url)
        self.ImageGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.ImageGetOrCreate, client, server_url)
        self.ImageJoinStreaming = modal.client.UnaryStreamWrapper(grpclib_stub.ImageJoinStreaming, client, server_url)
        self.MountGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.MountGetOrCreate, client, server_url)
        self.MountPutFile = modal.client.UnaryUnaryWrapper(grpclib_stub.MountPutFile, client, server_url)
        self.NotebookKernelPublishResults = modal.client.UnaryUnaryWrapper(grpclib_stub.NotebookKernelPublishResults, client, server_url)
        self.ProxyAddIp = modal.client.UnaryUnaryWrapper(grpclib_stub.ProxyAddIp, client, server_url)
        self.ProxyCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.ProxyCreate, client, server_url)
        self.ProxyDelete = modal.client.UnaryUnaryWrapper(grpclib_stub.ProxyDelete, client, server_url)
        self.ProxyGet = modal.client.UnaryUnaryWrapper(grpclib_stub.ProxyGet, client, server_url)
        self.ProxyGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.ProxyGetOrCreate, client, server_url)
        self.ProxyList = modal.client.UnaryUnaryWrapper(grpclib_stub.ProxyList, client, server_url)
        self.ProxyRemoveIp = modal.client.UnaryUnaryWrapper(grpclib_stub.ProxyRemoveIp, client, server_url)
        self.QueueClear = modal.client.UnaryUnaryWrapper(grpclib_stub.QueueClear, client, server_url)
        self.QueueDelete = modal.client.UnaryUnaryWrapper(grpclib_stub.QueueDelete, client, server_url)
        self.QueueGet = modal.client.UnaryUnaryWrapper(grpclib_stub.QueueGet, client, server_url)
        self.QueueGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.QueueGetOrCreate, client, server_url)
        self.QueueHeartbeat = modal.client.UnaryUnaryWrapper(grpclib_stub.QueueHeartbeat, client, server_url)
        self.QueueLen = modal.client.UnaryUnaryWrapper(grpclib_stub.QueueLen, client, server_url)
        self.QueueList = modal.client.UnaryUnaryWrapper(grpclib_stub.QueueList, client, server_url)
        self.QueueNextItems = modal.client.UnaryUnaryWrapper(grpclib_stub.QueueNextItems, client, server_url)
        self.QueuePut = modal.client.UnaryUnaryWrapper(grpclib_stub.QueuePut, client, server_url)
        self.SandboxCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxCreate, client, server_url)
        self.SandboxGetLogs = modal.client.UnaryStreamWrapper(grpclib_stub.SandboxGetLogs, client, server_url)
        self.SandboxGetResourceUsage = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxGetResourceUsage, client, server_url)
        self.SandboxGetTaskId = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxGetTaskId, client, server_url)
        self.SandboxGetTunnels = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxGetTunnels, client, server_url)
        self.SandboxList = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxList, client, server_url)
        self.SandboxRestore = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxRestore, client, server_url)
        self.SandboxSnapshot = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxSnapshot, client, server_url)
        self.SandboxSnapshotFs = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxSnapshotFs, client, server_url)
        self.SandboxSnapshotGet = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxSnapshotGet, client, server_url)
        self.SandboxSnapshotWait = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxSnapshotWait, client, server_url)
        self.SandboxStdinWrite = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxStdinWrite, client, server_url)
        self.SandboxTagsSet = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxTagsSet, client, server_url)
        self.SandboxTerminate = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxTerminate, client, server_url)
        self.SandboxWait = modal.client.UnaryUnaryWrapper(grpclib_stub.SandboxWait, client, server_url)
        self.SecretDelete = modal.client.UnaryUnaryWrapper(grpclib_stub.SecretDelete, client, server_url)
        self.SecretGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.SecretGetOrCreate, client, server_url)
        self.SecretList = modal.client.UnaryUnaryWrapper(grpclib_stub.SecretList, client, server_url)
        self.SharedVolumeDelete = modal.client.UnaryUnaryWrapper(grpclib_stub.SharedVolumeDelete, client, server_url)
        self.SharedVolumeGetFile = modal.client.UnaryUnaryWrapper(grpclib_stub.SharedVolumeGetFile, client, server_url)
        self.SharedVolumeGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.SharedVolumeGetOrCreate, client, server_url)
        self.SharedVolumeHeartbeat = modal.client.UnaryUnaryWrapper(grpclib_stub.SharedVolumeHeartbeat, client, server_url)
        self.SharedVolumeList = modal.client.UnaryUnaryWrapper(grpclib_stub.SharedVolumeList, client, server_url)
        self.SharedVolumeListFiles = modal.client.UnaryUnaryWrapper(grpclib_stub.SharedVolumeListFiles, client, server_url)
        self.SharedVolumeListFilesStream = modal.client.UnaryStreamWrapper(grpclib_stub.SharedVolumeListFilesStream, client, server_url)
        self.SharedVolumePutFile = modal.client.UnaryUnaryWrapper(grpclib_stub.SharedVolumePutFile, client, server_url)
        self.SharedVolumeRemoveFile = modal.client.UnaryUnaryWrapper(grpclib_stub.SharedVolumeRemoveFile, client, server_url)
        self.TaskClusterHello = modal.client.UnaryUnaryWrapper(grpclib_stub.TaskClusterHello, client, server_url)
        self.TaskCurrentInputs = modal.client.UnaryUnaryWrapper(grpclib_stub.TaskCurrentInputs, client, server_url)
        self.TaskList = modal.client.UnaryUnaryWrapper(grpclib_stub.TaskList, client, server_url)
        self.TaskResult = modal.client.UnaryUnaryWrapper(grpclib_stub.TaskResult, client, server_url)
        self.TokenFlowCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.TokenFlowCreate, client, server_url)
        self.TokenFlowWait = modal.client.UnaryUnaryWrapper(grpclib_stub.TokenFlowWait, client, server_url)
        self.TunnelStart = modal.client.UnaryUnaryWrapper(grpclib_stub.TunnelStart, client, server_url)
        self.TunnelStop = modal.client.UnaryUnaryWrapper(grpclib_stub.TunnelStop, client, server_url)
        self.VolumeCommit = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeCommit, client, server_url)
        self.VolumeCopyFiles = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeCopyFiles, client, server_url)
        self.VolumeCopyFiles2 = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeCopyFiles2, client, server_url)
        self.VolumeDelete = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeDelete, client, server_url)
        self.VolumeGetFile = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeGetFile, client, server_url)
        self.VolumeGetFile2 = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeGetFile2, client, server_url)
        self.VolumeGetOrCreate = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeGetOrCreate, client, server_url)
        self.VolumeHeartbeat = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeHeartbeat, client, server_url)
        self.VolumeList = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeList, client, server_url)
        self.VolumeListFiles = modal.client.UnaryStreamWrapper(grpclib_stub.VolumeListFiles, client, server_url)
        self.VolumeListFiles2 = modal.client.UnaryStreamWrapper(grpclib_stub.VolumeListFiles2, client, server_url)
        self.VolumePutFiles = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumePutFiles, client, server_url)
        self.VolumePutFiles2 = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumePutFiles2, client, server_url)
        self.VolumeReload = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeReload, client, server_url)
        self.VolumeRemoveFile = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeRemoveFile, client, server_url)
        self.VolumeRemoveFile2 = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeRemoveFile2, client, server_url)
        self.VolumeRename = modal.client.UnaryUnaryWrapper(grpclib_stub.VolumeRename, client, server_url)
        self.WorkspaceNameLookup = modal.client.UnaryUnaryWrapper(grpclib_stub.WorkspaceNameLookup, client, server_url)
