"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import abc
import collections.abc
import google.protobuf.empty_pb2
import grpc
import modal_proto.api_pb2

class ModalClientStub:
    def __init__(self, channel: grpc.Channel) -> None: ...
    AppClientDisconnect: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppClientDisconnectRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """Apps"""
    AppCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppCreateRequest,
        modal_proto.api_pb2.AppCreateResponse,
    ]
    AppDeploy: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppDeployRequest,
        modal_proto.api_pb2.AppDeployResponse,
    ]
    AppDeploymentHistory: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppDeploymentHistoryRequest,
        modal_proto.api_pb2.AppDeploymentHistoryResponse,
    ]
    AppGetByDeploymentName: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppGetByDeploymentNameRequest,
        modal_proto.api_pb2.AppGetByDeploymentNameResponse,
    ]
    AppGetLayout: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppGetLayoutRequest,
        modal_proto.api_pb2.AppGetLayoutResponse,
    ]
    AppGetLogs: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.AppGetLogsRequest,
        modal_proto.api_pb2.TaskLogsBatch,
    ]
    AppGetObjects: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppGetObjectsRequest,
        modal_proto.api_pb2.AppGetObjectsResponse,
    ]
    AppGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppGetOrCreateRequest,
        modal_proto.api_pb2.AppGetOrCreateResponse,
    ]
    AppHeartbeat: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppHeartbeatRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    AppList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppListRequest,
        modal_proto.api_pb2.AppListResponse,
    ]
    AppLookup: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppLookupRequest,
        modal_proto.api_pb2.AppLookupResponse,
    ]
    AppPublish: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppPublishRequest,
        modal_proto.api_pb2.AppPublishResponse,
    ]
    AppRollback: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppRollbackRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    AppSetObjects: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppSetObjectsRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    AppStop: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AppStopRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    AttemptAwait: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AttemptAwaitRequest,
        modal_proto.api_pb2.AttemptAwaitResponse,
    ]
    """Input Plane
    These RPCs are experimental, not deployed to production, and can be changed / removed
    without needing to worry about backwards compatibility.
    """
    AttemptRetry: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AttemptRetryRequest,
        modal_proto.api_pb2.AttemptRetryResponse,
    ]
    AttemptStart: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.AttemptStartRequest,
        modal_proto.api_pb2.AttemptStartResponse,
    ]
    BlobCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.BlobCreateRequest,
        modal_proto.api_pb2.BlobCreateResponse,
    ]
    """Blobs"""
    BlobGet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.BlobGetRequest,
        modal_proto.api_pb2.BlobGetResponse,
    ]
    ClassCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ClassCreateRequest,
        modal_proto.api_pb2.ClassCreateResponse,
    ]
    """Classes"""
    ClassGet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ClassGetRequest,
        modal_proto.api_pb2.ClassGetResponse,
    ]
    ClientHello: grpc.UnaryUnaryMultiCallable[
        google.protobuf.empty_pb2.Empty,
        modal_proto.api_pb2.ClientHelloResponse,
    ]
    """Clients"""
    ClusterGet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ClusterGetRequest,
        modal_proto.api_pb2.ClusterGetResponse,
    ]
    """Clusters"""
    ClusterList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ClusterListRequest,
        modal_proto.api_pb2.ClusterListResponse,
    ]
    ContainerCheckpoint: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ContainerCheckpointRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """Container"""
    ContainerExec: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ContainerExecRequest,
        modal_proto.api_pb2.ContainerExecResponse,
    ]
    ContainerExecGetOutput: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.ContainerExecGetOutputRequest,
        modal_proto.api_pb2.RuntimeOutputBatch,
    ]
    ContainerExecPutInput: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ContainerExecPutInputRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    ContainerExecWait: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ContainerExecWaitRequest,
        modal_proto.api_pb2.ContainerExecWaitResponse,
    ]
    ContainerFilesystemExec: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ContainerFilesystemExecRequest,
        modal_proto.api_pb2.ContainerFilesystemExecResponse,
    ]
    ContainerFilesystemExecGetOutput: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.ContainerFilesystemExecGetOutputRequest,
        modal_proto.api_pb2.FilesystemRuntimeOutputBatch,
    ]
    ContainerHeartbeat: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ContainerHeartbeatRequest,
        modal_proto.api_pb2.ContainerHeartbeatResponse,
    ]
    ContainerHello: grpc.UnaryUnaryMultiCallable[
        google.protobuf.empty_pb2.Empty,
        google.protobuf.empty_pb2.Empty,
    ]
    ContainerLog: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ContainerLogRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    ContainerStop: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ContainerStopRequest,
        modal_proto.api_pb2.ContainerStopResponse,
    ]
    DictClear: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictClearRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """Dicts"""
    DictContains: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictContainsRequest,
        modal_proto.api_pb2.DictContainsResponse,
    ]
    DictContents: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.DictContentsRequest,
        modal_proto.api_pb2.DictEntry,
    ]
    DictDelete: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictDeleteRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    DictGet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictGetRequest,
        modal_proto.api_pb2.DictGetResponse,
    ]
    DictGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictGetOrCreateRequest,
        modal_proto.api_pb2.DictGetOrCreateResponse,
    ]
    DictHeartbeat: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictHeartbeatRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    DictLen: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictLenRequest,
        modal_proto.api_pb2.DictLenResponse,
    ]
    DictList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictListRequest,
        modal_proto.api_pb2.DictListResponse,
    ]
    DictPop: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictPopRequest,
        modal_proto.api_pb2.DictPopResponse,
    ]
    DictUpdate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DictUpdateRequest,
        modal_proto.api_pb2.DictUpdateResponse,
    ]
    DomainCertificateVerify: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DomainCertificateVerifyRequest,
        modal_proto.api_pb2.DomainCertificateVerifyResponse,
    ]
    """Domains"""
    DomainCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DomainCreateRequest,
        modal_proto.api_pb2.DomainCreateResponse,
    ]
    DomainList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.DomainListRequest,
        modal_proto.api_pb2.DomainListResponse,
    ]
    EnvironmentCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.EnvironmentCreateRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """Environments"""
    EnvironmentDelete: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.EnvironmentDeleteRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    EnvironmentGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.EnvironmentGetOrCreateRequest,
        modal_proto.api_pb2.EnvironmentGetOrCreateResponse,
    ]
    EnvironmentList: grpc.UnaryUnaryMultiCallable[
        google.protobuf.empty_pb2.Empty,
        modal_proto.api_pb2.EnvironmentListResponse,
    ]
    EnvironmentUpdate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.EnvironmentUpdateRequest,
        modal_proto.api_pb2.EnvironmentListItem,
    ]
    FunctionAsyncInvoke: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionAsyncInvokeRequest,
        modal_proto.api_pb2.FunctionAsyncInvokeResponse,
    ]
    """Functions"""
    FunctionBindParams: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionBindParamsRequest,
        modal_proto.api_pb2.FunctionBindParamsResponse,
    ]
    FunctionCallCancel: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionCallCancelRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    FunctionCallGetDataIn: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.FunctionCallGetDataRequest,
        modal_proto.api_pb2.DataChunk,
    ]
    FunctionCallGetDataOut: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.FunctionCallGetDataRequest,
        modal_proto.api_pb2.DataChunk,
    ]
    FunctionCallList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionCallListRequest,
        modal_proto.api_pb2.FunctionCallListResponse,
    ]
    FunctionCallPutDataOut: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionCallPutDataRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    FunctionCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionCreateRequest,
        modal_proto.api_pb2.FunctionCreateResponse,
    ]
    FunctionGet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionGetRequest,
        modal_proto.api_pb2.FunctionGetResponse,
    ]
    FunctionGetCallGraph: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionGetCallGraphRequest,
        modal_proto.api_pb2.FunctionGetCallGraphResponse,
    ]
    FunctionGetCurrentStats: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionGetCurrentStatsRequest,
        modal_proto.api_pb2.FunctionStats,
    ]
    FunctionGetDynamicConcurrency: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionGetDynamicConcurrencyRequest,
        modal_proto.api_pb2.FunctionGetDynamicConcurrencyResponse,
    ]
    FunctionGetInputs: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionGetInputsRequest,
        modal_proto.api_pb2.FunctionGetInputsResponse,
    ]
    """For containers to request next call"""
    FunctionGetOutputs: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionGetOutputsRequest,
        modal_proto.api_pb2.FunctionGetOutputsResponse,
    ]
    """Returns the next result(s) for an entire function call (FunctionMap)"""
    FunctionGetSerialized: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionGetSerializedRequest,
        modal_proto.api_pb2.FunctionGetSerializedResponse,
    ]
    FunctionMap: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionMapRequest,
        modal_proto.api_pb2.FunctionMapResponse,
    ]
    FunctionPrecreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionPrecreateRequest,
        modal_proto.api_pb2.FunctionPrecreateResponse,
    ]
    FunctionPutInputs: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionPutInputsRequest,
        modal_proto.api_pb2.FunctionPutInputsResponse,
    ]
    FunctionPutOutputs: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionPutOutputsRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """For containers to return result"""
    FunctionRetryInputs: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionRetryInputsRequest,
        modal_proto.api_pb2.FunctionRetryInputsResponse,
    ]
    FunctionStartPtyShell: grpc.UnaryUnaryMultiCallable[
        google.protobuf.empty_pb2.Empty,
        google.protobuf.empty_pb2.Empty,
    ]
    FunctionUpdateSchedulingParams: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.FunctionUpdateSchedulingParamsRequest,
        modal_proto.api_pb2.FunctionUpdateSchedulingParamsResponse,
    ]
    ImageFromId: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ImageFromIdRequest,
        modal_proto.api_pb2.ImageFromIdResponse,
    ]
    """Images"""
    ImageGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ImageGetOrCreateRequest,
        modal_proto.api_pb2.ImageGetOrCreateResponse,
    ]
    ImageJoinStreaming: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.ImageJoinStreamingRequest,
        modal_proto.api_pb2.ImageJoinStreamingResponse,
    ]
    MountGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.MountGetOrCreateRequest,
        modal_proto.api_pb2.MountGetOrCreateResponse,
    ]
    """Mounts"""
    MountPutFile: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.MountPutFileRequest,
        modal_proto.api_pb2.MountPutFileResponse,
    ]
    NotebookKernelPublishResults: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.NotebookKernelPublishResultsRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """Notebooks"""
    ProxyAddIp: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ProxyAddIpRequest,
        modal_proto.api_pb2.ProxyAddIpResponse,
    ]
    """Proxies"""
    ProxyCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ProxyCreateRequest,
        modal_proto.api_pb2.ProxyCreateResponse,
    ]
    ProxyDelete: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ProxyDeleteRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    ProxyGet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ProxyGetRequest,
        modal_proto.api_pb2.ProxyGetResponse,
    ]
    ProxyGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ProxyGetOrCreateRequest,
        modal_proto.api_pb2.ProxyGetOrCreateResponse,
    ]
    ProxyList: grpc.UnaryUnaryMultiCallable[
        google.protobuf.empty_pb2.Empty,
        modal_proto.api_pb2.ProxyListResponse,
    ]
    ProxyRemoveIp: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.ProxyRemoveIpRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    QueueClear: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueueClearRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """Queues"""
    QueueDelete: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueueDeleteRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    QueueGet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueueGetRequest,
        modal_proto.api_pb2.QueueGetResponse,
    ]
    QueueGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueueGetOrCreateRequest,
        modal_proto.api_pb2.QueueGetOrCreateResponse,
    ]
    QueueHeartbeat: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueueHeartbeatRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    QueueLen: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueueLenRequest,
        modal_proto.api_pb2.QueueLenResponse,
    ]
    QueueList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueueListRequest,
        modal_proto.api_pb2.QueueListResponse,
    ]
    QueueNextItems: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueueNextItemsRequest,
        modal_proto.api_pb2.QueueNextItemsResponse,
    ]
    QueuePut: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.QueuePutRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    SandboxCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxCreateRequest,
        modal_proto.api_pb2.SandboxCreateResponse,
    ]
    """Sandboxes"""
    SandboxGetLogs: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.SandboxGetLogsRequest,
        modal_proto.api_pb2.TaskLogsBatch,
    ]
    SandboxGetResourceUsage: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxGetResourceUsageRequest,
        modal_proto.api_pb2.SandboxGetResourceUsageResponse,
    ]
    SandboxGetTaskId: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxGetTaskIdRequest,
        modal_proto.api_pb2.SandboxGetTaskIdResponse,
    ]
    """needed for modal container exec"""
    SandboxGetTunnels: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxGetTunnelsRequest,
        modal_proto.api_pb2.SandboxGetTunnelsResponse,
    ]
    SandboxList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxListRequest,
        modal_proto.api_pb2.SandboxListResponse,
    ]
    SandboxRestore: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxRestoreRequest,
        modal_proto.api_pb2.SandboxRestoreResponse,
    ]
    SandboxSnapshot: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxSnapshotRequest,
        modal_proto.api_pb2.SandboxSnapshotResponse,
    ]
    SandboxSnapshotFs: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxSnapshotFsRequest,
        modal_proto.api_pb2.SandboxSnapshotFsResponse,
    ]
    SandboxSnapshotGet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxSnapshotGetRequest,
        modal_proto.api_pb2.SandboxSnapshotGetResponse,
    ]
    SandboxSnapshotWait: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxSnapshotWaitRequest,
        modal_proto.api_pb2.SandboxSnapshotWaitResponse,
    ]
    SandboxStdinWrite: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxStdinWriteRequest,
        modal_proto.api_pb2.SandboxStdinWriteResponse,
    ]
    SandboxTagsSet: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxTagsSetRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    SandboxTerminate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxTerminateRequest,
        modal_proto.api_pb2.SandboxTerminateResponse,
    ]
    SandboxWait: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SandboxWaitRequest,
        modal_proto.api_pb2.SandboxWaitResponse,
    ]
    SecretDelete: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SecretDeleteRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """Secrets"""
    SecretGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SecretGetOrCreateRequest,
        modal_proto.api_pb2.SecretGetOrCreateResponse,
    ]
    SecretList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SecretListRequest,
        modal_proto.api_pb2.SecretListResponse,
    ]
    SharedVolumeDelete: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SharedVolumeDeleteRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    """SharedVolumes"""
    SharedVolumeGetFile: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SharedVolumeGetFileRequest,
        modal_proto.api_pb2.SharedVolumeGetFileResponse,
    ]
    SharedVolumeGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SharedVolumeGetOrCreateRequest,
        modal_proto.api_pb2.SharedVolumeGetOrCreateResponse,
    ]
    SharedVolumeHeartbeat: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SharedVolumeHeartbeatRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    SharedVolumeList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SharedVolumeListRequest,
        modal_proto.api_pb2.SharedVolumeListResponse,
    ]
    SharedVolumeListFiles: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SharedVolumeListFilesRequest,
        modal_proto.api_pb2.SharedVolumeListFilesResponse,
    ]
    SharedVolumeListFilesStream: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.SharedVolumeListFilesRequest,
        modal_proto.api_pb2.SharedVolumeListFilesResponse,
    ]
    SharedVolumePutFile: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SharedVolumePutFileRequest,
        modal_proto.api_pb2.SharedVolumePutFileResponse,
    ]
    SharedVolumeRemoveFile: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.SharedVolumeRemoveFileRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    TaskClusterHello: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.TaskClusterHelloRequest,
        modal_proto.api_pb2.TaskClusterHelloResponse,
    ]
    """Tasks"""
    TaskCurrentInputs: grpc.UnaryUnaryMultiCallable[
        google.protobuf.empty_pb2.Empty,
        modal_proto.api_pb2.TaskCurrentInputsResponse,
    ]
    TaskList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.TaskListRequest,
        modal_proto.api_pb2.TaskListResponse,
    ]
    TaskResult: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.TaskResultRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    TokenFlowCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.TokenFlowCreateRequest,
        modal_proto.api_pb2.TokenFlowCreateResponse,
    ]
    """Tokens (web auth flow)"""
    TokenFlowWait: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.TokenFlowWaitRequest,
        modal_proto.api_pb2.TokenFlowWaitResponse,
    ]
    TunnelStart: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.TunnelStartRequest,
        modal_proto.api_pb2.TunnelStartResponse,
    ]
    """Tunnels"""
    TunnelStop: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.TunnelStopRequest,
        modal_proto.api_pb2.TunnelStopResponse,
    ]
    VolumeCommit: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeCommitRequest,
        modal_proto.api_pb2.VolumeCommitResponse,
    ]
    """Volumes"""
    VolumeCopyFiles: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeCopyFilesRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    VolumeCopyFiles2: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeCopyFiles2Request,
        google.protobuf.empty_pb2.Empty,
    ]
    VolumeDelete: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeDeleteRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    VolumeGetFile: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeGetFileRequest,
        modal_proto.api_pb2.VolumeGetFileResponse,
    ]
    VolumeGetFile2: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeGetFile2Request,
        modal_proto.api_pb2.VolumeGetFile2Response,
    ]
    VolumeGetOrCreate: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeGetOrCreateRequest,
        modal_proto.api_pb2.VolumeGetOrCreateResponse,
    ]
    VolumeHeartbeat: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeHeartbeatRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    VolumeList: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeListRequest,
        modal_proto.api_pb2.VolumeListResponse,
    ]
    VolumeListFiles: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.VolumeListFilesRequest,
        modal_proto.api_pb2.VolumeListFilesResponse,
    ]
    VolumeListFiles2: grpc.UnaryStreamMultiCallable[
        modal_proto.api_pb2.VolumeListFiles2Request,
        modal_proto.api_pb2.VolumeListFiles2Response,
    ]
    VolumePutFiles: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumePutFilesRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    VolumePutFiles2: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumePutFiles2Request,
        modal_proto.api_pb2.VolumePutFiles2Response,
    ]
    VolumeReload: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeReloadRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    VolumeRemoveFile: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeRemoveFileRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    VolumeRemoveFile2: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeRemoveFile2Request,
        google.protobuf.empty_pb2.Empty,
    ]
    VolumeRename: grpc.UnaryUnaryMultiCallable[
        modal_proto.api_pb2.VolumeRenameRequest,
        google.protobuf.empty_pb2.Empty,
    ]
    WorkspaceNameLookup: grpc.UnaryUnaryMultiCallable[
        google.protobuf.empty_pb2.Empty,
        modal_proto.api_pb2.WorkspaceNameLookupResponse,
    ]
    """Workspaces"""

class ModalClientServicer(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def AppClientDisconnect(
        self,
        request: modal_proto.api_pb2.AppClientDisconnectRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """Apps"""
    @abc.abstractmethod
    def AppCreate(
        self,
        request: modal_proto.api_pb2.AppCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppCreateResponse: ...
    @abc.abstractmethod
    def AppDeploy(
        self,
        request: modal_proto.api_pb2.AppDeployRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppDeployResponse: ...
    @abc.abstractmethod
    def AppDeploymentHistory(
        self,
        request: modal_proto.api_pb2.AppDeploymentHistoryRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppDeploymentHistoryResponse: ...
    @abc.abstractmethod
    def AppGetByDeploymentName(
        self,
        request: modal_proto.api_pb2.AppGetByDeploymentNameRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppGetByDeploymentNameResponse: ...
    @abc.abstractmethod
    def AppGetLayout(
        self,
        request: modal_proto.api_pb2.AppGetLayoutRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppGetLayoutResponse: ...
    @abc.abstractmethod
    def AppGetLogs(
        self,
        request: modal_proto.api_pb2.AppGetLogsRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.TaskLogsBatch]: ...
    @abc.abstractmethod
    def AppGetObjects(
        self,
        request: modal_proto.api_pb2.AppGetObjectsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppGetObjectsResponse: ...
    @abc.abstractmethod
    def AppGetOrCreate(
        self,
        request: modal_proto.api_pb2.AppGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppGetOrCreateResponse: ...
    @abc.abstractmethod
    def AppHeartbeat(
        self,
        request: modal_proto.api_pb2.AppHeartbeatRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def AppList(
        self,
        request: modal_proto.api_pb2.AppListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppListResponse: ...
    @abc.abstractmethod
    def AppLookup(
        self,
        request: modal_proto.api_pb2.AppLookupRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppLookupResponse: ...
    @abc.abstractmethod
    def AppPublish(
        self,
        request: modal_proto.api_pb2.AppPublishRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AppPublishResponse: ...
    @abc.abstractmethod
    def AppRollback(
        self,
        request: modal_proto.api_pb2.AppRollbackRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def AppSetObjects(
        self,
        request: modal_proto.api_pb2.AppSetObjectsRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def AppStop(
        self,
        request: modal_proto.api_pb2.AppStopRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def AttemptAwait(
        self,
        request: modal_proto.api_pb2.AttemptAwaitRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AttemptAwaitResponse:
        """Input Plane
        These RPCs are experimental, not deployed to production, and can be changed / removed
        without needing to worry about backwards compatibility.
        """
    @abc.abstractmethod
    def AttemptRetry(
        self,
        request: modal_proto.api_pb2.AttemptRetryRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AttemptRetryResponse: ...
    @abc.abstractmethod
    def AttemptStart(
        self,
        request: modal_proto.api_pb2.AttemptStartRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.AttemptStartResponse: ...
    @abc.abstractmethod
    def BlobCreate(
        self,
        request: modal_proto.api_pb2.BlobCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.BlobCreateResponse:
        """Blobs"""
    @abc.abstractmethod
    def BlobGet(
        self,
        request: modal_proto.api_pb2.BlobGetRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.BlobGetResponse: ...
    @abc.abstractmethod
    def ClassCreate(
        self,
        request: modal_proto.api_pb2.ClassCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ClassCreateResponse:
        """Classes"""
    @abc.abstractmethod
    def ClassGet(
        self,
        request: modal_proto.api_pb2.ClassGetRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ClassGetResponse: ...
    @abc.abstractmethod
    def ClientHello(
        self,
        request: google.protobuf.empty_pb2.Empty,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ClientHelloResponse:
        """Clients"""
    @abc.abstractmethod
    def ClusterGet(
        self,
        request: modal_proto.api_pb2.ClusterGetRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ClusterGetResponse:
        """Clusters"""
    @abc.abstractmethod
    def ClusterList(
        self,
        request: modal_proto.api_pb2.ClusterListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ClusterListResponse: ...
    @abc.abstractmethod
    def ContainerCheckpoint(
        self,
        request: modal_proto.api_pb2.ContainerCheckpointRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """Container"""
    @abc.abstractmethod
    def ContainerExec(
        self,
        request: modal_proto.api_pb2.ContainerExecRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ContainerExecResponse: ...
    @abc.abstractmethod
    def ContainerExecGetOutput(
        self,
        request: modal_proto.api_pb2.ContainerExecGetOutputRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.RuntimeOutputBatch]: ...
    @abc.abstractmethod
    def ContainerExecPutInput(
        self,
        request: modal_proto.api_pb2.ContainerExecPutInputRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def ContainerExecWait(
        self,
        request: modal_proto.api_pb2.ContainerExecWaitRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ContainerExecWaitResponse: ...
    @abc.abstractmethod
    def ContainerFilesystemExec(
        self,
        request: modal_proto.api_pb2.ContainerFilesystemExecRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ContainerFilesystemExecResponse: ...
    @abc.abstractmethod
    def ContainerFilesystemExecGetOutput(
        self,
        request: modal_proto.api_pb2.ContainerFilesystemExecGetOutputRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.FilesystemRuntimeOutputBatch]: ...
    @abc.abstractmethod
    def ContainerHeartbeat(
        self,
        request: modal_proto.api_pb2.ContainerHeartbeatRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ContainerHeartbeatResponse: ...
    @abc.abstractmethod
    def ContainerHello(
        self,
        request: google.protobuf.empty_pb2.Empty,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def ContainerLog(
        self,
        request: modal_proto.api_pb2.ContainerLogRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def ContainerStop(
        self,
        request: modal_proto.api_pb2.ContainerStopRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ContainerStopResponse: ...
    @abc.abstractmethod
    def DictClear(
        self,
        request: modal_proto.api_pb2.DictClearRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """Dicts"""
    @abc.abstractmethod
    def DictContains(
        self,
        request: modal_proto.api_pb2.DictContainsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DictContainsResponse: ...
    @abc.abstractmethod
    def DictContents(
        self,
        request: modal_proto.api_pb2.DictContentsRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.DictEntry]: ...
    @abc.abstractmethod
    def DictDelete(
        self,
        request: modal_proto.api_pb2.DictDeleteRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def DictGet(
        self,
        request: modal_proto.api_pb2.DictGetRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DictGetResponse: ...
    @abc.abstractmethod
    def DictGetOrCreate(
        self,
        request: modal_proto.api_pb2.DictGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DictGetOrCreateResponse: ...
    @abc.abstractmethod
    def DictHeartbeat(
        self,
        request: modal_proto.api_pb2.DictHeartbeatRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def DictLen(
        self,
        request: modal_proto.api_pb2.DictLenRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DictLenResponse: ...
    @abc.abstractmethod
    def DictList(
        self,
        request: modal_proto.api_pb2.DictListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DictListResponse: ...
    @abc.abstractmethod
    def DictPop(
        self,
        request: modal_proto.api_pb2.DictPopRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DictPopResponse: ...
    @abc.abstractmethod
    def DictUpdate(
        self,
        request: modal_proto.api_pb2.DictUpdateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DictUpdateResponse: ...
    @abc.abstractmethod
    def DomainCertificateVerify(
        self,
        request: modal_proto.api_pb2.DomainCertificateVerifyRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DomainCertificateVerifyResponse:
        """Domains"""
    @abc.abstractmethod
    def DomainCreate(
        self,
        request: modal_proto.api_pb2.DomainCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DomainCreateResponse: ...
    @abc.abstractmethod
    def DomainList(
        self,
        request: modal_proto.api_pb2.DomainListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.DomainListResponse: ...
    @abc.abstractmethod
    def EnvironmentCreate(
        self,
        request: modal_proto.api_pb2.EnvironmentCreateRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """Environments"""
    @abc.abstractmethod
    def EnvironmentDelete(
        self,
        request: modal_proto.api_pb2.EnvironmentDeleteRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def EnvironmentGetOrCreate(
        self,
        request: modal_proto.api_pb2.EnvironmentGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.EnvironmentGetOrCreateResponse: ...
    @abc.abstractmethod
    def EnvironmentList(
        self,
        request: google.protobuf.empty_pb2.Empty,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.EnvironmentListResponse: ...
    @abc.abstractmethod
    def EnvironmentUpdate(
        self,
        request: modal_proto.api_pb2.EnvironmentUpdateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.EnvironmentListItem: ...
    @abc.abstractmethod
    def FunctionAsyncInvoke(
        self,
        request: modal_proto.api_pb2.FunctionAsyncInvokeRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionAsyncInvokeResponse:
        """Functions"""
    @abc.abstractmethod
    def FunctionBindParams(
        self,
        request: modal_proto.api_pb2.FunctionBindParamsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionBindParamsResponse: ...
    @abc.abstractmethod
    def FunctionCallCancel(
        self,
        request: modal_proto.api_pb2.FunctionCallCancelRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def FunctionCallGetDataIn(
        self,
        request: modal_proto.api_pb2.FunctionCallGetDataRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.DataChunk]: ...
    @abc.abstractmethod
    def FunctionCallGetDataOut(
        self,
        request: modal_proto.api_pb2.FunctionCallGetDataRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.DataChunk]: ...
    @abc.abstractmethod
    def FunctionCallList(
        self,
        request: modal_proto.api_pb2.FunctionCallListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionCallListResponse: ...
    @abc.abstractmethod
    def FunctionCallPutDataOut(
        self,
        request: modal_proto.api_pb2.FunctionCallPutDataRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def FunctionCreate(
        self,
        request: modal_proto.api_pb2.FunctionCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionCreateResponse: ...
    @abc.abstractmethod
    def FunctionGet(
        self,
        request: modal_proto.api_pb2.FunctionGetRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionGetResponse: ...
    @abc.abstractmethod
    def FunctionGetCallGraph(
        self,
        request: modal_proto.api_pb2.FunctionGetCallGraphRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionGetCallGraphResponse: ...
    @abc.abstractmethod
    def FunctionGetCurrentStats(
        self,
        request: modal_proto.api_pb2.FunctionGetCurrentStatsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionStats: ...
    @abc.abstractmethod
    def FunctionGetDynamicConcurrency(
        self,
        request: modal_proto.api_pb2.FunctionGetDynamicConcurrencyRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionGetDynamicConcurrencyResponse: ...
    @abc.abstractmethod
    def FunctionGetInputs(
        self,
        request: modal_proto.api_pb2.FunctionGetInputsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionGetInputsResponse:
        """For containers to request next call"""
    @abc.abstractmethod
    def FunctionGetOutputs(
        self,
        request: modal_proto.api_pb2.FunctionGetOutputsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionGetOutputsResponse:
        """Returns the next result(s) for an entire function call (FunctionMap)"""
    @abc.abstractmethod
    def FunctionGetSerialized(
        self,
        request: modal_proto.api_pb2.FunctionGetSerializedRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionGetSerializedResponse: ...
    @abc.abstractmethod
    def FunctionMap(
        self,
        request: modal_proto.api_pb2.FunctionMapRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionMapResponse: ...
    @abc.abstractmethod
    def FunctionPrecreate(
        self,
        request: modal_proto.api_pb2.FunctionPrecreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionPrecreateResponse: ...
    @abc.abstractmethod
    def FunctionPutInputs(
        self,
        request: modal_proto.api_pb2.FunctionPutInputsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionPutInputsResponse: ...
    @abc.abstractmethod
    def FunctionPutOutputs(
        self,
        request: modal_proto.api_pb2.FunctionPutOutputsRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """For containers to return result"""
    @abc.abstractmethod
    def FunctionRetryInputs(
        self,
        request: modal_proto.api_pb2.FunctionRetryInputsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionRetryInputsResponse: ...
    @abc.abstractmethod
    def FunctionStartPtyShell(
        self,
        request: google.protobuf.empty_pb2.Empty,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def FunctionUpdateSchedulingParams(
        self,
        request: modal_proto.api_pb2.FunctionUpdateSchedulingParamsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.FunctionUpdateSchedulingParamsResponse: ...
    @abc.abstractmethod
    def ImageFromId(
        self,
        request: modal_proto.api_pb2.ImageFromIdRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ImageFromIdResponse:
        """Images"""
    @abc.abstractmethod
    def ImageGetOrCreate(
        self,
        request: modal_proto.api_pb2.ImageGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ImageGetOrCreateResponse: ...
    @abc.abstractmethod
    def ImageJoinStreaming(
        self,
        request: modal_proto.api_pb2.ImageJoinStreamingRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.ImageJoinStreamingResponse]: ...
    @abc.abstractmethod
    def MountGetOrCreate(
        self,
        request: modal_proto.api_pb2.MountGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.MountGetOrCreateResponse:
        """Mounts"""
    @abc.abstractmethod
    def MountPutFile(
        self,
        request: modal_proto.api_pb2.MountPutFileRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.MountPutFileResponse: ...
    @abc.abstractmethod
    def NotebookKernelPublishResults(
        self,
        request: modal_proto.api_pb2.NotebookKernelPublishResultsRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """Notebooks"""
    @abc.abstractmethod
    def ProxyAddIp(
        self,
        request: modal_proto.api_pb2.ProxyAddIpRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ProxyAddIpResponse:
        """Proxies"""
    @abc.abstractmethod
    def ProxyCreate(
        self,
        request: modal_proto.api_pb2.ProxyCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ProxyCreateResponse: ...
    @abc.abstractmethod
    def ProxyDelete(
        self,
        request: modal_proto.api_pb2.ProxyDeleteRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def ProxyGet(
        self,
        request: modal_proto.api_pb2.ProxyGetRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ProxyGetResponse: ...
    @abc.abstractmethod
    def ProxyGetOrCreate(
        self,
        request: modal_proto.api_pb2.ProxyGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ProxyGetOrCreateResponse: ...
    @abc.abstractmethod
    def ProxyList(
        self,
        request: google.protobuf.empty_pb2.Empty,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.ProxyListResponse: ...
    @abc.abstractmethod
    def ProxyRemoveIp(
        self,
        request: modal_proto.api_pb2.ProxyRemoveIpRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def QueueClear(
        self,
        request: modal_proto.api_pb2.QueueClearRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """Queues"""
    @abc.abstractmethod
    def QueueDelete(
        self,
        request: modal_proto.api_pb2.QueueDeleteRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def QueueGet(
        self,
        request: modal_proto.api_pb2.QueueGetRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.QueueGetResponse: ...
    @abc.abstractmethod
    def QueueGetOrCreate(
        self,
        request: modal_proto.api_pb2.QueueGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.QueueGetOrCreateResponse: ...
    @abc.abstractmethod
    def QueueHeartbeat(
        self,
        request: modal_proto.api_pb2.QueueHeartbeatRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def QueueLen(
        self,
        request: modal_proto.api_pb2.QueueLenRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.QueueLenResponse: ...
    @abc.abstractmethod
    def QueueList(
        self,
        request: modal_proto.api_pb2.QueueListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.QueueListResponse: ...
    @abc.abstractmethod
    def QueueNextItems(
        self,
        request: modal_proto.api_pb2.QueueNextItemsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.QueueNextItemsResponse: ...
    @abc.abstractmethod
    def QueuePut(
        self,
        request: modal_proto.api_pb2.QueuePutRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def SandboxCreate(
        self,
        request: modal_proto.api_pb2.SandboxCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxCreateResponse:
        """Sandboxes"""
    @abc.abstractmethod
    def SandboxGetLogs(
        self,
        request: modal_proto.api_pb2.SandboxGetLogsRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.TaskLogsBatch]: ...
    @abc.abstractmethod
    def SandboxGetResourceUsage(
        self,
        request: modal_proto.api_pb2.SandboxGetResourceUsageRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxGetResourceUsageResponse: ...
    @abc.abstractmethod
    def SandboxGetTaskId(
        self,
        request: modal_proto.api_pb2.SandboxGetTaskIdRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxGetTaskIdResponse:
        """needed for modal container exec"""
    @abc.abstractmethod
    def SandboxGetTunnels(
        self,
        request: modal_proto.api_pb2.SandboxGetTunnelsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxGetTunnelsResponse: ...
    @abc.abstractmethod
    def SandboxList(
        self,
        request: modal_proto.api_pb2.SandboxListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxListResponse: ...
    @abc.abstractmethod
    def SandboxRestore(
        self,
        request: modal_proto.api_pb2.SandboxRestoreRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxRestoreResponse: ...
    @abc.abstractmethod
    def SandboxSnapshot(
        self,
        request: modal_proto.api_pb2.SandboxSnapshotRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxSnapshotResponse: ...
    @abc.abstractmethod
    def SandboxSnapshotFs(
        self,
        request: modal_proto.api_pb2.SandboxSnapshotFsRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxSnapshotFsResponse: ...
    @abc.abstractmethod
    def SandboxSnapshotGet(
        self,
        request: modal_proto.api_pb2.SandboxSnapshotGetRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxSnapshotGetResponse: ...
    @abc.abstractmethod
    def SandboxSnapshotWait(
        self,
        request: modal_proto.api_pb2.SandboxSnapshotWaitRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxSnapshotWaitResponse: ...
    @abc.abstractmethod
    def SandboxStdinWrite(
        self,
        request: modal_proto.api_pb2.SandboxStdinWriteRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxStdinWriteResponse: ...
    @abc.abstractmethod
    def SandboxTagsSet(
        self,
        request: modal_proto.api_pb2.SandboxTagsSetRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def SandboxTerminate(
        self,
        request: modal_proto.api_pb2.SandboxTerminateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxTerminateResponse: ...
    @abc.abstractmethod
    def SandboxWait(
        self,
        request: modal_proto.api_pb2.SandboxWaitRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SandboxWaitResponse: ...
    @abc.abstractmethod
    def SecretDelete(
        self,
        request: modal_proto.api_pb2.SecretDeleteRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """Secrets"""
    @abc.abstractmethod
    def SecretGetOrCreate(
        self,
        request: modal_proto.api_pb2.SecretGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SecretGetOrCreateResponse: ...
    @abc.abstractmethod
    def SecretList(
        self,
        request: modal_proto.api_pb2.SecretListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SecretListResponse: ...
    @abc.abstractmethod
    def SharedVolumeDelete(
        self,
        request: modal_proto.api_pb2.SharedVolumeDeleteRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty:
        """SharedVolumes"""
    @abc.abstractmethod
    def SharedVolumeGetFile(
        self,
        request: modal_proto.api_pb2.SharedVolumeGetFileRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SharedVolumeGetFileResponse: ...
    @abc.abstractmethod
    def SharedVolumeGetOrCreate(
        self,
        request: modal_proto.api_pb2.SharedVolumeGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SharedVolumeGetOrCreateResponse: ...
    @abc.abstractmethod
    def SharedVolumeHeartbeat(
        self,
        request: modal_proto.api_pb2.SharedVolumeHeartbeatRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def SharedVolumeList(
        self,
        request: modal_proto.api_pb2.SharedVolumeListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SharedVolumeListResponse: ...
    @abc.abstractmethod
    def SharedVolumeListFiles(
        self,
        request: modal_proto.api_pb2.SharedVolumeListFilesRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SharedVolumeListFilesResponse: ...
    @abc.abstractmethod
    def SharedVolumeListFilesStream(
        self,
        request: modal_proto.api_pb2.SharedVolumeListFilesRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.SharedVolumeListFilesResponse]: ...
    @abc.abstractmethod
    def SharedVolumePutFile(
        self,
        request: modal_proto.api_pb2.SharedVolumePutFileRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.SharedVolumePutFileResponse: ...
    @abc.abstractmethod
    def SharedVolumeRemoveFile(
        self,
        request: modal_proto.api_pb2.SharedVolumeRemoveFileRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def TaskClusterHello(
        self,
        request: modal_proto.api_pb2.TaskClusterHelloRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.TaskClusterHelloResponse:
        """Tasks"""
    @abc.abstractmethod
    def TaskCurrentInputs(
        self,
        request: google.protobuf.empty_pb2.Empty,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.TaskCurrentInputsResponse: ...
    @abc.abstractmethod
    def TaskList(
        self,
        request: modal_proto.api_pb2.TaskListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.TaskListResponse: ...
    @abc.abstractmethod
    def TaskResult(
        self,
        request: modal_proto.api_pb2.TaskResultRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def TokenFlowCreate(
        self,
        request: modal_proto.api_pb2.TokenFlowCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.TokenFlowCreateResponse:
        """Tokens (web auth flow)"""
    @abc.abstractmethod
    def TokenFlowWait(
        self,
        request: modal_proto.api_pb2.TokenFlowWaitRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.TokenFlowWaitResponse: ...
    @abc.abstractmethod
    def TunnelStart(
        self,
        request: modal_proto.api_pb2.TunnelStartRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.TunnelStartResponse:
        """Tunnels"""
    @abc.abstractmethod
    def TunnelStop(
        self,
        request: modal_proto.api_pb2.TunnelStopRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.TunnelStopResponse: ...
    @abc.abstractmethod
    def VolumeCommit(
        self,
        request: modal_proto.api_pb2.VolumeCommitRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.VolumeCommitResponse:
        """Volumes"""
    @abc.abstractmethod
    def VolumeCopyFiles(
        self,
        request: modal_proto.api_pb2.VolumeCopyFilesRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def VolumeCopyFiles2(
        self,
        request: modal_proto.api_pb2.VolumeCopyFiles2Request,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def VolumeDelete(
        self,
        request: modal_proto.api_pb2.VolumeDeleteRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def VolumeGetFile(
        self,
        request: modal_proto.api_pb2.VolumeGetFileRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.VolumeGetFileResponse: ...
    @abc.abstractmethod
    def VolumeGetFile2(
        self,
        request: modal_proto.api_pb2.VolumeGetFile2Request,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.VolumeGetFile2Response: ...
    @abc.abstractmethod
    def VolumeGetOrCreate(
        self,
        request: modal_proto.api_pb2.VolumeGetOrCreateRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.VolumeGetOrCreateResponse: ...
    @abc.abstractmethod
    def VolumeHeartbeat(
        self,
        request: modal_proto.api_pb2.VolumeHeartbeatRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def VolumeList(
        self,
        request: modal_proto.api_pb2.VolumeListRequest,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.VolumeListResponse: ...
    @abc.abstractmethod
    def VolumeListFiles(
        self,
        request: modal_proto.api_pb2.VolumeListFilesRequest,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.VolumeListFilesResponse]: ...
    @abc.abstractmethod
    def VolumeListFiles2(
        self,
        request: modal_proto.api_pb2.VolumeListFiles2Request,
        context: grpc.ServicerContext,
    ) -> collections.abc.Iterator[modal_proto.api_pb2.VolumeListFiles2Response]: ...
    @abc.abstractmethod
    def VolumePutFiles(
        self,
        request: modal_proto.api_pb2.VolumePutFilesRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def VolumePutFiles2(
        self,
        request: modal_proto.api_pb2.VolumePutFiles2Request,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.VolumePutFiles2Response: ...
    @abc.abstractmethod
    def VolumeReload(
        self,
        request: modal_proto.api_pb2.VolumeReloadRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def VolumeRemoveFile(
        self,
        request: modal_proto.api_pb2.VolumeRemoveFileRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def VolumeRemoveFile2(
        self,
        request: modal_proto.api_pb2.VolumeRemoveFile2Request,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def VolumeRename(
        self,
        request: modal_proto.api_pb2.VolumeRenameRequest,
        context: grpc.ServicerContext,
    ) -> google.protobuf.empty_pb2.Empty: ...
    @abc.abstractmethod
    def WorkspaceNameLookup(
        self,
        request: google.protobuf.empty_pb2.Empty,
        context: grpc.ServicerContext,
    ) -> modal_proto.api_pb2.WorkspaceNameLookupResponse:
        """Workspaces"""

def add_ModalClientServicer_to_server(servicer: ModalClientServicer, server: grpc.Server) -> None: ...
