"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import google.protobuf.struct_pb2
import google.protobuf.wrappers_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _AppDeployVisibility:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AppDeployVisibilityEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AppDeployVisibility.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    APP_DEPLOY_VISIBILITY_UNSPECIFIED: _AppDeployVisibility.ValueType  # 0
    APP_DEPLOY_VISIBILITY_WORKSPACE: _AppDeployVisibility.ValueType  # 1
    APP_DEPLOY_VISIBILITY_PUBLIC: _AppDeployVisibility.ValueType  # 2

class AppDeployVisibility(_AppDeployVisibility, metaclass=_AppDeployVisibilityEnumTypeWrapper): ...

APP_DEPLOY_VISIBILITY_UNSPECIFIED: AppDeployVisibility.ValueType  # 0
APP_DEPLOY_VISIBILITY_WORKSPACE: AppDeployVisibility.ValueType  # 1
APP_DEPLOY_VISIBILITY_PUBLIC: AppDeployVisibility.ValueType  # 2
global___AppDeployVisibility = AppDeployVisibility

class _AppDisconnectReason:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AppDisconnectReasonEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AppDisconnectReason.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    APP_DISCONNECT_REASON_UNSPECIFIED: _AppDisconnectReason.ValueType  # 0
    APP_DISCONNECT_REASON_LOCAL_EXCEPTION: _AppDisconnectReason.ValueType  # 1
    APP_DISCONNECT_REASON_KEYBOARD_INTERRUPT: _AppDisconnectReason.ValueType  # 2
    APP_DISCONNECT_REASON_ENTRYPOINT_COMPLETED: _AppDisconnectReason.ValueType  # 3
    APP_DISCONNECT_REASON_DEPLOYMENT_EXCEPTION: _AppDisconnectReason.ValueType  # 4
    APP_DISCONNECT_REASON_REMOTE_EXCEPTION: _AppDisconnectReason.ValueType  # 5

class AppDisconnectReason(_AppDisconnectReason, metaclass=_AppDisconnectReasonEnumTypeWrapper): ...

APP_DISCONNECT_REASON_UNSPECIFIED: AppDisconnectReason.ValueType  # 0
APP_DISCONNECT_REASON_LOCAL_EXCEPTION: AppDisconnectReason.ValueType  # 1
APP_DISCONNECT_REASON_KEYBOARD_INTERRUPT: AppDisconnectReason.ValueType  # 2
APP_DISCONNECT_REASON_ENTRYPOINT_COMPLETED: AppDisconnectReason.ValueType  # 3
APP_DISCONNECT_REASON_DEPLOYMENT_EXCEPTION: AppDisconnectReason.ValueType  # 4
APP_DISCONNECT_REASON_REMOTE_EXCEPTION: AppDisconnectReason.ValueType  # 5
global___AppDisconnectReason = AppDisconnectReason

class _AppState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AppStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AppState.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    APP_STATE_UNSPECIFIED: _AppState.ValueType  # 0
    APP_STATE_EPHEMERAL: _AppState.ValueType  # 1
    """Will be discharged when the client disconnects"""
    APP_STATE_DETACHED: _AppState.ValueType  # 2
    APP_STATE_DEPLOYED: _AppState.ValueType  # 3
    """Will be discharged when overwritten"""
    APP_STATE_STOPPING: _AppState.ValueType  # 4
    """Winding down app due to user termination."""
    APP_STATE_STOPPED: _AppState.ValueType  # 5
    """Stopped"""
    APP_STATE_INITIALIZING: _AppState.ValueType  # 6
    """App is created and in process of deployment."""
    APP_STATE_DISABLED: _AppState.ValueType  # 7
    """Same as stopped but prevented from being garbage collected"""
    APP_STATE_DETACHED_DISCONNECTED: _AppState.ValueType  # 8
    """App is detached and local client has disconnected."""
    APP_STATE_DERIVED: _AppState.ValueType  # 9
    """App is derived from another workspace. Acts as a static, immutable group of functions."""

class AppState(_AppState, metaclass=_AppStateEnumTypeWrapper):
    """NOTE: make sure to update the frontend if we add a new state here
    https://github.com/modal-labs/modal/blob/main/frontend/src/routes/(dashboard)/%5B%5Bworkspace%5D%5D/apps/+page.svelte#L95
    """

APP_STATE_UNSPECIFIED: AppState.ValueType  # 0
APP_STATE_EPHEMERAL: AppState.ValueType  # 1
"""Will be discharged when the client disconnects"""
APP_STATE_DETACHED: AppState.ValueType  # 2
APP_STATE_DEPLOYED: AppState.ValueType  # 3
"""Will be discharged when overwritten"""
APP_STATE_STOPPING: AppState.ValueType  # 4
"""Winding down app due to user termination."""
APP_STATE_STOPPED: AppState.ValueType  # 5
"""Stopped"""
APP_STATE_INITIALIZING: AppState.ValueType  # 6
"""App is created and in process of deployment."""
APP_STATE_DISABLED: AppState.ValueType  # 7
"""Same as stopped but prevented from being garbage collected"""
APP_STATE_DETACHED_DISCONNECTED: AppState.ValueType  # 8
"""App is detached and local client has disconnected."""
APP_STATE_DERIVED: AppState.ValueType  # 9
"""App is derived from another workspace. Acts as a static, immutable group of functions."""
global___AppState = AppState

class _AppStopSource:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _AppStopSourceEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_AppStopSource.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    APP_STOP_SOURCE_UNSPECIFIED: _AppStopSource.ValueType  # 0
    APP_STOP_SOURCE_CLI: _AppStopSource.ValueType  # 1
    APP_STOP_SOURCE_PYTHON_CLIENT: _AppStopSource.ValueType  # 2
    APP_STOP_SOURCE_WEB: _AppStopSource.ValueType  # 3

class AppStopSource(_AppStopSource, metaclass=_AppStopSourceEnumTypeWrapper): ...

APP_STOP_SOURCE_UNSPECIFIED: AppStopSource.ValueType  # 0
APP_STOP_SOURCE_CLI: AppStopSource.ValueType  # 1
APP_STOP_SOURCE_PYTHON_CLIENT: AppStopSource.ValueType  # 2
APP_STOP_SOURCE_WEB: AppStopSource.ValueType  # 3
global___AppStopSource = AppStopSource

class _CertificateStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _CertificateStatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_CertificateStatus.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    CERTIFICATE_STATUS_PENDING: _CertificateStatus.ValueType  # 0
    CERTIFICATE_STATUS_ISSUED: _CertificateStatus.ValueType  # 1
    CERTIFICATE_STATUS_FAILED: _CertificateStatus.ValueType  # 2
    CERTIFICATE_STATUS_REVOKED: _CertificateStatus.ValueType  # 3

class CertificateStatus(_CertificateStatus, metaclass=_CertificateStatusEnumTypeWrapper): ...

CERTIFICATE_STATUS_PENDING: CertificateStatus.ValueType  # 0
CERTIFICATE_STATUS_ISSUED: CertificateStatus.ValueType  # 1
CERTIFICATE_STATUS_FAILED: CertificateStatus.ValueType  # 2
CERTIFICATE_STATUS_REVOKED: CertificateStatus.ValueType  # 3
global___CertificateStatus = CertificateStatus

class _CheckpointStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _CheckpointStatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_CheckpointStatus.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    CHECKPOINT_STATUS_UNSPECIFIED: _CheckpointStatus.ValueType  # 0
    CHECKPOINT_STATUS_PENDING: _CheckpointStatus.ValueType  # 1
    CHECKPOINT_STATUS_PROCESSING: _CheckpointStatus.ValueType  # 2
    CHECKPOINT_STATUS_READY: _CheckpointStatus.ValueType  # 3
    CHECKPOINT_STATUS_FAILED: _CheckpointStatus.ValueType  # 4

class CheckpointStatus(_CheckpointStatus, metaclass=_CheckpointStatusEnumTypeWrapper): ...

CHECKPOINT_STATUS_UNSPECIFIED: CheckpointStatus.ValueType  # 0
CHECKPOINT_STATUS_PENDING: CheckpointStatus.ValueType  # 1
CHECKPOINT_STATUS_PROCESSING: CheckpointStatus.ValueType  # 2
CHECKPOINT_STATUS_READY: CheckpointStatus.ValueType  # 3
CHECKPOINT_STATUS_FAILED: CheckpointStatus.ValueType  # 4
global___CheckpointStatus = CheckpointStatus

class _ClientType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ClientTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ClientType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    CLIENT_TYPE_UNSPECIFIED: _ClientType.ValueType  # 0
    CLIENT_TYPE_CLIENT: _ClientType.ValueType  # 1
    """modal-client: Modal Python SDK"""
    CLIENT_TYPE_WORKER: _ClientType.ValueType  # 2
    """modal-worker"""
    CLIENT_TYPE_CONTAINER: _ClientType.ValueType  # 3
    """modal-client from inside containers"""
    CLIENT_TYPE_WEB_SERVER: _ClientType.ValueType  # 5
    """modal-web"""
    CLIENT_TYPE_NOTEBOOK_KERNEL: _ClientType.ValueType  # 6
    """kernelshim.py from notebooks"""
    CLIENT_TYPE_LIBMODAL: _ClientType.ValueType  # 7
    """libmodal: experimental client library"""

class ClientType(_ClientType, metaclass=_ClientTypeEnumTypeWrapper): ...

CLIENT_TYPE_UNSPECIFIED: ClientType.ValueType  # 0
CLIENT_TYPE_CLIENT: ClientType.ValueType  # 1
"""modal-client: Modal Python SDK"""
CLIENT_TYPE_WORKER: ClientType.ValueType  # 2
"""modal-worker"""
CLIENT_TYPE_CONTAINER: ClientType.ValueType  # 3
"""modal-client from inside containers"""
CLIENT_TYPE_WEB_SERVER: ClientType.ValueType  # 5
"""modal-web"""
CLIENT_TYPE_NOTEBOOK_KERNEL: ClientType.ValueType  # 6
"""kernelshim.py from notebooks"""
CLIENT_TYPE_LIBMODAL: ClientType.ValueType  # 7
"""libmodal: experimental client library"""
global___ClientType = ClientType

class _CloudProvider:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _CloudProviderEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_CloudProvider.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    CLOUD_PROVIDER_UNSPECIFIED: _CloudProvider.ValueType  # 0
    CLOUD_PROVIDER_AWS: _CloudProvider.ValueType  # 1
    CLOUD_PROVIDER_GCP: _CloudProvider.ValueType  # 2
    CLOUD_PROVIDER_AUTO: _CloudProvider.ValueType  # 3
    CLOUD_PROVIDER_OCI: _CloudProvider.ValueType  # 4

class CloudProvider(_CloudProvider, metaclass=_CloudProviderEnumTypeWrapper): ...

CLOUD_PROVIDER_UNSPECIFIED: CloudProvider.ValueType  # 0
CLOUD_PROVIDER_AWS: CloudProvider.ValueType  # 1
CLOUD_PROVIDER_GCP: CloudProvider.ValueType  # 2
CLOUD_PROVIDER_AUTO: CloudProvider.ValueType  # 3
CLOUD_PROVIDER_OCI: CloudProvider.ValueType  # 4
global___CloudProvider = CloudProvider

class _DNSRecordType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DNSRecordTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DNSRecordType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DNS_RECORD_TYPE_A: _DNSRecordType.ValueType  # 0
    DNS_RECORD_TYPE_TXT: _DNSRecordType.ValueType  # 1
    DNS_RECORD_TYPE_CNAME: _DNSRecordType.ValueType  # 2

class DNSRecordType(_DNSRecordType, metaclass=_DNSRecordTypeEnumTypeWrapper): ...

DNS_RECORD_TYPE_A: DNSRecordType.ValueType  # 0
DNS_RECORD_TYPE_TXT: DNSRecordType.ValueType  # 1
DNS_RECORD_TYPE_CNAME: DNSRecordType.ValueType  # 2
global___DNSRecordType = DNSRecordType

class _DataFormat:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DataFormatEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DataFormat.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DATA_FORMAT_UNSPECIFIED: _DataFormat.ValueType  # 0
    DATA_FORMAT_PICKLE: _DataFormat.ValueType  # 1
    """Cloudpickle"""
    DATA_FORMAT_ASGI: _DataFormat.ValueType  # 2
    """"Asgi" protobuf message"""
    DATA_FORMAT_GENERATOR_DONE: _DataFormat.ValueType  # 3
    """"GeneratorDone" protobuf message"""

class DataFormat(_DataFormat, metaclass=_DataFormatEnumTypeWrapper):
    """Which data format a binary message is encoded with."""

DATA_FORMAT_UNSPECIFIED: DataFormat.ValueType  # 0
DATA_FORMAT_PICKLE: DataFormat.ValueType  # 1
"""Cloudpickle"""
DATA_FORMAT_ASGI: DataFormat.ValueType  # 2
""""Asgi" protobuf message"""
DATA_FORMAT_GENERATOR_DONE: DataFormat.ValueType  # 3
""""GeneratorDone" protobuf message"""
global___DataFormat = DataFormat

class _DeploymentNamespace:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DeploymentNamespaceEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_DeploymentNamespace.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DEPLOYMENT_NAMESPACE_UNSPECIFIED: _DeploymentNamespace.ValueType  # 0
    DEPLOYMENT_NAMESPACE_WORKSPACE: _DeploymentNamespace.ValueType  # 1
    DEPLOYMENT_NAMESPACE_GLOBAL: _DeploymentNamespace.ValueType  # 3

class DeploymentNamespace(_DeploymentNamespace, metaclass=_DeploymentNamespaceEnumTypeWrapper): ...

DEPLOYMENT_NAMESPACE_UNSPECIFIED: DeploymentNamespace.ValueType  # 0
DEPLOYMENT_NAMESPACE_WORKSPACE: DeploymentNamespace.ValueType  # 1
DEPLOYMENT_NAMESPACE_GLOBAL: DeploymentNamespace.ValueType  # 3
global___DeploymentNamespace = DeploymentNamespace

class _ExecOutputOption:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ExecOutputOptionEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ExecOutputOption.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    EXEC_OUTPUT_OPTION_UNSPECIFIED: _ExecOutputOption.ValueType  # 0
    EXEC_OUTPUT_OPTION_DEVNULL: _ExecOutputOption.ValueType  # 1
    EXEC_OUTPUT_OPTION_PIPE: _ExecOutputOption.ValueType  # 2
    EXEC_OUTPUT_OPTION_STDOUT: _ExecOutputOption.ValueType  # 3

class ExecOutputOption(_ExecOutputOption, metaclass=_ExecOutputOptionEnumTypeWrapper): ...

EXEC_OUTPUT_OPTION_UNSPECIFIED: ExecOutputOption.ValueType  # 0
EXEC_OUTPUT_OPTION_DEVNULL: ExecOutputOption.ValueType  # 1
EXEC_OUTPUT_OPTION_PIPE: ExecOutputOption.ValueType  # 2
EXEC_OUTPUT_OPTION_STDOUT: ExecOutputOption.ValueType  # 3
global___ExecOutputOption = ExecOutputOption

class _FileDescriptor:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _FileDescriptorEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_FileDescriptor.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    FILE_DESCRIPTOR_UNSPECIFIED: _FileDescriptor.ValueType  # 0
    FILE_DESCRIPTOR_STDOUT: _FileDescriptor.ValueType  # 1
    FILE_DESCRIPTOR_STDERR: _FileDescriptor.ValueType  # 2
    FILE_DESCRIPTOR_INFO: _FileDescriptor.ValueType  # 3

class FileDescriptor(_FileDescriptor, metaclass=_FileDescriptorEnumTypeWrapper): ...

FILE_DESCRIPTOR_UNSPECIFIED: FileDescriptor.ValueType  # 0
FILE_DESCRIPTOR_STDOUT: FileDescriptor.ValueType  # 1
FILE_DESCRIPTOR_STDERR: FileDescriptor.ValueType  # 2
FILE_DESCRIPTOR_INFO: FileDescriptor.ValueType  # 3
global___FileDescriptor = FileDescriptor

class _FunctionCallInvocationType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _FunctionCallInvocationTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_FunctionCallInvocationType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    FUNCTION_CALL_INVOCATION_TYPE_UNSPECIFIED: _FunctionCallInvocationType.ValueType  # 0
    FUNCTION_CALL_INVOCATION_TYPE_SYNC_LEGACY: _FunctionCallInvocationType.ValueType  # 1
    FUNCTION_CALL_INVOCATION_TYPE_ASYNC_LEGACY: _FunctionCallInvocationType.ValueType  # 2
    FUNCTION_CALL_INVOCATION_TYPE_ASYNC: _FunctionCallInvocationType.ValueType  # 3
    FUNCTION_CALL_INVOCATION_TYPE_SYNC: _FunctionCallInvocationType.ValueType  # 4

class FunctionCallInvocationType(_FunctionCallInvocationType, metaclass=_FunctionCallInvocationTypeEnumTypeWrapper): ...

FUNCTION_CALL_INVOCATION_TYPE_UNSPECIFIED: FunctionCallInvocationType.ValueType  # 0
FUNCTION_CALL_INVOCATION_TYPE_SYNC_LEGACY: FunctionCallInvocationType.ValueType  # 1
FUNCTION_CALL_INVOCATION_TYPE_ASYNC_LEGACY: FunctionCallInvocationType.ValueType  # 2
FUNCTION_CALL_INVOCATION_TYPE_ASYNC: FunctionCallInvocationType.ValueType  # 3
FUNCTION_CALL_INVOCATION_TYPE_SYNC: FunctionCallInvocationType.ValueType  # 4
global___FunctionCallInvocationType = FunctionCallInvocationType

class _FunctionCallType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _FunctionCallTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_FunctionCallType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    FUNCTION_CALL_TYPE_UNSPECIFIED: _FunctionCallType.ValueType  # 0
    FUNCTION_CALL_TYPE_UNARY: _FunctionCallType.ValueType  # 1
    FUNCTION_CALL_TYPE_MAP: _FunctionCallType.ValueType  # 2

class FunctionCallType(_FunctionCallType, metaclass=_FunctionCallTypeEnumTypeWrapper): ...

FUNCTION_CALL_TYPE_UNSPECIFIED: FunctionCallType.ValueType  # 0
FUNCTION_CALL_TYPE_UNARY: FunctionCallType.ValueType  # 1
FUNCTION_CALL_TYPE_MAP: FunctionCallType.ValueType  # 2
global___FunctionCallType = FunctionCallType

class _GPUType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _GPUTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_GPUType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    GPU_TYPE_UNSPECIFIED: _GPUType.ValueType  # 0
    """Note: this enum is no longer used by current clients - don't add new types
    Old clients still send it, so we use it server-side for compatibility
    """
    GPU_TYPE_T4: _GPUType.ValueType  # 1
    GPU_TYPE_A100: _GPUType.ValueType  # 2
    GPU_TYPE_A10G: _GPUType.ValueType  # 3
    GPU_TYPE_ANY: _GPUType.ValueType  # 4
    GPU_TYPE_A100_80GB: _GPUType.ValueType  # 8
    GPU_TYPE_L4: _GPUType.ValueType  # 9
    GPU_TYPE_H100: _GPUType.ValueType  # 10
    GPU_TYPE_L40S: _GPUType.ValueType  # 11
    GPU_TYPE_H200: _GPUType.ValueType  # 12

class GPUType(_GPUType, metaclass=_GPUTypeEnumTypeWrapper): ...

GPU_TYPE_UNSPECIFIED: GPUType.ValueType  # 0
"""Note: this enum is no longer used by current clients - don't add new types
Old clients still send it, so we use it server-side for compatibility
"""
GPU_TYPE_T4: GPUType.ValueType  # 1
GPU_TYPE_A100: GPUType.ValueType  # 2
GPU_TYPE_A10G: GPUType.ValueType  # 3
GPU_TYPE_ANY: GPUType.ValueType  # 4
GPU_TYPE_A100_80GB: GPUType.ValueType  # 8
GPU_TYPE_L4: GPUType.ValueType  # 9
GPU_TYPE_H100: GPUType.ValueType  # 10
GPU_TYPE_L40S: GPUType.ValueType  # 11
GPU_TYPE_H200: GPUType.ValueType  # 12
global___GPUType = GPUType

class _ObjectCreationType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ObjectCreationTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ObjectCreationType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    OBJECT_CREATION_TYPE_UNSPECIFIED: _ObjectCreationType.ValueType  # 0
    """just lookup"""
    OBJECT_CREATION_TYPE_CREATE_IF_MISSING: _ObjectCreationType.ValueType  # 1
    OBJECT_CREATION_TYPE_CREATE_FAIL_IF_EXISTS: _ObjectCreationType.ValueType  # 2
    OBJECT_CREATION_TYPE_CREATE_OVERWRITE_IF_EXISTS: _ObjectCreationType.ValueType  # 3
    OBJECT_CREATION_TYPE_ANONYMOUS_OWNED_BY_APP: _ObjectCreationType.ValueType  # 4
    """deprecate at some point"""
    OBJECT_CREATION_TYPE_EPHEMERAL: _ObjectCreationType.ValueType  # 5

class ObjectCreationType(_ObjectCreationType, metaclass=_ObjectCreationTypeEnumTypeWrapper): ...

OBJECT_CREATION_TYPE_UNSPECIFIED: ObjectCreationType.ValueType  # 0
"""just lookup"""
OBJECT_CREATION_TYPE_CREATE_IF_MISSING: ObjectCreationType.ValueType  # 1
OBJECT_CREATION_TYPE_CREATE_FAIL_IF_EXISTS: ObjectCreationType.ValueType  # 2
OBJECT_CREATION_TYPE_CREATE_OVERWRITE_IF_EXISTS: ObjectCreationType.ValueType  # 3
OBJECT_CREATION_TYPE_ANONYMOUS_OWNED_BY_APP: ObjectCreationType.ValueType  # 4
"""deprecate at some point"""
OBJECT_CREATION_TYPE_EPHEMERAL: ObjectCreationType.ValueType  # 5
global___ObjectCreationType = ObjectCreationType

class _ParameterType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ParameterTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ParameterType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PARAM_TYPE_UNSPECIFIED: _ParameterType.ValueType  # 0
    PARAM_TYPE_STRING: _ParameterType.ValueType  # 1
    PARAM_TYPE_INT: _ParameterType.ValueType  # 2
    PARAM_TYPE_PICKLE: _ParameterType.ValueType  # 3
    """currently unused"""
    PARAM_TYPE_BYTES: _ParameterType.ValueType  # 4
    PARAM_TYPE_UNKNOWN: _ParameterType.ValueType  # 5
    """used in schemas to signify unrecognized or un-annotated types"""
    PARAM_TYPE_LIST: _ParameterType.ValueType  # 6
    PARAM_TYPE_DICT: _ParameterType.ValueType  # 7
    PARAM_TYPE_NONE: _ParameterType.ValueType  # 8
    PARAM_TYPE_BOOL: _ParameterType.ValueType  # 9

class ParameterType(_ParameterType, metaclass=_ParameterTypeEnumTypeWrapper): ...

PARAM_TYPE_UNSPECIFIED: ParameterType.ValueType  # 0
PARAM_TYPE_STRING: ParameterType.ValueType  # 1
PARAM_TYPE_INT: ParameterType.ValueType  # 2
PARAM_TYPE_PICKLE: ParameterType.ValueType  # 3
"""currently unused"""
PARAM_TYPE_BYTES: ParameterType.ValueType  # 4
PARAM_TYPE_UNKNOWN: ParameterType.ValueType  # 5
"""used in schemas to signify unrecognized or un-annotated types"""
PARAM_TYPE_LIST: ParameterType.ValueType  # 6
PARAM_TYPE_DICT: ParameterType.ValueType  # 7
PARAM_TYPE_NONE: ParameterType.ValueType  # 8
PARAM_TYPE_BOOL: ParameterType.ValueType  # 9
global___ParameterType = ParameterType

class _ProgressType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ProgressTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ProgressType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    IMAGE_SNAPSHOT_UPLOAD: _ProgressType.ValueType  # 0
    """TODO(erikbern): shouldn't be zero, and needs prefix"""
    FUNCTION_QUEUED: _ProgressType.ValueType  # 1
    """TODO(erikbern): needs_prefix"""

class ProgressType(_ProgressType, metaclass=_ProgressTypeEnumTypeWrapper): ...

IMAGE_SNAPSHOT_UPLOAD: ProgressType.ValueType  # 0
"""TODO(erikbern): shouldn't be zero, and needs prefix"""
FUNCTION_QUEUED: ProgressType.ValueType  # 1
"""TODO(erikbern): needs_prefix"""
global___ProgressType = ProgressType

class _ProxyIpStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ProxyIpStatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ProxyIpStatus.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PROXY_IP_STATUS_UNSPECIFIED: _ProxyIpStatus.ValueType  # 0
    PROXY_IP_STATUS_CREATING: _ProxyIpStatus.ValueType  # 1
    PROXY_IP_STATUS_ONLINE: _ProxyIpStatus.ValueType  # 2
    PROXY_IP_STATUS_TERMINATED: _ProxyIpStatus.ValueType  # 3
    PROXY_IP_STATUS_UNHEALTHY: _ProxyIpStatus.ValueType  # 4

class ProxyIpStatus(_ProxyIpStatus, metaclass=_ProxyIpStatusEnumTypeWrapper): ...

PROXY_IP_STATUS_UNSPECIFIED: ProxyIpStatus.ValueType  # 0
PROXY_IP_STATUS_CREATING: ProxyIpStatus.ValueType  # 1
PROXY_IP_STATUS_ONLINE: ProxyIpStatus.ValueType  # 2
PROXY_IP_STATUS_TERMINATED: ProxyIpStatus.ValueType  # 3
PROXY_IP_STATUS_UNHEALTHY: ProxyIpStatus.ValueType  # 4
global___ProxyIpStatus = ProxyIpStatus

class _ProxyType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ProxyTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ProxyType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PROXY_TYPE_UNSPECIFIED: _ProxyType.ValueType  # 0
    PROXY_TYPE_LEGACY: _ProxyType.ValueType  # 1
    PROXY_TYPE_VPROX: _ProxyType.ValueType  # 2

class ProxyType(_ProxyType, metaclass=_ProxyTypeEnumTypeWrapper): ...

PROXY_TYPE_UNSPECIFIED: ProxyType.ValueType  # 0
PROXY_TYPE_LEGACY: ProxyType.ValueType  # 1
PROXY_TYPE_VPROX: ProxyType.ValueType  # 2
global___ProxyType = ProxyType

class _RateLimitInterval:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _RateLimitIntervalEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_RateLimitInterval.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RATE_LIMIT_INTERVAL_UNSPECIFIED: _RateLimitInterval.ValueType  # 0
    RATE_LIMIT_INTERVAL_SECOND: _RateLimitInterval.ValueType  # 1
    RATE_LIMIT_INTERVAL_MINUTE: _RateLimitInterval.ValueType  # 2

class RateLimitInterval(_RateLimitInterval, metaclass=_RateLimitIntervalEnumTypeWrapper): ...

RATE_LIMIT_INTERVAL_UNSPECIFIED: RateLimitInterval.ValueType  # 0
RATE_LIMIT_INTERVAL_SECOND: RateLimitInterval.ValueType  # 1
RATE_LIMIT_INTERVAL_MINUTE: RateLimitInterval.ValueType  # 2
global___RateLimitInterval = RateLimitInterval

class _RegistryAuthType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _RegistryAuthTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_RegistryAuthType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    REGISTRY_AUTH_TYPE_UNSPECIFIED: _RegistryAuthType.ValueType  # 0
    """Older clients send this instead of "public"."""
    REGISTRY_AUTH_TYPE_AWS: _RegistryAuthType.ValueType  # 1
    REGISTRY_AUTH_TYPE_GCP: _RegistryAuthType.ValueType  # 2
    REGISTRY_AUTH_TYPE_PUBLIC: _RegistryAuthType.ValueType  # 3
    REGISTRY_AUTH_TYPE_STATIC_CREDS: _RegistryAuthType.ValueType  # 4

class RegistryAuthType(_RegistryAuthType, metaclass=_RegistryAuthTypeEnumTypeWrapper): ...

REGISTRY_AUTH_TYPE_UNSPECIFIED: RegistryAuthType.ValueType  # 0
"""Older clients send this instead of "public"."""
REGISTRY_AUTH_TYPE_AWS: RegistryAuthType.ValueType  # 1
REGISTRY_AUTH_TYPE_GCP: RegistryAuthType.ValueType  # 2
REGISTRY_AUTH_TYPE_PUBLIC: RegistryAuthType.ValueType  # 3
REGISTRY_AUTH_TYPE_STATIC_CREDS: RegistryAuthType.ValueType  # 4
global___RegistryAuthType = RegistryAuthType

class _SeekWhence:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SeekWhenceEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SeekWhence.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SEEK_SET: _SeekWhence.ValueType  # 0
    SEEK_CUR: _SeekWhence.ValueType  # 1
    SEEK_END: _SeekWhence.ValueType  # 2

class SeekWhence(_SeekWhence, metaclass=_SeekWhenceEnumTypeWrapper): ...

SEEK_SET: SeekWhence.ValueType  # 0
SEEK_CUR: SeekWhence.ValueType  # 1
SEEK_END: SeekWhence.ValueType  # 2
global___SeekWhence = SeekWhence

class _SystemErrorCode:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SystemErrorCodeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SystemErrorCode.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SYSTEM_ERROR_CODE_UNSPECIFIED: _SystemErrorCode.ValueType  # 0
    SYSTEM_ERROR_CODE_PERM: _SystemErrorCode.ValueType  # 1
    """EPERM: Operation not permitted"""
    SYSTEM_ERROR_CODE_NOENT: _SystemErrorCode.ValueType  # 2
    """ENOENT: No such file or directory"""
    SYSTEM_ERROR_CODE_IO: _SystemErrorCode.ValueType  # 5
    """EIO: Input/output error"""
    SYSTEM_ERROR_CODE_NXIO: _SystemErrorCode.ValueType  # 6
    """ENXIO: No such device or address"""
    SYSTEM_ERROR_CODE_NOMEM: _SystemErrorCode.ValueType  # 12
    """ENOMEM: Out of memory"""
    SYSTEM_ERROR_CODE_ACCES: _SystemErrorCode.ValueType  # 13
    """EACCES: Permission denied"""
    SYSTEM_ERROR_CODE_EXIST: _SystemErrorCode.ValueType  # 17
    """EEXIST: File exists"""
    SYSTEM_ERROR_CODE_NOTDIR: _SystemErrorCode.ValueType  # 20
    """ENOTDIR: Not a directory"""
    SYSTEM_ERROR_CODE_ISDIR: _SystemErrorCode.ValueType  # 21
    """EISDIR: Is a directory"""
    SYSTEM_ERROR_CODE_INVAL: _SystemErrorCode.ValueType  # 22
    """EINVAL: Invalid argument"""
    SYSTEM_ERROR_CODE_MFILE: _SystemErrorCode.ValueType  # 24
    """EMFILE: Too many open files"""
    SYSTEM_ERROR_CODE_FBIG: _SystemErrorCode.ValueType  # 27
    """EFBIG: File too large"""
    SYSTEM_ERROR_CODE_NOSPC: _SystemErrorCode.ValueType  # 28
    """ENOSPC: No space left on device"""

class SystemErrorCode(_SystemErrorCode, metaclass=_SystemErrorCodeEnumTypeWrapper): ...

SYSTEM_ERROR_CODE_UNSPECIFIED: SystemErrorCode.ValueType  # 0
SYSTEM_ERROR_CODE_PERM: SystemErrorCode.ValueType  # 1
"""EPERM: Operation not permitted"""
SYSTEM_ERROR_CODE_NOENT: SystemErrorCode.ValueType  # 2
"""ENOENT: No such file or directory"""
SYSTEM_ERROR_CODE_IO: SystemErrorCode.ValueType  # 5
"""EIO: Input/output error"""
SYSTEM_ERROR_CODE_NXIO: SystemErrorCode.ValueType  # 6
"""ENXIO: No such device or address"""
SYSTEM_ERROR_CODE_NOMEM: SystemErrorCode.ValueType  # 12
"""ENOMEM: Out of memory"""
SYSTEM_ERROR_CODE_ACCES: SystemErrorCode.ValueType  # 13
"""EACCES: Permission denied"""
SYSTEM_ERROR_CODE_EXIST: SystemErrorCode.ValueType  # 17
"""EEXIST: File exists"""
SYSTEM_ERROR_CODE_NOTDIR: SystemErrorCode.ValueType  # 20
"""ENOTDIR: Not a directory"""
SYSTEM_ERROR_CODE_ISDIR: SystemErrorCode.ValueType  # 21
"""EISDIR: Is a directory"""
SYSTEM_ERROR_CODE_INVAL: SystemErrorCode.ValueType  # 22
"""EINVAL: Invalid argument"""
SYSTEM_ERROR_CODE_MFILE: SystemErrorCode.ValueType  # 24
"""EMFILE: Too many open files"""
SYSTEM_ERROR_CODE_FBIG: SystemErrorCode.ValueType  # 27
"""EFBIG: File too large"""
SYSTEM_ERROR_CODE_NOSPC: SystemErrorCode.ValueType  # 28
"""ENOSPC: No space left on device"""
global___SystemErrorCode = SystemErrorCode

class _TaskState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _TaskStateEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_TaskState.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    TASK_STATE_UNSPECIFIED: _TaskState.ValueType  # 0
    TASK_STATE_CREATED: _TaskState.ValueType  # 6
    TASK_STATE_QUEUED: _TaskState.ValueType  # 1
    TASK_STATE_WORKER_ASSIGNED: _TaskState.ValueType  # 2
    TASK_STATE_LOADING_IMAGE: _TaskState.ValueType  # 3
    TASK_STATE_ACTIVE: _TaskState.ValueType  # 4
    TASK_STATE_COMPLETED: _TaskState.ValueType  # 5
    TASK_STATE_CREATING_CONTAINER: _TaskState.ValueType  # 7
    TASK_STATE_IDLE: _TaskState.ValueType  # 8
    TASK_STATE_PREEMPTIBLE: _TaskState.ValueType  # 9
    TASK_STATE_PREEMPTED: _TaskState.ValueType  # 10
    TASK_STATE_LOADING_CHECKPOINT_IMAGE: _TaskState.ValueType  # 11

class TaskState(_TaskState, metaclass=_TaskStateEnumTypeWrapper): ...

TASK_STATE_UNSPECIFIED: TaskState.ValueType  # 0
TASK_STATE_CREATED: TaskState.ValueType  # 6
TASK_STATE_QUEUED: TaskState.ValueType  # 1
TASK_STATE_WORKER_ASSIGNED: TaskState.ValueType  # 2
TASK_STATE_LOADING_IMAGE: TaskState.ValueType  # 3
TASK_STATE_ACTIVE: TaskState.ValueType  # 4
TASK_STATE_COMPLETED: TaskState.ValueType  # 5
TASK_STATE_CREATING_CONTAINER: TaskState.ValueType  # 7
TASK_STATE_IDLE: TaskState.ValueType  # 8
TASK_STATE_PREEMPTIBLE: TaskState.ValueType  # 9
TASK_STATE_PREEMPTED: TaskState.ValueType  # 10
TASK_STATE_LOADING_CHECKPOINT_IMAGE: TaskState.ValueType  # 11
global___TaskState = TaskState

class _TunnelType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _TunnelTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_TunnelType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    TUNNEL_TYPE_UNSPECIFIED: _TunnelType.ValueType  # 0
    TUNNEL_TYPE_H2: _TunnelType.ValueType  # 1
    """HTTP/2 tunnel"""

class TunnelType(_TunnelType, metaclass=_TunnelTypeEnumTypeWrapper): ...

TUNNEL_TYPE_UNSPECIFIED: TunnelType.ValueType  # 0
TUNNEL_TYPE_H2: TunnelType.ValueType  # 1
"""HTTP/2 tunnel"""
global___TunnelType = TunnelType

class _VolumeFsVersion:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _VolumeFsVersionEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_VolumeFsVersion.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    VOLUME_FS_VERSION_UNSPECIFIED: _VolumeFsVersion.ValueType  # 0
    VOLUME_FS_VERSION_V1: _VolumeFsVersion.ValueType  # 1
    VOLUME_FS_VERSION_V2: _VolumeFsVersion.ValueType  # 2

class VolumeFsVersion(_VolumeFsVersion, metaclass=_VolumeFsVersionEnumTypeWrapper): ...

VOLUME_FS_VERSION_UNSPECIFIED: VolumeFsVersion.ValueType  # 0
VOLUME_FS_VERSION_V1: VolumeFsVersion.ValueType  # 1
VOLUME_FS_VERSION_V2: VolumeFsVersion.ValueType  # 2
global___VolumeFsVersion = VolumeFsVersion

class _WebhookAsyncMode:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _WebhookAsyncModeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_WebhookAsyncMode.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    WEBHOOK_ASYNC_MODE_UNSPECIFIED: _WebhookAsyncMode.ValueType  # 0
    WEBHOOK_ASYNC_MODE_DISABLED: _WebhookAsyncMode.ValueType  # 2
    """No longer used by client"""
    WEBHOOK_ASYNC_MODE_TRIGGER: _WebhookAsyncMode.ValueType  # 3
    """No longer used by client (previously used when wait_for_response=False)"""
    WEBHOOK_ASYNC_MODE_AUTO: _WebhookAsyncMode.ValueType  # 4
    """The default"""

class WebhookAsyncMode(_WebhookAsyncMode, metaclass=_WebhookAsyncModeEnumTypeWrapper): ...

WEBHOOK_ASYNC_MODE_UNSPECIFIED: WebhookAsyncMode.ValueType  # 0
WEBHOOK_ASYNC_MODE_DISABLED: WebhookAsyncMode.ValueType  # 2
"""No longer used by client"""
WEBHOOK_ASYNC_MODE_TRIGGER: WebhookAsyncMode.ValueType  # 3
"""No longer used by client (previously used when wait_for_response=False)"""
WEBHOOK_ASYNC_MODE_AUTO: WebhookAsyncMode.ValueType  # 4
"""The default"""
global___WebhookAsyncMode = WebhookAsyncMode

class _WebhookType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _WebhookTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_WebhookType.ValueType], builtins.type):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    WEBHOOK_TYPE_UNSPECIFIED: _WebhookType.ValueType  # 0
    WEBHOOK_TYPE_ASGI_APP: _WebhookType.ValueType  # 1
    WEBHOOK_TYPE_FUNCTION: _WebhookType.ValueType  # 2
    WEBHOOK_TYPE_WSGI_APP: _WebhookType.ValueType  # 3
    WEBHOOK_TYPE_WEB_SERVER: _WebhookType.ValueType  # 4

class WebhookType(_WebhookType, metaclass=_WebhookTypeEnumTypeWrapper): ...

WEBHOOK_TYPE_UNSPECIFIED: WebhookType.ValueType  # 0
WEBHOOK_TYPE_ASGI_APP: WebhookType.ValueType  # 1
WEBHOOK_TYPE_FUNCTION: WebhookType.ValueType  # 2
WEBHOOK_TYPE_WSGI_APP: WebhookType.ValueType  # 3
WEBHOOK_TYPE_WEB_SERVER: WebhookType.ValueType  # 4
global___WebhookType = WebhookType

class AppClientDisconnectRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    REASON_FIELD_NUMBER: builtins.int
    EXCEPTION_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    reason: global___AppDisconnectReason.ValueType
    exception: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        reason: global___AppDisconnectReason.ValueType = ...,
        exception: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "exception", b"exception", "reason", b"reason"]) -> None: ...

global___AppClientDisconnectRequest = AppClientDisconnectRequest

class AppCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLIENT_ID_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    APP_STATE_FIELD_NUMBER: builtins.int
    client_id: builtins.str
    description: builtins.str
    """Human readable label for the app"""
    environment_name: builtins.str
    app_state: global___AppState.ValueType
    def __init__(
        self,
        *,
        client_id: builtins.str = ...,
        description: builtins.str = ...,
        environment_name: builtins.str = ...,
        app_state: global___AppState.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_state", b"app_state", "client_id", b"client_id", "description", b"description", "environment_name", b"environment_name"]) -> None: ...

global___AppCreateRequest = AppCreateRequest

class AppCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    APP_PAGE_URL_FIELD_NUMBER: builtins.int
    APP_LOGS_URL_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    app_page_url: builtins.str
    app_logs_url: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        app_page_url: builtins.str = ...,
        app_logs_url: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "app_logs_url", b"app_logs_url", "app_page_url", b"app_page_url"]) -> None: ...

global___AppCreateResponse = AppCreateResponse

class AppDeployRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    OBJECT_ENTITY_FIELD_NUMBER: builtins.int
    VISIBILITY_FIELD_NUMBER: builtins.int
    TAG_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    name: builtins.str
    object_entity: builtins.str
    visibility: global___AppDeployVisibility.ValueType
    tag: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        name: builtins.str = ...,
        object_entity: builtins.str = ...,
        visibility: global___AppDeployVisibility.ValueType = ...,
        tag: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "name", b"name", "namespace", b"namespace", "object_entity", b"object_entity", "tag", b"tag", "visibility", b"visibility"]) -> None: ...

global___AppDeployRequest = AppDeployRequest

class AppDeployResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URL_FIELD_NUMBER: builtins.int
    url: builtins.str
    def __init__(
        self,
        *,
        url: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["url", b"url"]) -> None: ...

global___AppDeployResponse = AppDeployResponse

class AppDeploymentHistory(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    VERSION_FIELD_NUMBER: builtins.int
    CLIENT_VERSION_FIELD_NUMBER: builtins.int
    DEPLOYED_AT_FIELD_NUMBER: builtins.int
    DEPLOYED_BY_FIELD_NUMBER: builtins.int
    DEPLOYED_BY_AVATAR_URL_FIELD_NUMBER: builtins.int
    TAG_FIELD_NUMBER: builtins.int
    ROLLBACK_VERSION_FIELD_NUMBER: builtins.int
    ROLLBACK_ALLOWED_FIELD_NUMBER: builtins.int
    COMMIT_INFO_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    version: builtins.int
    client_version: builtins.str
    deployed_at: builtins.float
    deployed_by: builtins.str
    deployed_by_avatar_url: builtins.str
    tag: builtins.str
    rollback_version: builtins.int
    rollback_allowed: builtins.bool
    @property
    def commit_info(self) -> global___CommitInfo: ...
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        version: builtins.int = ...,
        client_version: builtins.str = ...,
        deployed_at: builtins.float = ...,
        deployed_by: builtins.str = ...,
        deployed_by_avatar_url: builtins.str = ...,
        tag: builtins.str = ...,
        rollback_version: builtins.int = ...,
        rollback_allowed: builtins.bool = ...,
        commit_info: global___CommitInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_commit_info", b"_commit_info", "commit_info", b"commit_info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_commit_info", b"_commit_info", "app_id", b"app_id", "client_version", b"client_version", "commit_info", b"commit_info", "deployed_at", b"deployed_at", "deployed_by", b"deployed_by", "deployed_by_avatar_url", b"deployed_by_avatar_url", "rollback_allowed", b"rollback_allowed", "rollback_version", b"rollback_version", "tag", b"tag", "version", b"version"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_commit_info", b"_commit_info"]) -> typing_extensions.Literal["commit_info"] | None: ...

global___AppDeploymentHistory = AppDeploymentHistory

class AppDeploymentHistoryRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id"]) -> None: ...

global___AppDeploymentHistoryRequest = AppDeploymentHistoryRequest

class AppDeploymentHistoryResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_DEPLOYMENT_HISTORIES_FIELD_NUMBER: builtins.int
    @property
    def app_deployment_histories(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___AppDeploymentHistory]: ...
    def __init__(
        self,
        *,
        app_deployment_histories: collections.abc.Iterable[global___AppDeploymentHistory] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_deployment_histories", b"app_deployment_histories"]) -> None: ...

global___AppDeploymentHistoryResponse = AppDeploymentHistoryResponse

class AppGetByDeploymentNameRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    namespace: global___DeploymentNamespace.ValueType
    name: builtins.str
    environment_name: builtins.str
    def __init__(
        self,
        *,
        namespace: global___DeploymentNamespace.ValueType = ...,
        name: builtins.str = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "name", b"name", "namespace", b"namespace"]) -> None: ...

global___AppGetByDeploymentNameRequest = AppGetByDeploymentNameRequest

class AppGetByDeploymentNameResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id"]) -> None: ...

global___AppGetByDeploymentNameResponse = AppGetByDeploymentNameResponse

class AppGetLayoutRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id"]) -> None: ...

global___AppGetLayoutRequest = AppGetLayoutRequest

class AppGetLayoutResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_LAYOUT_FIELD_NUMBER: builtins.int
    @property
    def app_layout(self) -> global___AppLayout: ...
    def __init__(
        self,
        *,
        app_layout: global___AppLayout | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["app_layout", b"app_layout"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_layout", b"app_layout"]) -> None: ...

global___AppGetLayoutResponse = AppGetLayoutResponse

class AppGetLogsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    LAST_ENTRY_ID_FIELD_NUMBER: builtins.int
    FUNCTION_ID_FIELD_NUMBER: builtins.int
    INPUT_ID_FIELD_NUMBER: builtins.int
    TASK_ID_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    timeout: builtins.float
    last_entry_id: builtins.str
    function_id: builtins.str
    input_id: builtins.str
    task_id: builtins.str
    function_call_id: builtins.str
    file_descriptor: global___FileDescriptor.ValueType
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        timeout: builtins.float = ...,
        last_entry_id: builtins.str = ...,
        function_id: builtins.str = ...,
        input_id: builtins.str = ...,
        task_id: builtins.str = ...,
        function_call_id: builtins.str = ...,
        file_descriptor: global___FileDescriptor.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "file_descriptor", b"file_descriptor", "function_call_id", b"function_call_id", "function_id", b"function_id", "input_id", b"input_id", "last_entry_id", b"last_entry_id", "task_id", b"task_id", "timeout", b"timeout"]) -> None: ...

global___AppGetLogsRequest = AppGetLogsRequest

class AppGetObjectsItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TAG_FIELD_NUMBER: builtins.int
    OBJECT_FIELD_NUMBER: builtins.int
    tag: builtins.str
    @property
    def object(self) -> global___Object: ...
    def __init__(
        self,
        *,
        tag: builtins.str = ...,
        object: global___Object | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["object", b"object"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["object", b"object", "tag", b"tag"]) -> None: ...

global___AppGetObjectsItem = AppGetObjectsItem

class AppGetObjectsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    INCLUDE_UNINDEXED_FIELD_NUMBER: builtins.int
    ONLY_CLASS_FUNCTION_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    include_unindexed: builtins.bool
    only_class_function: builtins.bool
    """True starting with 0.67.x clients, which don't create method placeholder functions"""
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        include_unindexed: builtins.bool = ...,
        only_class_function: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "include_unindexed", b"include_unindexed", "only_class_function", b"only_class_function"]) -> None: ...

global___AppGetObjectsRequest = AppGetObjectsRequest

class AppGetObjectsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEMS_FIELD_NUMBER: builtins.int
    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___AppGetObjectsItem]: ...
    def __init__(
        self,
        *,
        items: collections.abc.Iterable[global___AppGetObjectsItem] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["items", b"items"]) -> None: ...

global___AppGetObjectsResponse = AppGetObjectsResponse

class AppGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_NAME_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    app_name: builtins.str
    environment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    def __init__(
        self,
        *,
        app_name: builtins.str = ...,
        environment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_name", b"app_name", "environment_name", b"environment_name", "object_creation_type", b"object_creation_type"]) -> None: ...

global___AppGetOrCreateRequest = AppGetOrCreateRequest

class AppGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id"]) -> None: ...

global___AppGetOrCreateResponse = AppGetOrCreateResponse

class AppHeartbeatRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id"]) -> None: ...

global___AppHeartbeatRequest = AppHeartbeatRequest

class AppLayout(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class FunctionIdsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    class ClassIdsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    OBJECTS_FIELD_NUMBER: builtins.int
    FUNCTION_IDS_FIELD_NUMBER: builtins.int
    CLASS_IDS_FIELD_NUMBER: builtins.int
    @property
    def objects(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Object]: ...
    @property
    def function_ids(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """tag -> function id"""
    @property
    def class_ids(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """tag -> class id"""
    def __init__(
        self,
        *,
        objects: collections.abc.Iterable[global___Object] | None = ...,
        function_ids: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        class_ids: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["class_ids", b"class_ids", "function_ids", b"function_ids", "objects", b"objects"]) -> None: ...

global___AppLayout = AppLayout

class AppListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name"]) -> None: ...

global___AppListRequest = AppListRequest

class AppListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class AppListItem(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        APP_ID_FIELD_NUMBER: builtins.int
        DESCRIPTION_FIELD_NUMBER: builtins.int
        STATE_FIELD_NUMBER: builtins.int
        CREATED_AT_FIELD_NUMBER: builtins.int
        STOPPED_AT_FIELD_NUMBER: builtins.int
        N_RUNNING_TASKS_FIELD_NUMBER: builtins.int
        NAME_FIELD_NUMBER: builtins.int
        app_id: builtins.str
        description: builtins.str
        state: global___AppState.ValueType
        created_at: builtins.float
        stopped_at: builtins.float
        n_running_tasks: builtins.int
        name: builtins.str
        def __init__(
            self,
            *,
            app_id: builtins.str = ...,
            description: builtins.str = ...,
            state: global___AppState.ValueType = ...,
            created_at: builtins.float = ...,
            stopped_at: builtins.float = ...,
            n_running_tasks: builtins.int = ...,
            name: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "created_at", b"created_at", "description", b"description", "n_running_tasks", b"n_running_tasks", "name", b"name", "state", b"state", "stopped_at", b"stopped_at"]) -> None: ...

    APPS_FIELD_NUMBER: builtins.int
    @property
    def apps(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___AppListResponse.AppListItem]: ...
    def __init__(
        self,
        *,
        apps: collections.abc.Iterable[global___AppListResponse.AppListItem] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["apps", b"apps"]) -> None: ...

global___AppListResponse = AppListResponse

class AppLookupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_NAME_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    app_name: builtins.str
    environment_name: builtins.str
    def __init__(
        self,
        *,
        app_name: builtins.str = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_name", b"app_name", "environment_name", b"environment_name"]) -> None: ...

global___AppLookupRequest = AppLookupRequest

class AppLookupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id"]) -> None: ...

global___AppLookupResponse = AppLookupResponse

class AppPublishRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class FunctionIdsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    class ClassIdsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    class DefinitionIdsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    APP_ID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    DEPLOYMENT_TAG_FIELD_NUMBER: builtins.int
    APP_STATE_FIELD_NUMBER: builtins.int
    FUNCTION_IDS_FIELD_NUMBER: builtins.int
    CLASS_IDS_FIELD_NUMBER: builtins.int
    DEFINITION_IDS_FIELD_NUMBER: builtins.int
    ROLLBACK_VERSION_FIELD_NUMBER: builtins.int
    CLIENT_VERSION_FIELD_NUMBER: builtins.int
    COMMIT_INFO_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    name: builtins.str
    deployment_tag: builtins.str
    """Additional metadata to identify a deployment"""
    app_state: global___AppState.ValueType
    """Published app will be in this state"""
    @property
    def function_ids(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """function_name -> function_id"""
    @property
    def class_ids(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """class_name -> class_id"""
    @property
    def definition_ids(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """function_id -> definition_id"""
    rollback_version: builtins.int
    """Unused by client, but used internally"""
    client_version: builtins.str
    """Unused by client, but used internally"""
    @property
    def commit_info(self) -> global___CommitInfo:
        """Git information for deployment tracking"""
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        name: builtins.str = ...,
        deployment_tag: builtins.str = ...,
        app_state: global___AppState.ValueType = ...,
        function_ids: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        class_ids: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        definition_ids: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        rollback_version: builtins.int = ...,
        client_version: builtins.str = ...,
        commit_info: global___CommitInfo | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["commit_info", b"commit_info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "app_state", b"app_state", "class_ids", b"class_ids", "client_version", b"client_version", "commit_info", b"commit_info", "definition_ids", b"definition_ids", "deployment_tag", b"deployment_tag", "function_ids", b"function_ids", "name", b"name", "rollback_version", b"rollback_version"]) -> None: ...

global___AppPublishRequest = AppPublishRequest

class AppPublishResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URL_FIELD_NUMBER: builtins.int
    SERVER_WARNINGS_FIELD_NUMBER: builtins.int
    url: builtins.str
    @property
    def server_warnings(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Warning]: ...
    def __init__(
        self,
        *,
        url: builtins.str = ...,
        server_warnings: collections.abc.Iterable[global___Warning] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["server_warnings", b"server_warnings", "url", b"url"]) -> None: ...

global___AppPublishResponse = AppPublishResponse

class AppRollbackRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    VERSION_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    version: builtins.int
    """signed as we support negative "roll back n versions" requests"""
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        version: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "version", b"version"]) -> None: ...

global___AppRollbackRequest = AppRollbackRequest

class AppSetObjectsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class IndexedObjectIdsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    APP_ID_FIELD_NUMBER: builtins.int
    INDEXED_OBJECT_IDS_FIELD_NUMBER: builtins.int
    CLIENT_ID_FIELD_NUMBER: builtins.int
    UNINDEXED_OBJECT_IDS_FIELD_NUMBER: builtins.int
    NEW_APP_STATE_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    @property
    def indexed_object_ids(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    client_id: builtins.str
    @property
    def unindexed_object_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    new_app_state: global___AppState.ValueType
    """promotes an app from initializing to this new state"""
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        indexed_object_ids: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        client_id: builtins.str = ...,
        unindexed_object_ids: collections.abc.Iterable[builtins.str] | None = ...,
        new_app_state: global___AppState.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "client_id", b"client_id", "indexed_object_ids", b"indexed_object_ids", "new_app_state", b"new_app_state", "unindexed_object_ids", b"unindexed_object_ids"]) -> None: ...

global___AppSetObjectsRequest = AppSetObjectsRequest

class AppStopRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    SOURCE_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    source: global___AppStopSource.ValueType
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        source: global___AppStopSource.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "source", b"source"]) -> None: ...

global___AppStopRequest = AppStopRequest

class Asgi(google.protobuf.message.Message):
    """A web endpoint connection-related message.

    Modal's internal web endpoint runtime effectively acts as a global web server
    that schedules requests to tasks that are spawned on-demand, so we need an
    internal protocol to model HTTP requests. This is that protocol.

    We base this protocol on Python's ASGI specification, which is a popular
    interface between applications and web servers.

    ASGI Spec: https://asgi.readthedocs.io/en/latest/specs/www.html
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class Http(google.protobuf.message.Message):
        """Message of type "http" (connection scope)"""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        HTTP_VERSION_FIELD_NUMBER: builtins.int
        METHOD_FIELD_NUMBER: builtins.int
        SCHEME_FIELD_NUMBER: builtins.int
        PATH_FIELD_NUMBER: builtins.int
        QUERY_STRING_FIELD_NUMBER: builtins.int
        HEADERS_FIELD_NUMBER: builtins.int
        CLIENT_HOST_FIELD_NUMBER: builtins.int
        CLIENT_PORT_FIELD_NUMBER: builtins.int
        http_version: builtins.str
        method: builtins.str
        scheme: builtins.str
        path: builtins.str
        query_string: builtins.bytes
        @property
        def headers(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.bytes]:
            """Flat list of alternating header names and values"""
        client_host: builtins.str
        client_port: builtins.int
        def __init__(
            self,
            *,
            http_version: builtins.str = ...,
            method: builtins.str = ...,
            scheme: builtins.str = ...,
            path: builtins.str = ...,
            query_string: builtins.bytes = ...,
            headers: collections.abc.Iterable[builtins.bytes] | None = ...,
            client_host: builtins.str | None = ...,
            client_port: builtins.int | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_client_host", b"_client_host", "_client_port", b"_client_port", "client_host", b"client_host", "client_port", b"client_port"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_client_host", b"_client_host", "_client_port", b"_client_port", "client_host", b"client_host", "client_port", b"client_port", "headers", b"headers", "http_version", b"http_version", "method", b"method", "path", b"path", "query_string", b"query_string", "scheme", b"scheme"]) -> None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_client_host", b"_client_host"]) -> typing_extensions.Literal["client_host"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_client_port", b"_client_port"]) -> typing_extensions.Literal["client_port"] | None: ...

    class HttpRequest(google.protobuf.message.Message):
        """Message of type "http.request" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        BODY_FIELD_NUMBER: builtins.int
        MORE_BODY_FIELD_NUMBER: builtins.int
        body: builtins.bytes
        more_body: builtins.bool
        def __init__(
            self,
            *,
            body: builtins.bytes = ...,
            more_body: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["body", b"body", "more_body", b"more_body"]) -> None: ...

    class HttpResponseStart(google.protobuf.message.Message):
        """Message of type "http.response.start" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        STATUS_FIELD_NUMBER: builtins.int
        HEADERS_FIELD_NUMBER: builtins.int
        TRAILERS_FIELD_NUMBER: builtins.int
        status: builtins.int
        @property
        def headers(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.bytes]:
            """Flat list of alternating header names and values"""
        trailers: builtins.bool
        def __init__(
            self,
            *,
            status: builtins.int = ...,
            headers: collections.abc.Iterable[builtins.bytes] | None = ...,
            trailers: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["headers", b"headers", "status", b"status", "trailers", b"trailers"]) -> None: ...

    class HttpResponseBody(google.protobuf.message.Message):
        """Message of type "http.response.body" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        BODY_FIELD_NUMBER: builtins.int
        MORE_BODY_FIELD_NUMBER: builtins.int
        body: builtins.bytes
        more_body: builtins.bool
        def __init__(
            self,
            *,
            body: builtins.bytes = ...,
            more_body: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["body", b"body", "more_body", b"more_body"]) -> None: ...

    class HttpResponseTrailers(google.protobuf.message.Message):
        """Message of type "http.response.trailers" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        HEADERS_FIELD_NUMBER: builtins.int
        MORE_TRAILERS_FIELD_NUMBER: builtins.int
        @property
        def headers(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.bytes]:
            """Flat list of alternating header names and values"""
        more_trailers: builtins.bool
        def __init__(
            self,
            *,
            headers: collections.abc.Iterable[builtins.bytes] | None = ...,
            more_trailers: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["headers", b"headers", "more_trailers", b"more_trailers"]) -> None: ...

    class HttpDisconnect(google.protobuf.message.Message):
        """Message of type "http.disconnect" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        def __init__(
            self,
        ) -> None: ...

    class Websocket(google.protobuf.message.Message):
        """Message of type "websocket" (connection scope)"""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        HTTP_VERSION_FIELD_NUMBER: builtins.int
        SCHEME_FIELD_NUMBER: builtins.int
        PATH_FIELD_NUMBER: builtins.int
        QUERY_STRING_FIELD_NUMBER: builtins.int
        HEADERS_FIELD_NUMBER: builtins.int
        CLIENT_HOST_FIELD_NUMBER: builtins.int
        CLIENT_PORT_FIELD_NUMBER: builtins.int
        SUBPROTOCOLS_FIELD_NUMBER: builtins.int
        http_version: builtins.str
        """We don't support RFC 8441 yet, so this will always be "1.1" """
        scheme: builtins.str
        path: builtins.str
        query_string: builtins.bytes
        @property
        def headers(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.bytes]:
            """Flat list of alternating header names and values"""
        client_host: builtins.str
        client_port: builtins.int
        @property
        def subprotocols(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
        def __init__(
            self,
            *,
            http_version: builtins.str = ...,
            scheme: builtins.str = ...,
            path: builtins.str = ...,
            query_string: builtins.bytes = ...,
            headers: collections.abc.Iterable[builtins.bytes] | None = ...,
            client_host: builtins.str | None = ...,
            client_port: builtins.int | None = ...,
            subprotocols: collections.abc.Iterable[builtins.str] | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_client_host", b"_client_host", "_client_port", b"_client_port", "client_host", b"client_host", "client_port", b"client_port"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_client_host", b"_client_host", "_client_port", b"_client_port", "client_host", b"client_host", "client_port", b"client_port", "headers", b"headers", "http_version", b"http_version", "path", b"path", "query_string", b"query_string", "scheme", b"scheme", "subprotocols", b"subprotocols"]) -> None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_client_host", b"_client_host"]) -> typing_extensions.Literal["client_host"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_client_port", b"_client_port"]) -> typing_extensions.Literal["client_port"] | None: ...

    class WebsocketConnect(google.protobuf.message.Message):
        """Message of type "websocket.connect" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        def __init__(
            self,
        ) -> None: ...

    class WebsocketAccept(google.protobuf.message.Message):
        """Message of type "websocket.accept" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        SUBPROTOCOL_FIELD_NUMBER: builtins.int
        HEADERS_FIELD_NUMBER: builtins.int
        subprotocol: builtins.str
        @property
        def headers(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.bytes]:
            """Flat list of alternating header names and values"""
        def __init__(
            self,
            *,
            subprotocol: builtins.str | None = ...,
            headers: collections.abc.Iterable[builtins.bytes] | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_subprotocol", b"_subprotocol", "subprotocol", b"subprotocol"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_subprotocol", b"_subprotocol", "headers", b"headers", "subprotocol", b"subprotocol"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_subprotocol", b"_subprotocol"]) -> typing_extensions.Literal["subprotocol"] | None: ...

    class WebsocketReceive(google.protobuf.message.Message):
        """Message of type "websocket.receive" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        BYTES_FIELD_NUMBER: builtins.int
        TEXT_FIELD_NUMBER: builtins.int
        bytes: builtins.bytes
        text: builtins.str
        def __init__(
            self,
            *,
            bytes: builtins.bytes = ...,
            text: builtins.str = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["bytes", b"bytes", "content", b"content", "text", b"text"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["bytes", b"bytes", "content", b"content", "text", b"text"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["content", b"content"]) -> typing_extensions.Literal["bytes", "text"] | None: ...

    class WebsocketSend(google.protobuf.message.Message):
        """Message of type "websocket.send" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        BYTES_FIELD_NUMBER: builtins.int
        TEXT_FIELD_NUMBER: builtins.int
        bytes: builtins.bytes
        text: builtins.str
        def __init__(
            self,
            *,
            bytes: builtins.bytes = ...,
            text: builtins.str = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["bytes", b"bytes", "content", b"content", "text", b"text"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["bytes", b"bytes", "content", b"content", "text", b"text"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["content", b"content"]) -> typing_extensions.Literal["bytes", "text"] | None: ...

    class WebsocketDisconnect(google.protobuf.message.Message):
        """Message of type "websocket.disconnect" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        CODE_FIELD_NUMBER: builtins.int
        code: builtins.int
        def __init__(
            self,
            *,
            code: builtins.int | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_code", b"_code", "code", b"code"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_code", b"_code", "code", b"code"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_code", b"_code"]) -> typing_extensions.Literal["code"] | None: ...

    class WebsocketClose(google.protobuf.message.Message):
        """Message of type "websocket.close" """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        CODE_FIELD_NUMBER: builtins.int
        REASON_FIELD_NUMBER: builtins.int
        code: builtins.int
        reason: builtins.str
        def __init__(
            self,
            *,
            code: builtins.int | None = ...,
            reason: builtins.str = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_code", b"_code", "code", b"code"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_code", b"_code", "code", b"code", "reason", b"reason"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_code", b"_code"]) -> typing_extensions.Literal["code"] | None: ...

    HTTP_FIELD_NUMBER: builtins.int
    HTTP_REQUEST_FIELD_NUMBER: builtins.int
    HTTP_RESPONSE_START_FIELD_NUMBER: builtins.int
    HTTP_RESPONSE_BODY_FIELD_NUMBER: builtins.int
    HTTP_RESPONSE_TRAILERS_FIELD_NUMBER: builtins.int
    HTTP_DISCONNECT_FIELD_NUMBER: builtins.int
    WEBSOCKET_FIELD_NUMBER: builtins.int
    WEBSOCKET_CONNECT_FIELD_NUMBER: builtins.int
    WEBSOCKET_ACCEPT_FIELD_NUMBER: builtins.int
    WEBSOCKET_RECEIVE_FIELD_NUMBER: builtins.int
    WEBSOCKET_SEND_FIELD_NUMBER: builtins.int
    WEBSOCKET_DISCONNECT_FIELD_NUMBER: builtins.int
    WEBSOCKET_CLOSE_FIELD_NUMBER: builtins.int
    @property
    def http(self) -> global___Asgi.Http: ...
    @property
    def http_request(self) -> global___Asgi.HttpRequest: ...
    @property
    def http_response_start(self) -> global___Asgi.HttpResponseStart: ...
    @property
    def http_response_body(self) -> global___Asgi.HttpResponseBody: ...
    @property
    def http_response_trailers(self) -> global___Asgi.HttpResponseTrailers: ...
    @property
    def http_disconnect(self) -> global___Asgi.HttpDisconnect: ...
    @property
    def websocket(self) -> global___Asgi.Websocket: ...
    @property
    def websocket_connect(self) -> global___Asgi.WebsocketConnect: ...
    @property
    def websocket_accept(self) -> global___Asgi.WebsocketAccept: ...
    @property
    def websocket_receive(self) -> global___Asgi.WebsocketReceive: ...
    @property
    def websocket_send(self) -> global___Asgi.WebsocketSend: ...
    @property
    def websocket_disconnect(self) -> global___Asgi.WebsocketDisconnect: ...
    @property
    def websocket_close(self) -> global___Asgi.WebsocketClose: ...
    def __init__(
        self,
        *,
        http: global___Asgi.Http | None = ...,
        http_request: global___Asgi.HttpRequest | None = ...,
        http_response_start: global___Asgi.HttpResponseStart | None = ...,
        http_response_body: global___Asgi.HttpResponseBody | None = ...,
        http_response_trailers: global___Asgi.HttpResponseTrailers | None = ...,
        http_disconnect: global___Asgi.HttpDisconnect | None = ...,
        websocket: global___Asgi.Websocket | None = ...,
        websocket_connect: global___Asgi.WebsocketConnect | None = ...,
        websocket_accept: global___Asgi.WebsocketAccept | None = ...,
        websocket_receive: global___Asgi.WebsocketReceive | None = ...,
        websocket_send: global___Asgi.WebsocketSend | None = ...,
        websocket_disconnect: global___Asgi.WebsocketDisconnect | None = ...,
        websocket_close: global___Asgi.WebsocketClose | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["http", b"http", "http_disconnect", b"http_disconnect", "http_request", b"http_request", "http_response_body", b"http_response_body", "http_response_start", b"http_response_start", "http_response_trailers", b"http_response_trailers", "type", b"type", "websocket", b"websocket", "websocket_accept", b"websocket_accept", "websocket_close", b"websocket_close", "websocket_connect", b"websocket_connect", "websocket_disconnect", b"websocket_disconnect", "websocket_receive", b"websocket_receive", "websocket_send", b"websocket_send"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["http", b"http", "http_disconnect", b"http_disconnect", "http_request", b"http_request", "http_response_body", b"http_response_body", "http_response_start", b"http_response_start", "http_response_trailers", b"http_response_trailers", "type", b"type", "websocket", b"websocket", "websocket_accept", b"websocket_accept", "websocket_close", b"websocket_close", "websocket_connect", b"websocket_connect", "websocket_disconnect", b"websocket_disconnect", "websocket_receive", b"websocket_receive", "websocket_send", b"websocket_send"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["type", b"type"]) -> typing_extensions.Literal["http", "http_request", "http_response_start", "http_response_body", "http_response_trailers", "http_disconnect", "websocket", "websocket_connect", "websocket_accept", "websocket_receive", "websocket_send", "websocket_disconnect", "websocket_close"] | None: ...

global___Asgi = Asgi

class AttemptAwaitRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ATTEMPT_TOKEN_FIELD_NUMBER: builtins.int
    REQUESTED_AT_FIELD_NUMBER: builtins.int
    TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    attempt_token: builtins.str
    requested_at: builtins.float
    """Used for waypoints."""
    timeout_secs: builtins.float
    def __init__(
        self,
        *,
        attempt_token: builtins.str = ...,
        requested_at: builtins.float = ...,
        timeout_secs: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["attempt_token", b"attempt_token", "requested_at", b"requested_at", "timeout_secs", b"timeout_secs"]) -> None: ...

global___AttemptAwaitRequest = AttemptAwaitRequest

class AttemptAwaitResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OUTPUT_FIELD_NUMBER: builtins.int
    @property
    def output(self) -> global___FunctionGetOutputsItem: ...
    def __init__(
        self,
        *,
        output: global___FunctionGetOutputsItem | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_output", b"_output", "output", b"output"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_output", b"_output", "output", b"output"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_output", b"_output"]) -> typing_extensions.Literal["output"] | None: ...

global___AttemptAwaitResponse = AttemptAwaitResponse

class AttemptRetryRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    PARENT_INPUT_ID_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    ATTEMPT_TOKEN_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    parent_input_id: builtins.str
    @property
    def input(self) -> global___FunctionPutInputsItem: ...
    attempt_token: builtins.str
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        parent_input_id: builtins.str = ...,
        input: global___FunctionPutInputsItem | None = ...,
        attempt_token: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["input", b"input"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["attempt_token", b"attempt_token", "function_id", b"function_id", "input", b"input", "parent_input_id", b"parent_input_id"]) -> None: ...

global___AttemptRetryRequest = AttemptRetryRequest

class AttemptRetryResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ATTEMPT_TOKEN_FIELD_NUMBER: builtins.int
    attempt_token: builtins.str
    def __init__(
        self,
        *,
        attempt_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["attempt_token", b"attempt_token"]) -> None: ...

global___AttemptRetryResponse = AttemptRetryResponse

class AttemptStartRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    PARENT_INPUT_ID_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    parent_input_id: builtins.str
    @property
    def input(self) -> global___FunctionPutInputsItem: ...
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        parent_input_id: builtins.str = ...,
        input: global___FunctionPutInputsItem | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["input", b"input"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id", "input", b"input", "parent_input_id", b"parent_input_id"]) -> None: ...

global___AttemptStartRequest = AttemptStartRequest

class AttemptStartResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ATTEMPT_TOKEN_FIELD_NUMBER: builtins.int
    RETRY_POLICY_FIELD_NUMBER: builtins.int
    attempt_token: builtins.str
    @property
    def retry_policy(self) -> global___FunctionRetryPolicy: ...
    def __init__(
        self,
        *,
        attempt_token: builtins.str = ...,
        retry_policy: global___FunctionRetryPolicy | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["retry_policy", b"retry_policy"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["attempt_token", b"attempt_token", "retry_policy", b"retry_policy"]) -> None: ...

global___AttemptStartResponse = AttemptStartResponse

class AutoscalerSettings(google.protobuf.message.Message):
    """A collection of user-configurable settings for Function autoscaling
    These are used for static configuration and for dynamic autoscaler updates
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MIN_CONTAINERS_FIELD_NUMBER: builtins.int
    MAX_CONTAINERS_FIELD_NUMBER: builtins.int
    BUFFER_CONTAINERS_FIELD_NUMBER: builtins.int
    SCALEUP_WINDOW_FIELD_NUMBER: builtins.int
    SCALEDOWN_WINDOW_FIELD_NUMBER: builtins.int
    min_containers: builtins.int
    """Minimum containers when scale-to-zero is not deisired; pka "keep_warm" or "warm_pool_size" """
    max_containers: builtins.int
    """Limit on the number of containers that can be running for each Function; pka "concurrency_limit" """
    buffer_containers: builtins.int
    """Additional container to spin up when Function is active"""
    scaleup_window: builtins.int
    """Currently unused; a placeholder in case we decide to expose scaleup control to users"""
    scaledown_window: builtins.int
    """Maximum amount of time a container can be idle before being scaled down, in seconds; pka "container_idle_timeout" """
    def __init__(
        self,
        *,
        min_containers: builtins.int | None = ...,
        max_containers: builtins.int | None = ...,
        buffer_containers: builtins.int | None = ...,
        scaleup_window: builtins.int | None = ...,
        scaledown_window: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_buffer_containers", b"_buffer_containers", "_max_containers", b"_max_containers", "_min_containers", b"_min_containers", "_scaledown_window", b"_scaledown_window", "_scaleup_window", b"_scaleup_window", "buffer_containers", b"buffer_containers", "max_containers", b"max_containers", "min_containers", b"min_containers", "scaledown_window", b"scaledown_window", "scaleup_window", b"scaleup_window"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_buffer_containers", b"_buffer_containers", "_max_containers", b"_max_containers", "_min_containers", b"_min_containers", "_scaledown_window", b"_scaledown_window", "_scaleup_window", b"_scaleup_window", "buffer_containers", b"buffer_containers", "max_containers", b"max_containers", "min_containers", b"min_containers", "scaledown_window", b"scaledown_window", "scaleup_window", b"scaleup_window"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_buffer_containers", b"_buffer_containers"]) -> typing_extensions.Literal["buffer_containers"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_max_containers", b"_max_containers"]) -> typing_extensions.Literal["max_containers"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_min_containers", b"_min_containers"]) -> typing_extensions.Literal["min_containers"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_scaledown_window", b"_scaledown_window"]) -> typing_extensions.Literal["scaledown_window"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_scaleup_window", b"_scaleup_window"]) -> typing_extensions.Literal["scaleup_window"] | None: ...

global___AutoscalerSettings = AutoscalerSettings

class BaseImage(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMAGE_ID_FIELD_NUMBER: builtins.int
    DOCKER_TAG_FIELD_NUMBER: builtins.int
    image_id: builtins.str
    docker_tag: builtins.str
    def __init__(
        self,
        *,
        image_id: builtins.str = ...,
        docker_tag: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["docker_tag", b"docker_tag", "image_id", b"image_id"]) -> None: ...

global___BaseImage = BaseImage

class BlobCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONTENT_MD5_FIELD_NUMBER: builtins.int
    CONTENT_SHA256_BASE64_FIELD_NUMBER: builtins.int
    CONTENT_LENGTH_FIELD_NUMBER: builtins.int
    content_md5: builtins.str
    """TODO(erikbern): how are these garbage collected?
    Shouldn't they belong to an app?
    """
    content_sha256_base64: builtins.str
    content_length: builtins.int
    def __init__(
        self,
        *,
        content_md5: builtins.str = ...,
        content_sha256_base64: builtins.str = ...,
        content_length: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["content_length", b"content_length", "content_md5", b"content_md5", "content_sha256_base64", b"content_sha256_base64"]) -> None: ...

global___BlobCreateRequest = BlobCreateRequest

class BlobCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOB_ID_FIELD_NUMBER: builtins.int
    UPLOAD_URL_FIELD_NUMBER: builtins.int
    MULTIPART_FIELD_NUMBER: builtins.int
    blob_id: builtins.str
    upload_url: builtins.str
    @property
    def multipart(self) -> global___MultiPartUpload: ...
    def __init__(
        self,
        *,
        blob_id: builtins.str = ...,
        upload_url: builtins.str = ...,
        multipart: global___MultiPartUpload | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["multipart", b"multipart", "upload_type_oneof", b"upload_type_oneof", "upload_url", b"upload_url"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["blob_id", b"blob_id", "multipart", b"multipart", "upload_type_oneof", b"upload_type_oneof", "upload_url", b"upload_url"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["upload_type_oneof", b"upload_type_oneof"]) -> typing_extensions.Literal["upload_url", "multipart"] | None: ...

global___BlobCreateResponse = BlobCreateResponse

class BlobGetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BLOB_ID_FIELD_NUMBER: builtins.int
    blob_id: builtins.str
    def __init__(
        self,
        *,
        blob_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["blob_id", b"blob_id"]) -> None: ...

global___BlobGetRequest = BlobGetRequest

class BlobGetResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DOWNLOAD_URL_FIELD_NUMBER: builtins.int
    download_url: builtins.str
    def __init__(
        self,
        *,
        download_url: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["download_url", b"download_url"]) -> None: ...

global___BlobGetResponse = BlobGetResponse

class BuildFunction(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEFINITION_FIELD_NUMBER: builtins.int
    GLOBALS_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    definition: builtins.str
    globals: builtins.bytes
    @property
    def input(self) -> global___FunctionInput: ...
    def __init__(
        self,
        *,
        definition: builtins.str = ...,
        globals: builtins.bytes = ...,
        input: global___FunctionInput | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["input", b"input"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["definition", b"definition", "globals", b"globals", "input", b"input"]) -> None: ...

global___BuildFunction = BuildFunction

class CancelInputEvent(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_IDS_FIELD_NUMBER: builtins.int
    TERMINATE_CONTAINERS_FIELD_NUMBER: builtins.int
    @property
    def input_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    terminate_containers: builtins.bool
    def __init__(
        self,
        *,
        input_ids: collections.abc.Iterable[builtins.str] | None = ...,
        terminate_containers: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["input_ids", b"input_ids", "terminate_containers", b"terminate_containers"]) -> None: ...

global___CancelInputEvent = CancelInputEvent

class CheckpointInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CHECKSUM_FIELD_NUMBER: builtins.int
    STATUS_FIELD_NUMBER: builtins.int
    CHECKPOINT_ID_FIELD_NUMBER: builtins.int
    RUNTIME_FINGERPRINT_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    CHECKSUM_IS_FILE_INDEX_FIELD_NUMBER: builtins.int
    ORIGINAL_TASK_ID_FIELD_NUMBER: builtins.int
    RUNSC_RUNTIME_VERSION_FIELD_NUMBER: builtins.int
    checksum: builtins.str
    status: global___CheckpointStatus.ValueType
    checkpoint_id: builtins.str
    runtime_fingerprint: builtins.str
    size: builtins.int
    checksum_is_file_index: builtins.bool
    original_task_id: builtins.str
    runsc_runtime_version: builtins.str
    def __init__(
        self,
        *,
        checksum: builtins.str = ...,
        status: global___CheckpointStatus.ValueType = ...,
        checkpoint_id: builtins.str = ...,
        runtime_fingerprint: builtins.str = ...,
        size: builtins.int = ...,
        checksum_is_file_index: builtins.bool = ...,
        original_task_id: builtins.str = ...,
        runsc_runtime_version: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["checkpoint_id", b"checkpoint_id", "checksum", b"checksum", "checksum_is_file_index", b"checksum_is_file_index", "original_task_id", b"original_task_id", "runsc_runtime_version", b"runsc_runtime_version", "runtime_fingerprint", b"runtime_fingerprint", "size", b"size", "status", b"status"]) -> None: ...

global___CheckpointInfo = CheckpointInfo

class ClassCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    EXISTING_CLASS_ID_FIELD_NUMBER: builtins.int
    METHODS_FIELD_NUMBER: builtins.int
    ONLY_CLASS_FUNCTION_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    existing_class_id: builtins.str
    @property
    def methods(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ClassMethod]: ...
    only_class_function: builtins.bool
    """True starting with 0.67.x clients, which don't create method placeholder functions"""
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        existing_class_id: builtins.str = ...,
        methods: collections.abc.Iterable[global___ClassMethod] | None = ...,
        only_class_function: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "existing_class_id", b"existing_class_id", "methods", b"methods", "only_class_function", b"only_class_function"]) -> None: ...

global___ClassCreateRequest = ClassCreateRequest

class ClassCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLASS_ID_FIELD_NUMBER: builtins.int
    HANDLE_METADATA_FIELD_NUMBER: builtins.int
    class_id: builtins.str
    @property
    def handle_metadata(self) -> global___ClassHandleMetadata: ...
    def __init__(
        self,
        *,
        class_id: builtins.str = ...,
        handle_metadata: global___ClassHandleMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["handle_metadata", b"handle_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["class_id", b"class_id", "handle_metadata", b"handle_metadata"]) -> None: ...

global___ClassCreateResponse = ClassCreateResponse

class ClassGetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_NAME_FIELD_NUMBER: builtins.int
    OBJECT_TAG_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    ONLY_CLASS_FUNCTION_FIELD_NUMBER: builtins.int
    app_name: builtins.str
    object_tag: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    only_class_function: builtins.bool
    """True starting with 0.67.x clients, which don't create method placeholder functions"""
    def __init__(
        self,
        *,
        app_name: builtins.str = ...,
        object_tag: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
        only_class_function: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_name", b"app_name", "environment_name", b"environment_name", "namespace", b"namespace", "object_tag", b"object_tag", "only_class_function", b"only_class_function"]) -> None: ...

global___ClassGetRequest = ClassGetRequest

class ClassGetResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLASS_ID_FIELD_NUMBER: builtins.int
    HANDLE_METADATA_FIELD_NUMBER: builtins.int
    SERVER_WARNINGS_FIELD_NUMBER: builtins.int
    class_id: builtins.str
    @property
    def handle_metadata(self) -> global___ClassHandleMetadata: ...
    @property
    def server_warnings(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Warning]: ...
    def __init__(
        self,
        *,
        class_id: builtins.str = ...,
        handle_metadata: global___ClassHandleMetadata | None = ...,
        server_warnings: collections.abc.Iterable[global___Warning] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["handle_metadata", b"handle_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["class_id", b"class_id", "handle_metadata", b"handle_metadata", "server_warnings", b"server_warnings"]) -> None: ...

global___ClassGetResponse = ClassGetResponse

class ClassHandleMetadata(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    METHODS_FIELD_NUMBER: builtins.int
    CLASS_FUNCTION_ID_FIELD_NUMBER: builtins.int
    CLASS_FUNCTION_METADATA_FIELD_NUMBER: builtins.int
    @property
    def methods(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ClassMethod]: ...
    class_function_id: builtins.str
    @property
    def class_function_metadata(self) -> global___FunctionHandleMetadata: ...
    def __init__(
        self,
        *,
        methods: collections.abc.Iterable[global___ClassMethod] | None = ...,
        class_function_id: builtins.str = ...,
        class_function_metadata: global___FunctionHandleMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["class_function_metadata", b"class_function_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["class_function_id", b"class_function_id", "class_function_metadata", b"class_function_metadata", "methods", b"methods"]) -> None: ...

global___ClassHandleMetadata = ClassHandleMetadata

class ClassMethod(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_NAME_FIELD_NUMBER: builtins.int
    FUNCTION_ID_FIELD_NUMBER: builtins.int
    FUNCTION_HANDLE_METADATA_FIELD_NUMBER: builtins.int
    function_name: builtins.str
    function_id: builtins.str
    @property
    def function_handle_metadata(self) -> global___FunctionHandleMetadata:
        """Class methods need to hydrate all functions on the class"""
    def __init__(
        self,
        *,
        function_name: builtins.str = ...,
        function_id: builtins.str = ...,
        function_handle_metadata: global___FunctionHandleMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["function_handle_metadata", b"function_handle_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_handle_metadata", b"function_handle_metadata", "function_id", b"function_id", "function_name", b"function_name"]) -> None: ...

global___ClassMethod = ClassMethod

class ClassParameterInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _ParameterSerializationFormat:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ParameterSerializationFormatEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ClassParameterInfo._ParameterSerializationFormat.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        PARAM_SERIALIZATION_FORMAT_UNSPECIFIED: ClassParameterInfo._ParameterSerializationFormat.ValueType  # 0
        PARAM_SERIALIZATION_FORMAT_PICKLE: ClassParameterInfo._ParameterSerializationFormat.ValueType  # 1
        """legacy format - pickle of (args, kwargs) tuple"""
        PARAM_SERIALIZATION_FORMAT_PROTO: ClassParameterInfo._ParameterSerializationFormat.ValueType  # 2
        """new format using api.FunctionParameterSet"""

    class ParameterSerializationFormat(_ParameterSerializationFormat, metaclass=_ParameterSerializationFormatEnumTypeWrapper): ...
    PARAM_SERIALIZATION_FORMAT_UNSPECIFIED: ClassParameterInfo.ParameterSerializationFormat.ValueType  # 0
    PARAM_SERIALIZATION_FORMAT_PICKLE: ClassParameterInfo.ParameterSerializationFormat.ValueType  # 1
    """legacy format - pickle of (args, kwargs) tuple"""
    PARAM_SERIALIZATION_FORMAT_PROTO: ClassParameterInfo.ParameterSerializationFormat.ValueType  # 2
    """new format using api.FunctionParameterSet"""

    FORMAT_FIELD_NUMBER: builtins.int
    SCHEMA_FIELD_NUMBER: builtins.int
    format: global___ClassParameterInfo.ParameterSerializationFormat.ValueType
    @property
    def schema(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ClassParameterSpec]:
        """only set for PARAM_SERIALIZATION_FORMAT_PROTO"""
    def __init__(
        self,
        *,
        format: global___ClassParameterInfo.ParameterSerializationFormat.ValueType = ...,
        schema: collections.abc.Iterable[global___ClassParameterSpec] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["format", b"format", "schema", b"schema"]) -> None: ...

global___ClassParameterInfo = ClassParameterInfo

class ClassParameterSet(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PARAMETERS_FIELD_NUMBER: builtins.int
    @property
    def parameters(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ClassParameterValue]:
        """NOTE: adding additional *fields* here can invalidate function lookups
         since we use the serialized message as the bound function identifier
         for parameter-bound classes. Modify with *caution*
        """
    def __init__(
        self,
        *,
        parameters: collections.abc.Iterable[global___ClassParameterValue] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["parameters", b"parameters"]) -> None: ...

global___ClassParameterSet = ClassParameterSet

class ClassParameterSpec(google.protobuf.message.Message):
    """TODO: rename into NamedPayloadType or similar"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    HAS_DEFAULT_FIELD_NUMBER: builtins.int
    STRING_DEFAULT_FIELD_NUMBER: builtins.int
    INT_DEFAULT_FIELD_NUMBER: builtins.int
    PICKLE_DEFAULT_FIELD_NUMBER: builtins.int
    BYTES_DEFAULT_FIELD_NUMBER: builtins.int
    BOOL_DEFAULT_FIELD_NUMBER: builtins.int
    FULL_TYPE_FIELD_NUMBER: builtins.int
    name: builtins.str
    type: global___ParameterType.ValueType
    """TODO: deprecate - use full_type instead"""
    has_default: builtins.bool
    string_default: builtins.str
    """Default *values* are only registered for class parameters"""
    int_default: builtins.int
    pickle_default: builtins.bytes
    bytes_default: builtins.bytes
    bool_default: builtins.bool
    @property
    def full_type(self) -> global___GenericPayloadType:
        """supersedes `type`"""
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        type: global___ParameterType.ValueType = ...,
        has_default: builtins.bool = ...,
        string_default: builtins.str = ...,
        int_default: builtins.int = ...,
        pickle_default: builtins.bytes = ...,
        bytes_default: builtins.bytes = ...,
        bool_default: builtins.bool = ...,
        full_type: global___GenericPayloadType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["bool_default", b"bool_default", "bytes_default", b"bytes_default", "default_oneof", b"default_oneof", "full_type", b"full_type", "int_default", b"int_default", "pickle_default", b"pickle_default", "string_default", b"string_default"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["bool_default", b"bool_default", "bytes_default", b"bytes_default", "default_oneof", b"default_oneof", "full_type", b"full_type", "has_default", b"has_default", "int_default", b"int_default", "name", b"name", "pickle_default", b"pickle_default", "string_default", b"string_default", "type", b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["default_oneof", b"default_oneof"]) -> typing_extensions.Literal["string_default", "int_default", "pickle_default", "bytes_default", "bool_default"] | None: ...

global___ClassParameterSpec = ClassParameterSpec

class ClassParameterValue(google.protobuf.message.Message):
    """TODO: rename into NamedPayloadValue"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    STRING_VALUE_FIELD_NUMBER: builtins.int
    INT_VALUE_FIELD_NUMBER: builtins.int
    PICKLE_VALUE_FIELD_NUMBER: builtins.int
    BYTES_VALUE_FIELD_NUMBER: builtins.int
    BOOL_VALUE_FIELD_NUMBER: builtins.int
    name: builtins.str
    """NOTE: adding additional *fields* here can invalidate function lookups
     since we use the serialized message as the bound function identifier
     for parameter-bound classes. Modify with *caution*
    """
    type: global___ParameterType.ValueType
    string_value: builtins.str
    int_value: builtins.int
    pickle_value: builtins.bytes
    bytes_value: builtins.bytes
    bool_value: builtins.bool
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        type: global___ParameterType.ValueType = ...,
        string_value: builtins.str = ...,
        int_value: builtins.int = ...,
        pickle_value: builtins.bytes = ...,
        bytes_value: builtins.bytes = ...,
        bool_value: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["bool_value", b"bool_value", "bytes_value", b"bytes_value", "int_value", b"int_value", "pickle_value", b"pickle_value", "string_value", b"string_value", "value_oneof", b"value_oneof"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["bool_value", b"bool_value", "bytes_value", b"bytes_value", "int_value", b"int_value", "name", b"name", "pickle_value", b"pickle_value", "string_value", b"string_value", "type", b"type", "value_oneof", b"value_oneof"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["value_oneof", b"value_oneof"]) -> typing_extensions.Literal["string_value", "int_value", "pickle_value", "bytes_value", "bool_value"] | None: ...

global___ClassParameterValue = ClassParameterValue

class ClientHelloResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WARNING_FIELD_NUMBER: builtins.int
    IMAGE_BUILDER_VERSION_FIELD_NUMBER: builtins.int
    SERVER_WARNINGS_FIELD_NUMBER: builtins.int
    warning: builtins.str
    image_builder_version: builtins.str
    """Deprecated, no longer used in client"""
    @property
    def server_warnings(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Warning]: ...
    def __init__(
        self,
        *,
        warning: builtins.str = ...,
        image_builder_version: builtins.str = ...,
        server_warnings: collections.abc.Iterable[global___Warning] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["image_builder_version", b"image_builder_version", "server_warnings", b"server_warnings", "warning", b"warning"]) -> None: ...

global___ClientHelloResponse = ClientHelloResponse

class CloudBucketMount(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _BucketType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _BucketTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[CloudBucketMount._BucketType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSPECIFIED: CloudBucketMount._BucketType.ValueType  # 0
        S3: CloudBucketMount._BucketType.ValueType  # 1
        R2: CloudBucketMount._BucketType.ValueType  # 2
        GCP: CloudBucketMount._BucketType.ValueType  # 3

    class BucketType(_BucketType, metaclass=_BucketTypeEnumTypeWrapper): ...
    UNSPECIFIED: CloudBucketMount.BucketType.ValueType  # 0
    S3: CloudBucketMount.BucketType.ValueType  # 1
    R2: CloudBucketMount.BucketType.ValueType  # 2
    GCP: CloudBucketMount.BucketType.ValueType  # 3

    BUCKET_NAME_FIELD_NUMBER: builtins.int
    MOUNT_PATH_FIELD_NUMBER: builtins.int
    CREDENTIALS_SECRET_ID_FIELD_NUMBER: builtins.int
    READ_ONLY_FIELD_NUMBER: builtins.int
    BUCKET_TYPE_FIELD_NUMBER: builtins.int
    REQUESTER_PAYS_FIELD_NUMBER: builtins.int
    BUCKET_ENDPOINT_URL_FIELD_NUMBER: builtins.int
    KEY_PREFIX_FIELD_NUMBER: builtins.int
    OIDC_AUTH_ROLE_ARN_FIELD_NUMBER: builtins.int
    bucket_name: builtins.str
    mount_path: builtins.str
    credentials_secret_id: builtins.str
    read_only: builtins.bool
    bucket_type: global___CloudBucketMount.BucketType.ValueType
    requester_pays: builtins.bool
    bucket_endpoint_url: builtins.str
    key_prefix: builtins.str
    oidc_auth_role_arn: builtins.str
    def __init__(
        self,
        *,
        bucket_name: builtins.str = ...,
        mount_path: builtins.str = ...,
        credentials_secret_id: builtins.str = ...,
        read_only: builtins.bool = ...,
        bucket_type: global___CloudBucketMount.BucketType.ValueType = ...,
        requester_pays: builtins.bool = ...,
        bucket_endpoint_url: builtins.str | None = ...,
        key_prefix: builtins.str | None = ...,
        oidc_auth_role_arn: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_bucket_endpoint_url", b"_bucket_endpoint_url", "_key_prefix", b"_key_prefix", "_oidc_auth_role_arn", b"_oidc_auth_role_arn", "bucket_endpoint_url", b"bucket_endpoint_url", "key_prefix", b"key_prefix", "oidc_auth_role_arn", b"oidc_auth_role_arn"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_bucket_endpoint_url", b"_bucket_endpoint_url", "_key_prefix", b"_key_prefix", "_oidc_auth_role_arn", b"_oidc_auth_role_arn", "bucket_endpoint_url", b"bucket_endpoint_url", "bucket_name", b"bucket_name", "bucket_type", b"bucket_type", "credentials_secret_id", b"credentials_secret_id", "key_prefix", b"key_prefix", "mount_path", b"mount_path", "oidc_auth_role_arn", b"oidc_auth_role_arn", "read_only", b"read_only", "requester_pays", b"requester_pays"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_bucket_endpoint_url", b"_bucket_endpoint_url"]) -> typing_extensions.Literal["bucket_endpoint_url"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_key_prefix", b"_key_prefix"]) -> typing_extensions.Literal["key_prefix"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_oidc_auth_role_arn", b"_oidc_auth_role_arn"]) -> typing_extensions.Literal["oidc_auth_role_arn"] | None: ...

global___CloudBucketMount = CloudBucketMount

class ClusterGetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLUSTER_ID_FIELD_NUMBER: builtins.int
    cluster_id: builtins.str
    def __init__(
        self,
        *,
        cluster_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["cluster_id", b"cluster_id"]) -> None: ...

global___ClusterGetRequest = ClusterGetRequest

class ClusterGetResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLUSTER_FIELD_NUMBER: builtins.int
    @property
    def cluster(self) -> global___ClusterStats: ...
    def __init__(
        self,
        *,
        cluster: global___ClusterStats | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["cluster", b"cluster"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["cluster", b"cluster"]) -> None: ...

global___ClusterGetResponse = ClusterGetResponse

class ClusterListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name"]) -> None: ...

global___ClusterListRequest = ClusterListRequest

class ClusterListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLUSTERS_FIELD_NUMBER: builtins.int
    @property
    def clusters(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ClusterStats]: ...
    def __init__(
        self,
        *,
        clusters: collections.abc.Iterable[global___ClusterStats] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["clusters", b"clusters"]) -> None: ...

global___ClusterListResponse = ClusterListResponse

class ClusterStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    TASK_IDS_FIELD_NUMBER: builtins.int
    CLUSTER_ID_FIELD_NUMBER: builtins.int
    STARTED_AT_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    @property
    def task_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    cluster_id: builtins.str
    started_at: builtins.float
    """Defined as start time of the first task in the cluster"""
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        task_ids: collections.abc.Iterable[builtins.str] | None = ...,
        cluster_id: builtins.str = ...,
        started_at: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "cluster_id", b"cluster_id", "started_at", b"started_at", "task_ids", b"task_ids"]) -> None: ...

global___ClusterStats = ClusterStats

class CommitInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VCS_FIELD_NUMBER: builtins.int
    BRANCH_FIELD_NUMBER: builtins.int
    COMMIT_HASH_FIELD_NUMBER: builtins.int
    COMMIT_TIMESTAMP_FIELD_NUMBER: builtins.int
    DIRTY_FIELD_NUMBER: builtins.int
    AUTHOR_NAME_FIELD_NUMBER: builtins.int
    AUTHOR_EMAIL_FIELD_NUMBER: builtins.int
    REPO_URL_FIELD_NUMBER: builtins.int
    vcs: builtins.str
    """Only git is supported for now"""
    branch: builtins.str
    commit_hash: builtins.str
    commit_timestamp: builtins.int
    dirty: builtins.bool
    author_name: builtins.str
    author_email: builtins.str
    repo_url: builtins.str
    def __init__(
        self,
        *,
        vcs: builtins.str = ...,
        branch: builtins.str = ...,
        commit_hash: builtins.str = ...,
        commit_timestamp: builtins.int = ...,
        dirty: builtins.bool = ...,
        author_name: builtins.str = ...,
        author_email: builtins.str = ...,
        repo_url: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["author_email", b"author_email", "author_name", b"author_name", "branch", b"branch", "commit_hash", b"commit_hash", "commit_timestamp", b"commit_timestamp", "dirty", b"dirty", "repo_url", b"repo_url", "vcs", b"vcs"]) -> None: ...

global___CommitInfo = CommitInfo

class ContainerArguments(google.protobuf.message.Message):
    """This is used to pass data from the worker to the container"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class TracingContextEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    TASK_ID_FIELD_NUMBER: builtins.int
    FUNCTION_ID_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    FUNCTION_DEF_FIELD_NUMBER: builtins.int
    PROXY_INFO_FIELD_NUMBER: builtins.int
    TRACING_CONTEXT_FIELD_NUMBER: builtins.int
    SERIALIZED_PARAMS_FIELD_NUMBER: builtins.int
    RUNTIME_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    CHECKPOINT_ID_FIELD_NUMBER: builtins.int
    APP_LAYOUT_FIELD_NUMBER: builtins.int
    task_id: builtins.str
    function_id: builtins.str
    app_id: builtins.str
    @property
    def function_def(self) -> global___Function: ...
    @property
    def proxy_info(self) -> global___ProxyInfo: ...
    @property
    def tracing_context(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    serialized_params: builtins.bytes
    runtime: builtins.str
    environment_name: builtins.str
    checkpoint_id: builtins.str
    @property
    def app_layout(self) -> global___AppLayout: ...
    def __init__(
        self,
        *,
        task_id: builtins.str = ...,
        function_id: builtins.str = ...,
        app_id: builtins.str = ...,
        function_def: global___Function | None = ...,
        proxy_info: global___ProxyInfo | None = ...,
        tracing_context: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        serialized_params: builtins.bytes = ...,
        runtime: builtins.str = ...,
        environment_name: builtins.str = ...,
        checkpoint_id: builtins.str | None = ...,
        app_layout: global___AppLayout | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_checkpoint_id", b"_checkpoint_id", "app_layout", b"app_layout", "checkpoint_id", b"checkpoint_id", "function_def", b"function_def", "proxy_info", b"proxy_info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_checkpoint_id", b"_checkpoint_id", "app_id", b"app_id", "app_layout", b"app_layout", "checkpoint_id", b"checkpoint_id", "environment_name", b"environment_name", "function_def", b"function_def", "function_id", b"function_id", "proxy_info", b"proxy_info", "runtime", b"runtime", "serialized_params", b"serialized_params", "task_id", b"task_id", "tracing_context", b"tracing_context"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_checkpoint_id", b"_checkpoint_id"]) -> typing_extensions.Literal["checkpoint_id"] | None: ...

global___ContainerArguments = ContainerArguments

class ContainerCheckpointRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CHECKPOINT_ID_FIELD_NUMBER: builtins.int
    checkpoint_id: builtins.str
    def __init__(
        self,
        *,
        checkpoint_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["checkpoint_id", b"checkpoint_id"]) -> None: ...

global___ContainerCheckpointRequest = ContainerCheckpointRequest

class ContainerExecGetOutputRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXEC_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    LAST_BATCH_INDEX_FIELD_NUMBER: builtins.int
    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    GET_RAW_BYTES_FIELD_NUMBER: builtins.int
    exec_id: builtins.str
    timeout: builtins.float
    last_batch_index: builtins.int
    file_descriptor: global___FileDescriptor.ValueType
    get_raw_bytes: builtins.bool
    """Old clients (up to 0.65.39) expect string output. Newer clients stream raw bytes"""
    def __init__(
        self,
        *,
        exec_id: builtins.str = ...,
        timeout: builtins.float = ...,
        last_batch_index: builtins.int = ...,
        file_descriptor: global___FileDescriptor.ValueType = ...,
        get_raw_bytes: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["exec_id", b"exec_id", "file_descriptor", b"file_descriptor", "get_raw_bytes", b"get_raw_bytes", "last_batch_index", b"last_batch_index", "timeout", b"timeout"]) -> None: ...

global___ContainerExecGetOutputRequest = ContainerExecGetOutputRequest

class ContainerExecPutInputRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXEC_ID_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    exec_id: builtins.str
    @property
    def input(self) -> global___RuntimeInputMessage: ...
    def __init__(
        self,
        *,
        exec_id: builtins.str = ...,
        input: global___RuntimeInputMessage | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["input", b"input"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["exec_id", b"exec_id", "input", b"input"]) -> None: ...

global___ContainerExecPutInputRequest = ContainerExecPutInputRequest

class ContainerExecRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TASK_ID_FIELD_NUMBER: builtins.int
    COMMAND_FIELD_NUMBER: builtins.int
    PTY_INFO_FIELD_NUMBER: builtins.int
    TERMINATE_CONTAINER_ON_EXIT_FIELD_NUMBER: builtins.int
    RUNTIME_DEBUG_FIELD_NUMBER: builtins.int
    STDOUT_OUTPUT_FIELD_NUMBER: builtins.int
    STDERR_OUTPUT_FIELD_NUMBER: builtins.int
    TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    WORKDIR_FIELD_NUMBER: builtins.int
    SECRET_IDS_FIELD_NUMBER: builtins.int
    task_id: builtins.str
    @property
    def command(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def pty_info(self) -> global___PTYInfo:
        """If pty_info is provided, open a PTY, but also this container exec is treated an
        "interactive shell" request, and it will be terminated if messages are not periodically
        sent on the stdin stream on some interval (currently 40 seconds).
        """
    terminate_container_on_exit: builtins.bool
    """Send SIGTERM to running container on exit of exec command."""
    runtime_debug: builtins.bool
    """For internal debugging use only."""
    stdout_output: global___ExecOutputOption.ValueType
    stderr_output: global___ExecOutputOption.ValueType
    timeout_secs: builtins.int
    workdir: builtins.str
    @property
    def secret_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        task_id: builtins.str = ...,
        command: collections.abc.Iterable[builtins.str] | None = ...,
        pty_info: global___PTYInfo | None = ...,
        terminate_container_on_exit: builtins.bool = ...,
        runtime_debug: builtins.bool = ...,
        stdout_output: global___ExecOutputOption.ValueType = ...,
        stderr_output: global___ExecOutputOption.ValueType = ...,
        timeout_secs: builtins.int = ...,
        workdir: builtins.str | None = ...,
        secret_ids: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_pty_info", b"_pty_info", "_workdir", b"_workdir", "pty_info", b"pty_info", "workdir", b"workdir"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_pty_info", b"_pty_info", "_workdir", b"_workdir", "command", b"command", "pty_info", b"pty_info", "runtime_debug", b"runtime_debug", "secret_ids", b"secret_ids", "stderr_output", b"stderr_output", "stdout_output", b"stdout_output", "task_id", b"task_id", "terminate_container_on_exit", b"terminate_container_on_exit", "timeout_secs", b"timeout_secs", "workdir", b"workdir"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_pty_info", b"_pty_info"]) -> typing_extensions.Literal["pty_info"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_workdir", b"_workdir"]) -> typing_extensions.Literal["workdir"] | None: ...

global___ContainerExecRequest = ContainerExecRequest

class ContainerExecResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXEC_ID_FIELD_NUMBER: builtins.int
    exec_id: builtins.str
    def __init__(
        self,
        *,
        exec_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["exec_id", b"exec_id"]) -> None: ...

global___ContainerExecResponse = ContainerExecResponse

class ContainerExecWaitRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXEC_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    exec_id: builtins.str
    timeout: builtins.float
    def __init__(
        self,
        *,
        exec_id: builtins.str = ...,
        timeout: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["exec_id", b"exec_id", "timeout", b"timeout"]) -> None: ...

global___ContainerExecWaitRequest = ContainerExecWaitRequest

class ContainerExecWaitResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXIT_CODE_FIELD_NUMBER: builtins.int
    COMPLETED_FIELD_NUMBER: builtins.int
    exit_code: builtins.int
    completed: builtins.bool
    def __init__(
        self,
        *,
        exit_code: builtins.int | None = ...,
        completed: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_exit_code", b"_exit_code", "exit_code", b"exit_code"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_exit_code", b"_exit_code", "completed", b"completed", "exit_code", b"exit_code"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_exit_code", b"_exit_code"]) -> typing_extensions.Literal["exit_code"] | None: ...

global___ContainerExecWaitResponse = ContainerExecWaitResponse

class ContainerFileCloseRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    def __init__(
        self,
        *,
        file_descriptor: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["file_descriptor", b"file_descriptor"]) -> None: ...

global___ContainerFileCloseRequest = ContainerFileCloseRequest

class ContainerFileDeleteBytesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    START_INCLUSIVE_FIELD_NUMBER: builtins.int
    END_EXCLUSIVE_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    start_inclusive: builtins.int
    end_exclusive: builtins.int
    def __init__(
        self,
        *,
        file_descriptor: builtins.str = ...,
        start_inclusive: builtins.int | None = ...,
        end_exclusive: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_end_exclusive", b"_end_exclusive", "_start_inclusive", b"_start_inclusive", "end_exclusive", b"end_exclusive", "start_inclusive", b"start_inclusive"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_end_exclusive", b"_end_exclusive", "_start_inclusive", b"_start_inclusive", "end_exclusive", b"end_exclusive", "file_descriptor", b"file_descriptor", "start_inclusive", b"start_inclusive"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_end_exclusive", b"_end_exclusive"]) -> typing_extensions.Literal["end_exclusive"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_start_inclusive", b"_start_inclusive"]) -> typing_extensions.Literal["start_inclusive"] | None: ...

global___ContainerFileDeleteBytesRequest = ContainerFileDeleteBytesRequest

class ContainerFileFlushRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    def __init__(
        self,
        *,
        file_descriptor: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["file_descriptor", b"file_descriptor"]) -> None: ...

global___ContainerFileFlushRequest = ContainerFileFlushRequest

class ContainerFileLsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    path: builtins.str
    def __init__(
        self,
        *,
        path: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["path", b"path"]) -> None: ...

global___ContainerFileLsRequest = ContainerFileLsRequest

class ContainerFileMkdirRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    MAKE_PARENTS_FIELD_NUMBER: builtins.int
    path: builtins.str
    make_parents: builtins.bool
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        make_parents: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["make_parents", b"make_parents", "path", b"path"]) -> None: ...

global___ContainerFileMkdirRequest = ContainerFileMkdirRequest

class ContainerFileOpenRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    """file descriptor is hydrated when sent from server -> worker"""
    path: builtins.str
    mode: builtins.str
    def __init__(
        self,
        *,
        file_descriptor: builtins.str | None = ...,
        path: builtins.str = ...,
        mode: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_file_descriptor", b"_file_descriptor", "file_descriptor", b"file_descriptor"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_file_descriptor", b"_file_descriptor", "file_descriptor", b"file_descriptor", "mode", b"mode", "path", b"path"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_file_descriptor", b"_file_descriptor"]) -> typing_extensions.Literal["file_descriptor"] | None: ...

global___ContainerFileOpenRequest = ContainerFileOpenRequest

class ContainerFileReadLineRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    def __init__(
        self,
        *,
        file_descriptor: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["file_descriptor", b"file_descriptor"]) -> None: ...

global___ContainerFileReadLineRequest = ContainerFileReadLineRequest

class ContainerFileReadRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    N_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    n: builtins.int
    def __init__(
        self,
        *,
        file_descriptor: builtins.str = ...,
        n: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_n", b"_n", "n", b"n"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_n", b"_n", "file_descriptor", b"file_descriptor", "n", b"n"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_n", b"_n"]) -> typing_extensions.Literal["n"] | None: ...

global___ContainerFileReadRequest = ContainerFileReadRequest

class ContainerFileRmRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    path: builtins.str
    recursive: builtins.bool
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        recursive: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["path", b"path", "recursive", b"recursive"]) -> None: ...

global___ContainerFileRmRequest = ContainerFileRmRequest

class ContainerFileSeekRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    OFFSET_FIELD_NUMBER: builtins.int
    WHENCE_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    offset: builtins.int
    whence: global___SeekWhence.ValueType
    def __init__(
        self,
        *,
        file_descriptor: builtins.str = ...,
        offset: builtins.int = ...,
        whence: global___SeekWhence.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["file_descriptor", b"file_descriptor", "offset", b"offset", "whence", b"whence"]) -> None: ...

global___ContainerFileSeekRequest = ContainerFileSeekRequest

class ContainerFileWatchRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    path: builtins.str
    recursive: builtins.bool
    timeout_secs: builtins.int
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        recursive: builtins.bool = ...,
        timeout_secs: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_timeout_secs", b"_timeout_secs", "timeout_secs", b"timeout_secs"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_timeout_secs", b"_timeout_secs", "path", b"path", "recursive", b"recursive", "timeout_secs", b"timeout_secs"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_timeout_secs", b"_timeout_secs"]) -> typing_extensions.Literal["timeout_secs"] | None: ...

global___ContainerFileWatchRequest = ContainerFileWatchRequest

class ContainerFileWriteReplaceBytesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    START_INCLUSIVE_FIELD_NUMBER: builtins.int
    END_EXCLUSIVE_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    data: builtins.bytes
    start_inclusive: builtins.int
    end_exclusive: builtins.int
    def __init__(
        self,
        *,
        file_descriptor: builtins.str = ...,
        data: builtins.bytes = ...,
        start_inclusive: builtins.int | None = ...,
        end_exclusive: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_end_exclusive", b"_end_exclusive", "_start_inclusive", b"_start_inclusive", "end_exclusive", b"end_exclusive", "start_inclusive", b"start_inclusive"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_end_exclusive", b"_end_exclusive", "_start_inclusive", b"_start_inclusive", "data", b"data", "end_exclusive", b"end_exclusive", "file_descriptor", b"file_descriptor", "start_inclusive", b"start_inclusive"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_end_exclusive", b"_end_exclusive"]) -> typing_extensions.Literal["end_exclusive"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_start_inclusive", b"_start_inclusive"]) -> typing_extensions.Literal["start_inclusive"] | None: ...

global___ContainerFileWriteReplaceBytesRequest = ContainerFileWriteReplaceBytesRequest

class ContainerFileWriteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    file_descriptor: builtins.str
    data: builtins.bytes
    def __init__(
        self,
        *,
        file_descriptor: builtins.str = ...,
        data: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "file_descriptor", b"file_descriptor"]) -> None: ...

global___ContainerFileWriteRequest = ContainerFileWriteRequest

class ContainerFilesystemExecGetOutputRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXEC_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    exec_id: builtins.str
    timeout: builtins.float
    def __init__(
        self,
        *,
        exec_id: builtins.str = ...,
        timeout: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["exec_id", b"exec_id", "timeout", b"timeout"]) -> None: ...

global___ContainerFilesystemExecGetOutputRequest = ContainerFilesystemExecGetOutputRequest

class ContainerFilesystemExecRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_OPEN_REQUEST_FIELD_NUMBER: builtins.int
    FILE_WRITE_REQUEST_FIELD_NUMBER: builtins.int
    FILE_READ_REQUEST_FIELD_NUMBER: builtins.int
    FILE_FLUSH_REQUEST_FIELD_NUMBER: builtins.int
    FILE_READ_LINE_REQUEST_FIELD_NUMBER: builtins.int
    FILE_SEEK_REQUEST_FIELD_NUMBER: builtins.int
    FILE_DELETE_BYTES_REQUEST_FIELD_NUMBER: builtins.int
    FILE_WRITE_REPLACE_BYTES_REQUEST_FIELD_NUMBER: builtins.int
    FILE_CLOSE_REQUEST_FIELD_NUMBER: builtins.int
    FILE_LS_REQUEST_FIELD_NUMBER: builtins.int
    FILE_MKDIR_REQUEST_FIELD_NUMBER: builtins.int
    FILE_RM_REQUEST_FIELD_NUMBER: builtins.int
    FILE_WATCH_REQUEST_FIELD_NUMBER: builtins.int
    TASK_ID_FIELD_NUMBER: builtins.int
    @property
    def file_open_request(self) -> global___ContainerFileOpenRequest: ...
    @property
    def file_write_request(self) -> global___ContainerFileWriteRequest: ...
    @property
    def file_read_request(self) -> global___ContainerFileReadRequest: ...
    @property
    def file_flush_request(self) -> global___ContainerFileFlushRequest: ...
    @property
    def file_read_line_request(self) -> global___ContainerFileReadLineRequest: ...
    @property
    def file_seek_request(self) -> global___ContainerFileSeekRequest: ...
    @property
    def file_delete_bytes_request(self) -> global___ContainerFileDeleteBytesRequest: ...
    @property
    def file_write_replace_bytes_request(self) -> global___ContainerFileWriteReplaceBytesRequest: ...
    @property
    def file_close_request(self) -> global___ContainerFileCloseRequest: ...
    @property
    def file_ls_request(self) -> global___ContainerFileLsRequest: ...
    @property
    def file_mkdir_request(self) -> global___ContainerFileMkdirRequest: ...
    @property
    def file_rm_request(self) -> global___ContainerFileRmRequest: ...
    @property
    def file_watch_request(self) -> global___ContainerFileWatchRequest: ...
    task_id: builtins.str
    def __init__(
        self,
        *,
        file_open_request: global___ContainerFileOpenRequest | None = ...,
        file_write_request: global___ContainerFileWriteRequest | None = ...,
        file_read_request: global___ContainerFileReadRequest | None = ...,
        file_flush_request: global___ContainerFileFlushRequest | None = ...,
        file_read_line_request: global___ContainerFileReadLineRequest | None = ...,
        file_seek_request: global___ContainerFileSeekRequest | None = ...,
        file_delete_bytes_request: global___ContainerFileDeleteBytesRequest | None = ...,
        file_write_replace_bytes_request: global___ContainerFileWriteReplaceBytesRequest | None = ...,
        file_close_request: global___ContainerFileCloseRequest | None = ...,
        file_ls_request: global___ContainerFileLsRequest | None = ...,
        file_mkdir_request: global___ContainerFileMkdirRequest | None = ...,
        file_rm_request: global___ContainerFileRmRequest | None = ...,
        file_watch_request: global___ContainerFileWatchRequest | None = ...,
        task_id: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["file_close_request", b"file_close_request", "file_delete_bytes_request", b"file_delete_bytes_request", "file_exec_request_oneof", b"file_exec_request_oneof", "file_flush_request", b"file_flush_request", "file_ls_request", b"file_ls_request", "file_mkdir_request", b"file_mkdir_request", "file_open_request", b"file_open_request", "file_read_line_request", b"file_read_line_request", "file_read_request", b"file_read_request", "file_rm_request", b"file_rm_request", "file_seek_request", b"file_seek_request", "file_watch_request", b"file_watch_request", "file_write_replace_bytes_request", b"file_write_replace_bytes_request", "file_write_request", b"file_write_request"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["file_close_request", b"file_close_request", "file_delete_bytes_request", b"file_delete_bytes_request", "file_exec_request_oneof", b"file_exec_request_oneof", "file_flush_request", b"file_flush_request", "file_ls_request", b"file_ls_request", "file_mkdir_request", b"file_mkdir_request", "file_open_request", b"file_open_request", "file_read_line_request", b"file_read_line_request", "file_read_request", b"file_read_request", "file_rm_request", b"file_rm_request", "file_seek_request", b"file_seek_request", "file_watch_request", b"file_watch_request", "file_write_replace_bytes_request", b"file_write_replace_bytes_request", "file_write_request", b"file_write_request", "task_id", b"task_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["file_exec_request_oneof", b"file_exec_request_oneof"]) -> typing_extensions.Literal["file_open_request", "file_write_request", "file_read_request", "file_flush_request", "file_read_line_request", "file_seek_request", "file_delete_bytes_request", "file_write_replace_bytes_request", "file_close_request", "file_ls_request", "file_mkdir_request", "file_rm_request", "file_watch_request"] | None: ...

global___ContainerFilesystemExecRequest = ContainerFilesystemExecRequest

class ContainerFilesystemExecResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXEC_ID_FIELD_NUMBER: builtins.int
    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    exec_id: builtins.str
    file_descriptor: builtins.str
    """only set when the request opens a new file, i.e., ContainerFileOpenRequest"""
    def __init__(
        self,
        *,
        exec_id: builtins.str = ...,
        file_descriptor: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_file_descriptor", b"_file_descriptor", "file_descriptor", b"file_descriptor"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_file_descriptor", b"_file_descriptor", "exec_id", b"exec_id", "file_descriptor", b"file_descriptor"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_file_descriptor", b"_file_descriptor"]) -> typing_extensions.Literal["file_descriptor"] | None: ...

global___ContainerFilesystemExecResponse = ContainerFilesystemExecResponse

class ContainerHeartbeatRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CANCELED_INPUTS_RETURN_OUTPUTS_FIELD_NUMBER: builtins.int
    CANCELED_INPUTS_RETURN_OUTPUTS_V2_FIELD_NUMBER: builtins.int
    canceled_inputs_return_outputs: builtins.bool
    """Bad client version."""
    canceled_inputs_return_outputs_v2: builtins.bool
    def __init__(
        self,
        *,
        canceled_inputs_return_outputs: builtins.bool = ...,
        canceled_inputs_return_outputs_v2: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["canceled_inputs_return_outputs", b"canceled_inputs_return_outputs", "canceled_inputs_return_outputs_v2", b"canceled_inputs_return_outputs_v2"]) -> None: ...

global___ContainerHeartbeatRequest = ContainerHeartbeatRequest

class ContainerHeartbeatResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CANCEL_INPUT_EVENT_FIELD_NUMBER: builtins.int
    @property
    def cancel_input_event(self) -> global___CancelInputEvent: ...
    def __init__(
        self,
        *,
        cancel_input_event: global___CancelInputEvent | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_cancel_input_event", b"_cancel_input_event", "cancel_input_event", b"cancel_input_event"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_cancel_input_event", b"_cancel_input_event", "cancel_input_event", b"cancel_input_event"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_cancel_input_event", b"_cancel_input_event"]) -> typing_extensions.Literal["cancel_input_event"] | None: ...

global___ContainerHeartbeatResponse = ContainerHeartbeatResponse

class ContainerLogRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOGS_FIELD_NUMBER: builtins.int
    @property
    def logs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TaskLogs]: ...
    def __init__(
        self,
        *,
        logs: collections.abc.Iterable[global___TaskLogs] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["logs", b"logs"]) -> None: ...

global___ContainerLogRequest = ContainerLogRequest

class ContainerStopRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TASK_ID_FIELD_NUMBER: builtins.int
    task_id: builtins.str
    def __init__(
        self,
        *,
        task_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["task_id", b"task_id"]) -> None: ...

global___ContainerStopRequest = ContainerStopRequest

class ContainerStopResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___ContainerStopResponse = ContainerStopResponse

class CustomDomainConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["name", b"name"]) -> None: ...

global___CustomDomainConfig = CustomDomainConfig

class CustomDomainInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URL_FIELD_NUMBER: builtins.int
    url: builtins.str
    def __init__(
        self,
        *,
        url: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["url", b"url"]) -> None: ...

global___CustomDomainInfo = CustomDomainInfo

class DNSRecord(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    type: global___DNSRecordType.ValueType
    name: builtins.str
    value: builtins.str
    def __init__(
        self,
        *,
        type: global___DNSRecordType.ValueType = ...,
        name: builtins.str = ...,
        value: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["name", b"name", "type", b"type", "value", b"value"]) -> None: ...

global___DNSRecord = DNSRecord

class DataChunk(google.protobuf.message.Message):
    """Chunks of data that can be streamed in and out of tasks."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FORMAT_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    DATA_BLOB_ID_FIELD_NUMBER: builtins.int
    INDEX_FIELD_NUMBER: builtins.int
    data_format: global___DataFormat.ValueType
    data: builtins.bytes
    data_blob_id: builtins.str
    index: builtins.int
    """Index of this data chunk in the stream."""
    def __init__(
        self,
        *,
        data_format: global___DataFormat.ValueType = ...,
        data: builtins.bytes = ...,
        data_blob_id: builtins.str = ...,
        index: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_format", b"data_format", "data_oneof", b"data_oneof", "index", b"index"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["data_oneof", b"data_oneof"]) -> typing_extensions.Literal["data", "data_blob_id"] | None: ...

global___DataChunk = DataChunk

class DictClearRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id"]) -> None: ...

global___DictClearRequest = DictClearRequest

class DictContainsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    KEY_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    key: builtins.bytes
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
        key: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id", "key", b"key"]) -> None: ...

global___DictContainsRequest = DictContainsRequest

class DictContainsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FOUND_FIELD_NUMBER: builtins.int
    found: builtins.bool
    def __init__(
        self,
        *,
        found: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["found", b"found"]) -> None: ...

global___DictContainsResponse = DictContainsResponse

class DictContentsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    KEYS_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    keys: builtins.bool
    """Setting these to True will populate the corresponding field in the response, otherwise it will be null
    This lets us support the keys/values/items SDK API through one RPC without unnecessary data transfer
    """
    values: builtins.bool
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
        keys: builtins.bool = ...,
        values: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id", "keys", b"keys", "values", b"values"]) -> None: ...

global___DictContentsRequest = DictContentsRequest

class DictDeleteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id"]) -> None: ...

global___DictDeleteRequest = DictDeleteRequest

class DictEntry(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    key: builtins.bytes
    value: builtins.bytes
    def __init__(
        self,
        *,
        key: builtins.bytes = ...,
        value: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

global___DictEntry = DictEntry

class DictGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEPLOYMENT_NAME_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    deployment_name: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    @property
    def data(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DictEntry]: ...
    def __init__(
        self,
        *,
        deployment_name: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
        data: collections.abc.Iterable[global___DictEntry] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "deployment_name", b"deployment_name", "environment_name", b"environment_name", "namespace", b"namespace", "object_creation_type", b"object_creation_type"]) -> None: ...

global___DictGetOrCreateRequest = DictGetOrCreateRequest

class DictGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id"]) -> None: ...

global___DictGetOrCreateResponse = DictGetOrCreateResponse

class DictGetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    KEY_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    key: builtins.bytes
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
        key: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id", "key", b"key"]) -> None: ...

global___DictGetRequest = DictGetRequest

class DictGetResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FOUND_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    found: builtins.bool
    value: builtins.bytes
    def __init__(
        self,
        *,
        found: builtins.bool = ...,
        value: builtins.bytes | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_value", b"_value", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_value", b"_value", "found", b"found", "value", b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_value", b"_value"]) -> typing_extensions.Literal["value"] | None: ...

global___DictGetResponse = DictGetResponse

class DictHeartbeatRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id"]) -> None: ...

global___DictHeartbeatRequest = DictHeartbeatRequest

class DictLenRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id"]) -> None: ...

global___DictLenRequest = DictLenRequest

class DictLenResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LEN_FIELD_NUMBER: builtins.int
    len: builtins.int
    def __init__(
        self,
        *,
        len: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["len", b"len"]) -> None: ...

global___DictLenResponse = DictLenResponse

class DictListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name"]) -> None: ...

global___DictListRequest = DictListRequest

class DictListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class DictInfo(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        NAME_FIELD_NUMBER: builtins.int
        CREATED_AT_FIELD_NUMBER: builtins.int
        name: builtins.str
        created_at: builtins.float
        def __init__(
            self,
            *,
            name: builtins.str = ...,
            created_at: builtins.float = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["created_at", b"created_at", "name", b"name"]) -> None: ...

    DICTS_FIELD_NUMBER: builtins.int
    @property
    def dicts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DictListResponse.DictInfo]: ...
    def __init__(
        self,
        *,
        dicts: collections.abc.Iterable[global___DictListResponse.DictInfo] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dicts", b"dicts"]) -> None: ...

global___DictListResponse = DictListResponse

class DictPopRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    KEY_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    key: builtins.bytes
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
        key: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id", "key", b"key"]) -> None: ...

global___DictPopRequest = DictPopRequest

class DictPopResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FOUND_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    found: builtins.bool
    value: builtins.bytes
    def __init__(
        self,
        *,
        found: builtins.bool = ...,
        value: builtins.bytes | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_value", b"_value", "value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_value", b"_value", "found", b"found", "value", b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_value", b"_value"]) -> typing_extensions.Literal["value"] | None: ...

global___DictPopResponse = DictPopResponse

class DictUpdateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DICT_ID_FIELD_NUMBER: builtins.int
    UPDATES_FIELD_NUMBER: builtins.int
    IF_NOT_EXISTS_FIELD_NUMBER: builtins.int
    dict_id: builtins.str
    @property
    def updates(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DictEntry]: ...
    if_not_exists: builtins.bool
    def __init__(
        self,
        *,
        dict_id: builtins.str = ...,
        updates: collections.abc.Iterable[global___DictEntry] | None = ...,
        if_not_exists: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dict_id", b"dict_id", "if_not_exists", b"if_not_exists", "updates", b"updates"]) -> None: ...

global___DictUpdateRequest = DictUpdateRequest

class DictUpdateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CREATED_FIELD_NUMBER: builtins.int
    created: builtins.bool
    def __init__(
        self,
        *,
        created: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["created", b"created"]) -> None: ...

global___DictUpdateResponse = DictUpdateResponse

class Domain(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DOMAIN_ID_FIELD_NUMBER: builtins.int
    DOMAIN_NAME_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    CERTIFICATE_STATUS_FIELD_NUMBER: builtins.int
    DNS_RECORDS_FIELD_NUMBER: builtins.int
    domain_id: builtins.str
    domain_name: builtins.str
    created_at: builtins.float
    certificate_status: global___CertificateStatus.ValueType
    @property
    def dns_records(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DNSRecord]: ...
    def __init__(
        self,
        *,
        domain_id: builtins.str = ...,
        domain_name: builtins.str = ...,
        created_at: builtins.float = ...,
        certificate_status: global___CertificateStatus.ValueType = ...,
        dns_records: collections.abc.Iterable[global___DNSRecord] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["certificate_status", b"certificate_status", "created_at", b"created_at", "dns_records", b"dns_records", "domain_id", b"domain_id", "domain_name", b"domain_name"]) -> None: ...

global___Domain = Domain

class DomainCertificateVerifyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DOMAIN_ID_FIELD_NUMBER: builtins.int
    domain_id: builtins.str
    def __init__(
        self,
        *,
        domain_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["domain_id", b"domain_id"]) -> None: ...

global___DomainCertificateVerifyRequest = DomainCertificateVerifyRequest

class DomainCertificateVerifyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DOMAIN_FIELD_NUMBER: builtins.int
    @property
    def domain(self) -> global___Domain: ...
    def __init__(
        self,
        *,
        domain: global___Domain | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["domain", b"domain"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["domain", b"domain"]) -> None: ...

global___DomainCertificateVerifyResponse = DomainCertificateVerifyResponse

class DomainCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DOMAIN_NAME_FIELD_NUMBER: builtins.int
    domain_name: builtins.str
    def __init__(
        self,
        *,
        domain_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["domain_name", b"domain_name"]) -> None: ...

global___DomainCreateRequest = DomainCreateRequest

class DomainCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DOMAIN_ID_FIELD_NUMBER: builtins.int
    DNS_RECORDS_FIELD_NUMBER: builtins.int
    domain_id: builtins.str
    @property
    def dns_records(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DNSRecord]: ...
    def __init__(
        self,
        *,
        domain_id: builtins.str = ...,
        dns_records: collections.abc.Iterable[global___DNSRecord] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dns_records", b"dns_records", "domain_id", b"domain_id"]) -> None: ...

global___DomainCreateResponse = DomainCreateResponse

class DomainListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___DomainListRequest = DomainListRequest

class DomainListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DOMAINS_FIELD_NUMBER: builtins.int
    @property
    def domains(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Domain]: ...
    def __init__(
        self,
        *,
        domains: collections.abc.Iterable[global___Domain] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["domains", b"domains"]) -> None: ...

global___DomainListResponse = DomainListResponse

class EnvironmentCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["name", b"name"]) -> None: ...

global___EnvironmentCreateRequest = EnvironmentCreateRequest

class EnvironmentDeleteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["name", b"name"]) -> None: ...

global___EnvironmentDeleteRequest = EnvironmentDeleteRequest

class EnvironmentGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEPLOYMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    deployment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    def __init__(
        self,
        *,
        deployment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["deployment_name", b"deployment_name", "object_creation_type", b"object_creation_type"]) -> None: ...

global___EnvironmentGetOrCreateRequest = EnvironmentGetOrCreateRequest

class EnvironmentGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_ID_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    environment_id: builtins.str
    @property
    def metadata(self) -> global___EnvironmentMetadata: ...
    def __init__(
        self,
        *,
        environment_id: builtins.str = ...,
        metadata: global___EnvironmentMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["metadata", b"metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_id", b"environment_id", "metadata", b"metadata"]) -> None: ...

global___EnvironmentGetOrCreateResponse = EnvironmentGetOrCreateResponse

class EnvironmentListItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    WEBHOOK_SUFFIX_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    DEFAULT_FIELD_NUMBER: builtins.int
    name: builtins.str
    webhook_suffix: builtins.str
    created_at: builtins.float
    default: builtins.bool
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        webhook_suffix: builtins.str = ...,
        created_at: builtins.float = ...,
        default: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["created_at", b"created_at", "default", b"default", "name", b"name", "webhook_suffix", b"webhook_suffix"]) -> None: ...

global___EnvironmentListItem = EnvironmentListItem

class EnvironmentListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEMS_FIELD_NUMBER: builtins.int
    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___EnvironmentListItem]: ...
    def __init__(
        self,
        *,
        items: collections.abc.Iterable[global___EnvironmentListItem] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["items", b"items"]) -> None: ...

global___EnvironmentListResponse = EnvironmentListResponse

class EnvironmentMetadata(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    SETTINGS_FIELD_NUMBER: builtins.int
    name: builtins.str
    @property
    def settings(self) -> global___EnvironmentSettings: ...
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        settings: global___EnvironmentSettings | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["settings", b"settings"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["name", b"name", "settings", b"settings"]) -> None: ...

global___EnvironmentMetadata = EnvironmentMetadata

class EnvironmentSettings(google.protobuf.message.Message):
    """Environment-scoped settings, with workspace-level defaults.
    Note that we use MergeFrom to combine workspace / environment settings,
    which will *append* any `repeated` fields!
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMAGE_BUILDER_VERSION_FIELD_NUMBER: builtins.int
    WEBHOOK_SUFFIX_FIELD_NUMBER: builtins.int
    image_builder_version: builtins.str
    webhook_suffix: builtins.str
    def __init__(
        self,
        *,
        image_builder_version: builtins.str = ...,
        webhook_suffix: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["image_builder_version", b"image_builder_version", "webhook_suffix", b"webhook_suffix"]) -> None: ...

global___EnvironmentSettings = EnvironmentSettings

class EnvironmentUpdateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CURRENT_NAME_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    WEB_SUFFIX_FIELD_NUMBER: builtins.int
    current_name: builtins.str
    @property
    def name(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    @property
    def web_suffix(self) -> google.protobuf.wrappers_pb2.StringValue: ...
    def __init__(
        self,
        *,
        current_name: builtins.str = ...,
        name: google.protobuf.wrappers_pb2.StringValue | None = ...,
        web_suffix: google.protobuf.wrappers_pb2.StringValue | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["name", b"name", "web_suffix", b"web_suffix"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["current_name", b"current_name", "name", b"name", "web_suffix", b"web_suffix"]) -> None: ...

global___EnvironmentUpdateRequest = EnvironmentUpdateRequest

class FileEntry(google.protobuf.message.Message):
    """A file entry when listing files in a volume or network file system."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _FileType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _FileTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[FileEntry._FileType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSPECIFIED: FileEntry._FileType.ValueType  # 0
        FILE: FileEntry._FileType.ValueType  # 1
        DIRECTORY: FileEntry._FileType.ValueType  # 2
        SYMLINK: FileEntry._FileType.ValueType  # 3
        FIFO: FileEntry._FileType.ValueType  # 4
        SOCKET: FileEntry._FileType.ValueType  # 5

    class FileType(_FileType, metaclass=_FileTypeEnumTypeWrapper): ...
    UNSPECIFIED: FileEntry.FileType.ValueType  # 0
    FILE: FileEntry.FileType.ValueType  # 1
    DIRECTORY: FileEntry.FileType.ValueType  # 2
    SYMLINK: FileEntry.FileType.ValueType  # 3
    FIFO: FileEntry.FileType.ValueType  # 4
    SOCKET: FileEntry.FileType.ValueType  # 5

    PATH_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    MTIME_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    path: builtins.str
    type: global___FileEntry.FileType.ValueType
    mtime: builtins.int
    size: builtins.int
    def __init__(
        self,
        *,
        path: builtins.str = ...,
        type: global___FileEntry.FileType.ValueType = ...,
        mtime: builtins.int = ...,
        size: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["mtime", b"mtime", "path", b"path", "size", b"size", "type", b"type"]) -> None: ...

global___FileEntry = FileEntry

class FilesystemRuntimeOutputBatch(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OUTPUT_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    BATCH_INDEX_FIELD_NUMBER: builtins.int
    EOF_FIELD_NUMBER: builtins.int
    @property
    def output(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.bytes]: ...
    @property
    def error(self) -> global___SystemErrorMessage: ...
    batch_index: builtins.int
    eof: builtins.bool
    def __init__(
        self,
        *,
        output: collections.abc.Iterable[builtins.bytes] | None = ...,
        error: global___SystemErrorMessage | None = ...,
        batch_index: builtins.int = ...,
        eof: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_error", b"_error", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_error", b"_error", "batch_index", b"batch_index", "eof", b"eof", "error", b"error", "output", b"output"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_error", b"_error"]) -> typing_extensions.Literal["error"] | None: ...

global___FilesystemRuntimeOutputBatch = FilesystemRuntimeOutputBatch

class Function(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _DefinitionType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _DefinitionTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Function._DefinitionType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        DEFINITION_TYPE_UNSPECIFIED: Function._DefinitionType.ValueType  # 0
        DEFINITION_TYPE_SERIALIZED: Function._DefinitionType.ValueType  # 1
        DEFINITION_TYPE_FILE: Function._DefinitionType.ValueType  # 2

    class DefinitionType(_DefinitionType, metaclass=_DefinitionTypeEnumTypeWrapper): ...
    DEFINITION_TYPE_UNSPECIFIED: Function.DefinitionType.ValueType  # 0
    DEFINITION_TYPE_SERIALIZED: Function.DefinitionType.ValueType  # 1
    DEFINITION_TYPE_FILE: Function.DefinitionType.ValueType  # 2

    class _FunctionType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _FunctionTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Function._FunctionType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        FUNCTION_TYPE_UNSPECIFIED: Function._FunctionType.ValueType  # 0
        FUNCTION_TYPE_GENERATOR: Function._FunctionType.ValueType  # 1
        FUNCTION_TYPE_FUNCTION: Function._FunctionType.ValueType  # 2

    class FunctionType(_FunctionType, metaclass=_FunctionTypeEnumTypeWrapper): ...
    FUNCTION_TYPE_UNSPECIFIED: Function.FunctionType.ValueType  # 0
    FUNCTION_TYPE_GENERATOR: Function.FunctionType.ValueType  # 1
    FUNCTION_TYPE_FUNCTION: Function.FunctionType.ValueType  # 2

    class MethodDefinitionsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___MethodDefinition: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___MethodDefinition | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    class ExperimentalOptionsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    MODULE_NAME_FIELD_NUMBER: builtins.int
    FUNCTION_NAME_FIELD_NUMBER: builtins.int
    MOUNT_IDS_FIELD_NUMBER: builtins.int
    IMAGE_ID_FIELD_NUMBER: builtins.int
    FUNCTION_SERIALIZED_FIELD_NUMBER: builtins.int
    DEFINITION_TYPE_FIELD_NUMBER: builtins.int
    FUNCTION_TYPE_FIELD_NUMBER: builtins.int
    RESOURCES_FIELD_NUMBER: builtins.int
    SECRET_IDS_FIELD_NUMBER: builtins.int
    RATE_LIMIT_FIELD_NUMBER: builtins.int
    WEBHOOK_CONFIG_FIELD_NUMBER: builtins.int
    SHARED_VOLUME_MOUNTS_FIELD_NUMBER: builtins.int
    PROXY_ID_FIELD_NUMBER: builtins.int
    RETRY_POLICY_FIELD_NUMBER: builtins.int
    CONCURRENCY_LIMIT_FIELD_NUMBER: builtins.int
    TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    PTY_INFO_FIELD_NUMBER: builtins.int
    CLASS_SERIALIZED_FIELD_NUMBER: builtins.int
    TASK_IDLE_TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_FIELD_NUMBER: builtins.int
    WARM_POOL_SIZE_FIELD_NUMBER: builtins.int
    WEB_URL_FIELD_NUMBER: builtins.int
    WEB_URL_INFO_FIELD_NUMBER: builtins.int
    RUNTIME_FIELD_NUMBER: builtins.int
    APP_NAME_FIELD_NUMBER: builtins.int
    VOLUME_MOUNTS_FIELD_NUMBER: builtins.int
    MAX_CONCURRENT_INPUTS_FIELD_NUMBER: builtins.int
    CUSTOM_DOMAIN_INFO_FIELD_NUMBER: builtins.int
    WORKER_ID_FIELD_NUMBER: builtins.int
    RUNTIME_DEBUG_FIELD_NUMBER: builtins.int
    IS_BUILDER_FUNCTION_FIELD_NUMBER: builtins.int
    IS_AUTO_SNAPSHOT_FIELD_NUMBER: builtins.int
    IS_METHOD_FIELD_NUMBER: builtins.int
    IS_CHECKPOINTING_FUNCTION_FIELD_NUMBER: builtins.int
    CHECKPOINTING_ENABLED_FIELD_NUMBER: builtins.int
    CHECKPOINT_FIELD_NUMBER: builtins.int
    OBJECT_DEPENDENCIES_FIELD_NUMBER: builtins.int
    BLOCK_NETWORK_FIELD_NUMBER: builtins.int
    MAX_INPUTS_FIELD_NUMBER: builtins.int
    S3_MOUNTS_FIELD_NUMBER: builtins.int
    CLOUD_BUCKET_MOUNTS_FIELD_NUMBER: builtins.int
    SCHEDULER_PLACEMENT_FIELD_NUMBER: builtins.int
    IS_CLASS_FIELD_NUMBER: builtins.int
    USE_FUNCTION_ID_FIELD_NUMBER: builtins.int
    USE_METHOD_NAME_FIELD_NUMBER: builtins.int
    CLASS_PARAMETER_INFO_FIELD_NUMBER: builtins.int
    BATCH_MAX_SIZE_FIELD_NUMBER: builtins.int
    BATCH_LINGER_MS_FIELD_NUMBER: builtins.int
    I6PN_ENABLED_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_CONCURRENT_CANCELLATIONS_FIELD_NUMBER: builtins.int
    TARGET_CONCURRENT_INPUTS_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_TASK_TEMPLATES_ENABLED_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_TASK_TEMPLATES_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_GROUP_SIZE_FIELD_NUMBER: builtins.int
    UNTRUSTED_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_BUFFER_CONTAINERS_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_PROXY_IP_FIELD_NUMBER: builtins.int
    RUNTIME_PERF_RECORD_FIELD_NUMBER: builtins.int
    SCHEDULE_FIELD_NUMBER: builtins.int
    SNAPSHOT_DEBUG_FIELD_NUMBER: builtins.int
    METHOD_DEFINITIONS_FIELD_NUMBER: builtins.int
    METHOD_DEFINITIONS_SET_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_CUSTOM_SCALING_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_STR_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_ENABLE_GPU_SNAPSHOT_FIELD_NUMBER: builtins.int
    AUTOSCALER_SETTINGS_FIELD_NUMBER: builtins.int
    FUNCTION_SCHEMA_FIELD_NUMBER: builtins.int
    EXPERIMENTAL_OPTIONS_FIELD_NUMBER: builtins.int
    MOUNT_CLIENT_DEPENDENCIES_FIELD_NUMBER: builtins.int
    module_name: builtins.str
    function_name: builtins.str
    @property
    def mount_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    image_id: builtins.str
    function_serialized: builtins.bytes
    definition_type: global___Function.DefinitionType.ValueType
    function_type: global___Function.FunctionType.ValueType
    @property
    def resources(self) -> global___Resources: ...
    @property
    def secret_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def rate_limit(self) -> global___RateLimit: ...
    @property
    def webhook_config(self) -> global___WebhookConfig: ...
    @property
    def shared_volume_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SharedVolumeMount]: ...
    proxy_id: builtins.str
    @property
    def retry_policy(self) -> global___FunctionRetryPolicy: ...
    concurrency_limit: builtins.int
    """To be replaced by autoscaler_settings.max_containers"""
    timeout_secs: builtins.int
    @property
    def pty_info(self) -> global___PTYInfo: ...
    class_serialized: builtins.bytes
    task_idle_timeout_secs: builtins.int
    """To be replaced by autoscaler_settings.scaledown_period"""
    cloud_provider: global___CloudProvider.ValueType
    """Deprecated at some point"""
    warm_pool_size: builtins.int
    """To be replaced by autoscaler_settings.min_containers"""
    web_url: builtins.str
    @property
    def web_url_info(self) -> global___WebUrlInfo: ...
    runtime: builtins.str
    """If set, overrides the runtime used by the function, either "runc" or "gvisor"."""
    app_name: builtins.str
    """Formerly stub_name"""
    @property
    def volume_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___VolumeMount]: ...
    max_concurrent_inputs: builtins.int
    @property
    def custom_domain_info(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CustomDomainInfo]: ...
    worker_id: builtins.str
    """For internal debugging use only."""
    runtime_debug: builtins.bool
    """For internal debugging use only."""
    is_builder_function: builtins.bool
    """TODO: combine into enum?"""
    is_auto_snapshot: builtins.bool
    is_method: builtins.bool
    is_checkpointing_function: builtins.bool
    checkpointing_enabled: builtins.bool
    """Checkpoint and restore"""
    @property
    def checkpoint(self) -> global___CheckpointInfo: ...
    @property
    def object_dependencies(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ObjectDependency]: ...
    block_network: builtins.bool
    max_inputs: builtins.int
    @property
    def s3_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___S3Mount]: ...
    @property
    def cloud_bucket_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CloudBucketMount]: ...
    @property
    def scheduler_placement(self) -> global___SchedulerPlacement: ...
    is_class: builtins.bool
    """if "Function" is actually a class grouping multiple methods"""
    use_function_id: builtins.str
    """for class methods use this function id instead for invocations - the *referenced* function should have is_class=True"""
    use_method_name: builtins.str
    """for class methods - this method name needs to be included in the FunctionInput"""
    @property
    def class_parameter_info(self) -> global___ClassParameterInfo: ...
    batch_max_size: builtins.int
    """Maximum number of inputs to fetch at once"""
    batch_linger_ms: builtins.int
    """Miliseconds to block before a response is needed"""
    i6pn_enabled: builtins.bool
    _experimental_concurrent_cancellations: builtins.bool
    target_concurrent_inputs: builtins.int
    _experimental_task_templates_enabled: builtins.bool
    """TODO(irfansharif): Remove, once https://github.com/modal-labs/modal/pull/15645 lands.
    forces going through the new gpu-fallbacks integration path, even if no fallback options are specified
    """
    @property
    def _experimental_task_templates(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TaskTemplate]:
        """for fallback options, where the first/most-preferred "template" is derived from fields above"""
    _experimental_group_size: builtins.int
    """When the function is a "grouped" one, this records the # of tasks we want
    to schedule in tandem.
    """
    untrusted: builtins.bool
    """If set, the function will be run in an untrusted environment."""
    _experimental_buffer_containers: builtins.int
    """To be replaced by autoscaler_settings.buffer_containers"""
    _experimental_proxy_ip: builtins.str
    """_experimental_proxy_ip -> ProxyInfo
    TODO: deprecate.
    """
    runtime_perf_record: builtins.bool
    """For internal debugging use only."""
    @property
    def schedule(self) -> global___Schedule: ...
    snapshot_debug: builtins.bool
    """For internal debugging use only."""
    @property
    def method_definitions(self) -> google.protobuf.internal.containers.MessageMap[builtins.str, global___MethodDefinition]:
        """Mapping of method names to method definitions, only non-empty for class service functions"""
    method_definitions_set: builtins.bool
    _experimental_custom_scaling: builtins.bool
    cloud_provider_str: builtins.str
    """Supersedes cloud_provider"""
    _experimental_enable_gpu_snapshot: builtins.bool
    """Experimental support for GPU snapshotting"""
    @property
    def autoscaler_settings(self) -> global___AutoscalerSettings:
        """Bundle of parameters related to autoscaling"""
    @property
    def function_schema(self) -> global___FunctionSchema: ...
    @property
    def experimental_options(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """For server-side experimental functionality. Prefer using this over individual _experimental_* fields.
        Note the value type as string. Internally we'll coerce all values to string with str().
        On the server, it's necessary to convert back to the most natural type (e.g. int) when relevant.
        """
    mount_client_dependencies: builtins.bool
    """If set, client deps will be mounted into the container, and are
    no longer expected to exist in the image itself.
    """
    def __init__(
        self,
        *,
        module_name: builtins.str = ...,
        function_name: builtins.str = ...,
        mount_ids: collections.abc.Iterable[builtins.str] | None = ...,
        image_id: builtins.str = ...,
        function_serialized: builtins.bytes = ...,
        definition_type: global___Function.DefinitionType.ValueType = ...,
        function_type: global___Function.FunctionType.ValueType = ...,
        resources: global___Resources | None = ...,
        secret_ids: collections.abc.Iterable[builtins.str] | None = ...,
        rate_limit: global___RateLimit | None = ...,
        webhook_config: global___WebhookConfig | None = ...,
        shared_volume_mounts: collections.abc.Iterable[global___SharedVolumeMount] | None = ...,
        proxy_id: builtins.str | None = ...,
        retry_policy: global___FunctionRetryPolicy | None = ...,
        concurrency_limit: builtins.int = ...,
        timeout_secs: builtins.int = ...,
        pty_info: global___PTYInfo | None = ...,
        class_serialized: builtins.bytes = ...,
        task_idle_timeout_secs: builtins.int = ...,
        cloud_provider: global___CloudProvider.ValueType | None = ...,
        warm_pool_size: builtins.int = ...,
        web_url: builtins.str = ...,
        web_url_info: global___WebUrlInfo | None = ...,
        runtime: builtins.str = ...,
        app_name: builtins.str = ...,
        volume_mounts: collections.abc.Iterable[global___VolumeMount] | None = ...,
        max_concurrent_inputs: builtins.int = ...,
        custom_domain_info: collections.abc.Iterable[global___CustomDomainInfo] | None = ...,
        worker_id: builtins.str = ...,
        runtime_debug: builtins.bool = ...,
        is_builder_function: builtins.bool = ...,
        is_auto_snapshot: builtins.bool = ...,
        is_method: builtins.bool = ...,
        is_checkpointing_function: builtins.bool = ...,
        checkpointing_enabled: builtins.bool = ...,
        checkpoint: global___CheckpointInfo | None = ...,
        object_dependencies: collections.abc.Iterable[global___ObjectDependency] | None = ...,
        block_network: builtins.bool = ...,
        max_inputs: builtins.int = ...,
        s3_mounts: collections.abc.Iterable[global___S3Mount] | None = ...,
        cloud_bucket_mounts: collections.abc.Iterable[global___CloudBucketMount] | None = ...,
        scheduler_placement: global___SchedulerPlacement | None = ...,
        is_class: builtins.bool = ...,
        use_function_id: builtins.str = ...,
        use_method_name: builtins.str = ...,
        class_parameter_info: global___ClassParameterInfo | None = ...,
        batch_max_size: builtins.int = ...,
        batch_linger_ms: builtins.int = ...,
        i6pn_enabled: builtins.bool = ...,
        _experimental_concurrent_cancellations: builtins.bool = ...,
        target_concurrent_inputs: builtins.int = ...,
        _experimental_task_templates_enabled: builtins.bool = ...,
        _experimental_task_templates: collections.abc.Iterable[global___TaskTemplate] | None = ...,
        _experimental_group_size: builtins.int = ...,
        untrusted: builtins.bool = ...,
        _experimental_buffer_containers: builtins.int = ...,
        _experimental_proxy_ip: builtins.str | None = ...,
        runtime_perf_record: builtins.bool = ...,
        schedule: global___Schedule | None = ...,
        snapshot_debug: builtins.bool = ...,
        method_definitions: collections.abc.Mapping[builtins.str, global___MethodDefinition] | None = ...,
        method_definitions_set: builtins.bool = ...,
        _experimental_custom_scaling: builtins.bool = ...,
        cloud_provider_str: builtins.str = ...,
        _experimental_enable_gpu_snapshot: builtins.bool = ...,
        autoscaler_settings: global___AutoscalerSettings | None = ...,
        function_schema: global___FunctionSchema | None = ...,
        experimental_options: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        mount_client_dependencies: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["X_experimental_proxy_ip", b"X_experimental_proxy_ip", "_cloud_provider", b"_cloud_provider", "_experimental_proxy_ip", b"_experimental_proxy_ip", "_proxy_id", b"_proxy_id", "_scheduler_placement", b"_scheduler_placement", "autoscaler_settings", b"autoscaler_settings", "checkpoint", b"checkpoint", "class_parameter_info", b"class_parameter_info", "cloud_provider", b"cloud_provider", "function_schema", b"function_schema", "proxy_id", b"proxy_id", "pty_info", b"pty_info", "rate_limit", b"rate_limit", "resources", b"resources", "retry_policy", b"retry_policy", "schedule", b"schedule", "scheduler_placement", b"scheduler_placement", "web_url_info", b"web_url_info", "webhook_config", b"webhook_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["X_experimental_proxy_ip", b"X_experimental_proxy_ip", "_cloud_provider", b"_cloud_provider", "_experimental_buffer_containers", b"_experimental_buffer_containers", "_experimental_concurrent_cancellations", b"_experimental_concurrent_cancellations", "_experimental_custom_scaling", b"_experimental_custom_scaling", "_experimental_enable_gpu_snapshot", b"_experimental_enable_gpu_snapshot", "_experimental_group_size", b"_experimental_group_size", "_experimental_proxy_ip", b"_experimental_proxy_ip", "_experimental_task_templates", b"_experimental_task_templates", "_experimental_task_templates_enabled", b"_experimental_task_templates_enabled", "_proxy_id", b"_proxy_id", "_scheduler_placement", b"_scheduler_placement", "app_name", b"app_name", "autoscaler_settings", b"autoscaler_settings", "batch_linger_ms", b"batch_linger_ms", "batch_max_size", b"batch_max_size", "block_network", b"block_network", "checkpoint", b"checkpoint", "checkpointing_enabled", b"checkpointing_enabled", "class_parameter_info", b"class_parameter_info", "class_serialized", b"class_serialized", "cloud_bucket_mounts", b"cloud_bucket_mounts", "cloud_provider", b"cloud_provider", "cloud_provider_str", b"cloud_provider_str", "concurrency_limit", b"concurrency_limit", "custom_domain_info", b"custom_domain_info", "definition_type", b"definition_type", "experimental_options", b"experimental_options", "function_name", b"function_name", "function_schema", b"function_schema", "function_serialized", b"function_serialized", "function_type", b"function_type", "i6pn_enabled", b"i6pn_enabled", "image_id", b"image_id", "is_auto_snapshot", b"is_auto_snapshot", "is_builder_function", b"is_builder_function", "is_checkpointing_function", b"is_checkpointing_function", "is_class", b"is_class", "is_method", b"is_method", "max_concurrent_inputs", b"max_concurrent_inputs", "max_inputs", b"max_inputs", "method_definitions", b"method_definitions", "method_definitions_set", b"method_definitions_set", "module_name", b"module_name", "mount_client_dependencies", b"mount_client_dependencies", "mount_ids", b"mount_ids", "object_dependencies", b"object_dependencies", "proxy_id", b"proxy_id", "pty_info", b"pty_info", "rate_limit", b"rate_limit", "resources", b"resources", "retry_policy", b"retry_policy", "runtime", b"runtime", "runtime_debug", b"runtime_debug", "runtime_perf_record", b"runtime_perf_record", "s3_mounts", b"s3_mounts", "schedule", b"schedule", "scheduler_placement", b"scheduler_placement", "secret_ids", b"secret_ids", "shared_volume_mounts", b"shared_volume_mounts", "snapshot_debug", b"snapshot_debug", "target_concurrent_inputs", b"target_concurrent_inputs", "task_idle_timeout_secs", b"task_idle_timeout_secs", "timeout_secs", b"timeout_secs", "untrusted", b"untrusted", "use_function_id", b"use_function_id", "use_method_name", b"use_method_name", "volume_mounts", b"volume_mounts", "warm_pool_size", b"warm_pool_size", "web_url", b"web_url", "web_url_info", b"web_url_info", "webhook_config", b"webhook_config", "worker_id", b"worker_id"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["X_experimental_proxy_ip", b"X_experimental_proxy_ip"]) -> typing_extensions.Literal["_experimental_proxy_ip"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_cloud_provider", b"_cloud_provider"]) -> typing_extensions.Literal["cloud_provider"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_proxy_id", b"_proxy_id"]) -> typing_extensions.Literal["proxy_id"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_scheduler_placement", b"_scheduler_placement"]) -> typing_extensions.Literal["scheduler_placement"] | None: ...

global___Function = Function

class FunctionAsyncInvokeRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    PARENT_INPUT_ID_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    parent_input_id: builtins.str
    @property
    def input(self) -> global___FunctionInput: ...
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        parent_input_id: builtins.str = ...,
        input: global___FunctionInput | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["input", b"input"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id", "input", b"input", "parent_input_id", b"parent_input_id"]) -> None: ...

global___FunctionAsyncInvokeRequest = FunctionAsyncInvokeRequest

class FunctionAsyncInvokeResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RETRY_WITH_BLOB_UPLOAD_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    retry_with_blob_upload: builtins.bool
    function_call_id: builtins.str
    def __init__(
        self,
        *,
        retry_with_blob_upload: builtins.bool = ...,
        function_call_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id", "retry_with_blob_upload", b"retry_with_blob_upload"]) -> None: ...

global___FunctionAsyncInvokeResponse = FunctionAsyncInvokeResponse

class FunctionBindParamsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    SERIALIZED_PARAMS_FIELD_NUMBER: builtins.int
    FUNCTION_OPTIONS_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    serialized_params: builtins.bytes
    @property
    def function_options(self) -> global___FunctionOptions: ...
    environment_name: builtins.str
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        serialized_params: builtins.bytes = ...,
        function_options: global___FunctionOptions | None = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["function_options", b"function_options"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "function_id", b"function_id", "function_options", b"function_options", "serialized_params", b"serialized_params"]) -> None: ...

global___FunctionBindParamsRequest = FunctionBindParamsRequest

class FunctionBindParamsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BOUND_FUNCTION_ID_FIELD_NUMBER: builtins.int
    HANDLE_METADATA_FIELD_NUMBER: builtins.int
    bound_function_id: builtins.str
    @property
    def handle_metadata(self) -> global___FunctionHandleMetadata: ...
    def __init__(
        self,
        *,
        bound_function_id: builtins.str = ...,
        handle_metadata: global___FunctionHandleMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["handle_metadata", b"handle_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["bound_function_id", b"bound_function_id", "handle_metadata", b"handle_metadata"]) -> None: ...

global___FunctionBindParamsResponse = FunctionBindParamsResponse

class FunctionCallCallGraphInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    PARENT_INPUT_ID_FIELD_NUMBER: builtins.int
    FUNCTION_NAME_FIELD_NUMBER: builtins.int
    MODULE_NAME_FIELD_NUMBER: builtins.int
    function_call_id: builtins.str
    parent_input_id: builtins.str
    function_name: builtins.str
    module_name: builtins.str
    def __init__(
        self,
        *,
        function_call_id: builtins.str = ...,
        parent_input_id: builtins.str = ...,
        function_name: builtins.str = ...,
        module_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id", "function_name", b"function_name", "module_name", b"module_name", "parent_input_id", b"parent_input_id"]) -> None: ...

global___FunctionCallCallGraphInfo = FunctionCallCallGraphInfo

class FunctionCallCancelRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    TERMINATE_CONTAINERS_FIELD_NUMBER: builtins.int
    function_call_id: builtins.str
    terminate_containers: builtins.bool
    def __init__(
        self,
        *,
        function_call_id: builtins.str = ...,
        terminate_containers: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id", "terminate_containers", b"terminate_containers"]) -> None: ...

global___FunctionCallCancelRequest = FunctionCallCancelRequest

class FunctionCallGetDataRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    LAST_INDEX_FIELD_NUMBER: builtins.int
    function_call_id: builtins.str
    last_index: builtins.int
    def __init__(
        self,
        *,
        function_call_id: builtins.str = ...,
        last_index: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id", "last_index", b"last_index"]) -> None: ...

global___FunctionCallGetDataRequest = FunctionCallGetDataRequest

class FunctionCallInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    IDX_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    SCHEDULED_AT_FIELD_NUMBER: builtins.int
    PENDING_INPUTS_FIELD_NUMBER: builtins.int
    FAILED_INPUTS_FIELD_NUMBER: builtins.int
    SUCCEEDED_INPUTS_FIELD_NUMBER: builtins.int
    TIMEOUT_INPUTS_FIELD_NUMBER: builtins.int
    CANCELLED_INPUTS_FIELD_NUMBER: builtins.int
    TOTAL_INPUTS_FIELD_NUMBER: builtins.int
    function_call_id: builtins.str
    idx: builtins.int
    created_at: builtins.float
    """when the call was created"""
    scheduled_at: builtins.float
    """if cron job, when run was scheduled"""
    @property
    def pending_inputs(self) -> global___InputCategoryInfo: ...
    @property
    def failed_inputs(self) -> global___InputCategoryInfo: ...
    @property
    def succeeded_inputs(self) -> global___InputCategoryInfo: ...
    @property
    def timeout_inputs(self) -> global___InputCategoryInfo: ...
    @property
    def cancelled_inputs(self) -> global___InputCategoryInfo: ...
    total_inputs: builtins.int
    def __init__(
        self,
        *,
        function_call_id: builtins.str = ...,
        idx: builtins.int = ...,
        created_at: builtins.float = ...,
        scheduled_at: builtins.float = ...,
        pending_inputs: global___InputCategoryInfo | None = ...,
        failed_inputs: global___InputCategoryInfo | None = ...,
        succeeded_inputs: global___InputCategoryInfo | None = ...,
        timeout_inputs: global___InputCategoryInfo | None = ...,
        cancelled_inputs: global___InputCategoryInfo | None = ...,
        total_inputs: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["cancelled_inputs", b"cancelled_inputs", "failed_inputs", b"failed_inputs", "pending_inputs", b"pending_inputs", "succeeded_inputs", b"succeeded_inputs", "timeout_inputs", b"timeout_inputs"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["cancelled_inputs", b"cancelled_inputs", "created_at", b"created_at", "failed_inputs", b"failed_inputs", "function_call_id", b"function_call_id", "idx", b"idx", "pending_inputs", b"pending_inputs", "scheduled_at", b"scheduled_at", "succeeded_inputs", b"succeeded_inputs", "timeout_inputs", b"timeout_inputs", "total_inputs", b"total_inputs"]) -> None: ...

global___FunctionCallInfo = FunctionCallInfo

class FunctionCallListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id"]) -> None: ...

global___FunctionCallListRequest = FunctionCallListRequest

class FunctionCallListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALLS_FIELD_NUMBER: builtins.int
    @property
    def function_calls(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionCallInfo]: ...
    def __init__(
        self,
        *,
        function_calls: collections.abc.Iterable[global___FunctionCallInfo] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_calls", b"function_calls"]) -> None: ...

global___FunctionCallListResponse = FunctionCallListResponse

class FunctionCallPutDataRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    DATA_CHUNKS_FIELD_NUMBER: builtins.int
    function_call_id: builtins.str
    @property
    def data_chunks(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DataChunk]: ...
    def __init__(
        self,
        *,
        function_call_id: builtins.str = ...,
        data_chunks: collections.abc.Iterable[global___DataChunk] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["data_chunks", b"data_chunks", "function_call_id", b"function_call_id"]) -> None: ...

global___FunctionCallPutDataRequest = FunctionCallPutDataRequest

class FunctionCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    SCHEDULE_FIELD_NUMBER: builtins.int
    EXISTING_FUNCTION_ID_FIELD_NUMBER: builtins.int
    FUNCTION_DATA_FIELD_NUMBER: builtins.int
    @property
    def function(self) -> global___Function: ...
    app_id: builtins.str
    @property
    def schedule(self) -> global___Schedule:
        """Deprecated: now passed in the Function definition"""
    existing_function_id: builtins.str
    @property
    def function_data(self) -> global___FunctionData:
        """supersedes 'function' field above"""
    def __init__(
        self,
        *,
        function: global___Function | None = ...,
        app_id: builtins.str = ...,
        schedule: global___Schedule | None = ...,
        existing_function_id: builtins.str = ...,
        function_data: global___FunctionData | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["function", b"function", "function_data", b"function_data", "schedule", b"schedule"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "existing_function_id", b"existing_function_id", "function", b"function", "function_data", b"function_data", "schedule", b"schedule"]) -> None: ...

global___FunctionCreateRequest = FunctionCreateRequest

class FunctionCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    __DEPRECATED_WEB_URL_FIELD_NUMBER: builtins.int
    FUNCTION_FIELD_NUMBER: builtins.int
    HANDLE_METADATA_FIELD_NUMBER: builtins.int
    SERVER_WARNINGS_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    __deprecated_web_url: builtins.str
    """Used up until 0.62.212"""
    @property
    def function(self) -> global___Function: ...
    @property
    def handle_metadata(self) -> global___FunctionHandleMetadata: ...
    @property
    def server_warnings(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Warning]: ...
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        __deprecated_web_url: builtins.str = ...,
        function: global___Function | None = ...,
        handle_metadata: global___FunctionHandleMetadata | None = ...,
        server_warnings: collections.abc.Iterable[global___Warning] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["function", b"function", "handle_metadata", b"handle_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["__deprecated_web_url", b"__deprecated_web_url", "function", b"function", "function_id", b"function_id", "handle_metadata", b"handle_metadata", "server_warnings", b"server_warnings"]) -> None: ...

global___FunctionCreateResponse = FunctionCreateResponse

class FunctionData(google.protobuf.message.Message):
    """Note: FunctionData pulls "up" a subset of fields from Function message that
    will get deprecated there and made authoritative here, at the top-level.
    All remaining fields will stay within the Function message itself and a
    single FunctionData will contain a list of such (ranked) Functions. The
    top-level fields capture data not specific to any particular underlying
    task (like warm-pool-size, applicable across all tasks), while fields
    specific to the task (like the resource request) will exist at the bottom
    level.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class MethodDefinitionsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___MethodDefinition: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___MethodDefinition | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    class RankedFunction(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        RANK_FIELD_NUMBER: builtins.int
        FUNCTION_FIELD_NUMBER: builtins.int
        rank: builtins.int
        @property
        def function(self) -> global___Function: ...
        def __init__(
            self,
            *,
            rank: builtins.int = ...,
            function: global___Function | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["function", b"function"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["function", b"function", "rank", b"rank"]) -> None: ...

    class ExperimentalOptionsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    MODULE_NAME_FIELD_NUMBER: builtins.int
    FUNCTION_NAME_FIELD_NUMBER: builtins.int
    FUNCTION_TYPE_FIELD_NUMBER: builtins.int
    WARM_POOL_SIZE_FIELD_NUMBER: builtins.int
    CONCURRENCY_LIMIT_FIELD_NUMBER: builtins.int
    TASK_IDLE_TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_GROUP_SIZE_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_BUFFER_CONTAINERS_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_CUSTOM_SCALING_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_ENABLE_GPU_SNAPSHOT_FIELD_NUMBER: builtins.int
    WORKER_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    WEB_URL_FIELD_NUMBER: builtins.int
    WEB_URL_INFO_FIELD_NUMBER: builtins.int
    WEBHOOK_CONFIG_FIELD_NUMBER: builtins.int
    CUSTOM_DOMAIN_INFO_FIELD_NUMBER: builtins.int
    _EXPERIMENTAL_PROXY_IP_FIELD_NUMBER: builtins.int
    METHOD_DEFINITIONS_FIELD_NUMBER: builtins.int
    METHOD_DEFINITIONS_SET_FIELD_NUMBER: builtins.int
    IS_CLASS_FIELD_NUMBER: builtins.int
    CLASS_PARAMETER_INFO_FIELD_NUMBER: builtins.int
    IS_METHOD_FIELD_NUMBER: builtins.int
    USE_FUNCTION_ID_FIELD_NUMBER: builtins.int
    USE_METHOD_NAME_FIELD_NUMBER: builtins.int
    RANKED_FUNCTIONS_FIELD_NUMBER: builtins.int
    SCHEDULE_FIELD_NUMBER: builtins.int
    UNTRUSTED_FIELD_NUMBER: builtins.int
    SNAPSHOT_DEBUG_FIELD_NUMBER: builtins.int
    RUNTIME_PERF_RECORD_FIELD_NUMBER: builtins.int
    AUTOSCALER_SETTINGS_FIELD_NUMBER: builtins.int
    FUNCTION_SCHEMA_FIELD_NUMBER: builtins.int
    EXPERIMENTAL_OPTIONS_FIELD_NUMBER: builtins.int
    module_name: builtins.str
    function_name: builtins.str
    function_type: global___Function.FunctionType.ValueType
    warm_pool_size: builtins.int
    """Scheduling related fields."""
    concurrency_limit: builtins.int
    task_idle_timeout_secs: builtins.int
    _experimental_group_size: builtins.int
    """When the function is a "grouped" one, this records the # of tasks we want
    to schedule in tandem.
    """
    _experimental_buffer_containers: builtins.int
    _experimental_custom_scaling: builtins.bool
    _experimental_enable_gpu_snapshot: builtins.bool
    worker_id: builtins.str
    """for internal debugging use only"""
    timeout_secs: builtins.int
    web_url: builtins.str
    @property
    def web_url_info(self) -> global___WebUrlInfo: ...
    @property
    def webhook_config(self) -> global___WebhookConfig: ...
    @property
    def custom_domain_info(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CustomDomainInfo]: ...
    _experimental_proxy_ip: builtins.str
    """_experimental_proxy_ip -> ProxyInfo
    TODO: deprecate.
    """
    @property
    def method_definitions(self) -> google.protobuf.internal.containers.MessageMap[builtins.str, global___MethodDefinition]:
        """Mapping of method names to method definitions, only non-empty for class service functions"""
    method_definitions_set: builtins.bool
    is_class: builtins.bool
    """if "Function" is actually a class grouping multiple methods - applies across all underlying tasks"""
    @property
    def class_parameter_info(self) -> global___ClassParameterInfo: ...
    is_method: builtins.bool
    use_function_id: builtins.str
    """used for methods"""
    use_method_name: builtins.str
    """used for methods"""
    @property
    def ranked_functions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionData.RankedFunction]: ...
    @property
    def schedule(self) -> global___Schedule: ...
    untrusted: builtins.bool
    """If set, the function will be run in an untrusted environment."""
    snapshot_debug: builtins.bool
    """For internal debugging use only."""
    runtime_perf_record: builtins.bool
    """For internal debugging use only."""
    @property
    def autoscaler_settings(self) -> global___AutoscalerSettings:
        """Bundle of parameters related to autoscaling"""
    @property
    def function_schema(self) -> global___FunctionSchema: ...
    @property
    def experimental_options(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    def __init__(
        self,
        *,
        module_name: builtins.str = ...,
        function_name: builtins.str = ...,
        function_type: global___Function.FunctionType.ValueType = ...,
        warm_pool_size: builtins.int = ...,
        concurrency_limit: builtins.int = ...,
        task_idle_timeout_secs: builtins.int = ...,
        _experimental_group_size: builtins.int = ...,
        _experimental_buffer_containers: builtins.int = ...,
        _experimental_custom_scaling: builtins.bool = ...,
        _experimental_enable_gpu_snapshot: builtins.bool = ...,
        worker_id: builtins.str = ...,
        timeout_secs: builtins.int = ...,
        web_url: builtins.str = ...,
        web_url_info: global___WebUrlInfo | None = ...,
        webhook_config: global___WebhookConfig | None = ...,
        custom_domain_info: collections.abc.Iterable[global___CustomDomainInfo] | None = ...,
        _experimental_proxy_ip: builtins.str | None = ...,
        method_definitions: collections.abc.Mapping[builtins.str, global___MethodDefinition] | None = ...,
        method_definitions_set: builtins.bool = ...,
        is_class: builtins.bool = ...,
        class_parameter_info: global___ClassParameterInfo | None = ...,
        is_method: builtins.bool = ...,
        use_function_id: builtins.str = ...,
        use_method_name: builtins.str = ...,
        ranked_functions: collections.abc.Iterable[global___FunctionData.RankedFunction] | None = ...,
        schedule: global___Schedule | None = ...,
        untrusted: builtins.bool = ...,
        snapshot_debug: builtins.bool = ...,
        runtime_perf_record: builtins.bool = ...,
        autoscaler_settings: global___AutoscalerSettings | None = ...,
        function_schema: global___FunctionSchema | None = ...,
        experimental_options: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["X_experimental_proxy_ip", b"X_experimental_proxy_ip", "_experimental_proxy_ip", b"_experimental_proxy_ip", "autoscaler_settings", b"autoscaler_settings", "class_parameter_info", b"class_parameter_info", "function_schema", b"function_schema", "schedule", b"schedule", "web_url_info", b"web_url_info", "webhook_config", b"webhook_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["X_experimental_proxy_ip", b"X_experimental_proxy_ip", "_experimental_buffer_containers", b"_experimental_buffer_containers", "_experimental_custom_scaling", b"_experimental_custom_scaling", "_experimental_enable_gpu_snapshot", b"_experimental_enable_gpu_snapshot", "_experimental_group_size", b"_experimental_group_size", "_experimental_proxy_ip", b"_experimental_proxy_ip", "autoscaler_settings", b"autoscaler_settings", "class_parameter_info", b"class_parameter_info", "concurrency_limit", b"concurrency_limit", "custom_domain_info", b"custom_domain_info", "experimental_options", b"experimental_options", "function_name", b"function_name", "function_schema", b"function_schema", "function_type", b"function_type", "is_class", b"is_class", "is_method", b"is_method", "method_definitions", b"method_definitions", "method_definitions_set", b"method_definitions_set", "module_name", b"module_name", "ranked_functions", b"ranked_functions", "runtime_perf_record", b"runtime_perf_record", "schedule", b"schedule", "snapshot_debug", b"snapshot_debug", "task_idle_timeout_secs", b"task_idle_timeout_secs", "timeout_secs", b"timeout_secs", "untrusted", b"untrusted", "use_function_id", b"use_function_id", "use_method_name", b"use_method_name", "warm_pool_size", b"warm_pool_size", "web_url", b"web_url", "web_url_info", b"web_url_info", "webhook_config", b"webhook_config", "worker_id", b"worker_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["X_experimental_proxy_ip", b"X_experimental_proxy_ip"]) -> typing_extensions.Literal["_experimental_proxy_ip"] | None: ...

global___FunctionData = FunctionData

class FunctionExtended(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_IDENTIFIER_FIELD_NUMBER: builtins.int
    FUNCTION_SINGLETON_FIELD_NUMBER: builtins.int
    FUNCTION_DATA_FIELD_NUMBER: builtins.int
    type_identifier: builtins.int
    @property
    def function_singleton(self) -> global___Function: ...
    @property
    def function_data(self) -> global___FunctionData: ...
    def __init__(
        self,
        *,
        type_identifier: builtins.int = ...,
        function_singleton: global___Function | None = ...,
        function_data: global___FunctionData | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["function_data", b"function_data", "function_extended", b"function_extended", "function_singleton", b"function_singleton"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_data", b"function_data", "function_extended", b"function_extended", "function_singleton", b"function_singleton", "type_identifier", b"type_identifier"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["function_extended", b"function_extended"]) -> typing_extensions.Literal["function_singleton", "function_data"] | None: ...

global___FunctionExtended = FunctionExtended

class FunctionGetCallGraphRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    function_call_id: builtins.str
    """TODO: use input_id once we switch client submit API to return those."""
    def __init__(
        self,
        *,
        function_call_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id"]) -> None: ...

global___FunctionGetCallGraphRequest = FunctionGetCallGraphRequest

class FunctionGetCallGraphResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUTS_FIELD_NUMBER: builtins.int
    FUNCTION_CALLS_FIELD_NUMBER: builtins.int
    @property
    def inputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___InputCallGraphInfo]: ...
    @property
    def function_calls(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionCallCallGraphInfo]: ...
    def __init__(
        self,
        *,
        inputs: collections.abc.Iterable[global___InputCallGraphInfo] | None = ...,
        function_calls: collections.abc.Iterable[global___FunctionCallCallGraphInfo] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_calls", b"function_calls", "inputs", b"inputs"]) -> None: ...

global___FunctionGetCallGraphResponse = FunctionGetCallGraphResponse

class FunctionGetCurrentStatsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id"]) -> None: ...

global___FunctionGetCurrentStatsRequest = FunctionGetCurrentStatsRequest

class FunctionGetDynamicConcurrencyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    TARGET_CONCURRENCY_FIELD_NUMBER: builtins.int
    MAX_CONCURRENCY_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    target_concurrency: builtins.int
    max_concurrency: builtins.int
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        target_concurrency: builtins.int = ...,
        max_concurrency: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id", "max_concurrency", b"max_concurrency", "target_concurrency", b"target_concurrency"]) -> None: ...

global___FunctionGetDynamicConcurrencyRequest = FunctionGetDynamicConcurrencyRequest

class FunctionGetDynamicConcurrencyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONCURRENCY_FIELD_NUMBER: builtins.int
    concurrency: builtins.int
    def __init__(
        self,
        *,
        concurrency: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["concurrency", b"concurrency"]) -> None: ...

global___FunctionGetDynamicConcurrencyResponse = FunctionGetDynamicConcurrencyResponse

class FunctionGetInputsItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_ID_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    KILL_SWITCH_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_INVOCATION_TYPE_FIELD_NUMBER: builtins.int
    RETRY_COUNT_FIELD_NUMBER: builtins.int
    input_id: builtins.str
    @property
    def input(self) -> global___FunctionInput: ...
    kill_switch: builtins.bool
    function_call_id: builtins.str
    function_call_invocation_type: global___FunctionCallInvocationType.ValueType
    retry_count: builtins.int
    def __init__(
        self,
        *,
        input_id: builtins.str = ...,
        input: global___FunctionInput | None = ...,
        kill_switch: builtins.bool = ...,
        function_call_id: builtins.str = ...,
        function_call_invocation_type: global___FunctionCallInvocationType.ValueType = ...,
        retry_count: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["input", b"input"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id", "function_call_invocation_type", b"function_call_invocation_type", "input", b"input", "input_id", b"input_id", "kill_switch", b"kill_switch", "retry_count", b"retry_count"]) -> None: ...

global___FunctionGetInputsItem = FunctionGetInputsItem

class FunctionGetInputsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    MAX_VALUES_FIELD_NUMBER: builtins.int
    AVERAGE_CALL_TIME_FIELD_NUMBER: builtins.int
    INPUT_CONCURRENCY_FIELD_NUMBER: builtins.int
    BATCH_MAX_SIZE_FIELD_NUMBER: builtins.int
    BATCH_LINGER_MS_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    max_values: builtins.int
    average_call_time: builtins.float
    input_concurrency: builtins.int
    """Container aims to fetch multiple inputs at the same time"""
    batch_max_size: builtins.int
    """Maximum number of inputs to fetch at once"""
    batch_linger_ms: builtins.int
    """Miliseconds to block before a response is needed"""
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        max_values: builtins.int = ...,
        average_call_time: builtins.float = ...,
        input_concurrency: builtins.int = ...,
        batch_max_size: builtins.int = ...,
        batch_linger_ms: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["average_call_time", b"average_call_time", "batch_linger_ms", b"batch_linger_ms", "batch_max_size", b"batch_max_size", "function_id", b"function_id", "input_concurrency", b"input_concurrency", "max_values", b"max_values"]) -> None: ...

global___FunctionGetInputsRequest = FunctionGetInputsRequest

class FunctionGetInputsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUTS_FIELD_NUMBER: builtins.int
    RATE_LIMIT_SLEEP_DURATION_FIELD_NUMBER: builtins.int
    @property
    def inputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionGetInputsItem]: ...
    rate_limit_sleep_duration: builtins.float
    """How long to sleep before requesting another input."""
    def __init__(
        self,
        *,
        inputs: collections.abc.Iterable[global___FunctionGetInputsItem] | None = ...,
        rate_limit_sleep_duration: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["inputs", b"inputs", "rate_limit_sleep_duration", b"rate_limit_sleep_duration"]) -> None: ...

global___FunctionGetInputsResponse = FunctionGetInputsResponse

class FunctionGetOutputsItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    IDX_FIELD_NUMBER: builtins.int
    INPUT_ID_FIELD_NUMBER: builtins.int
    DATA_FORMAT_FIELD_NUMBER: builtins.int
    TASK_ID_FIELD_NUMBER: builtins.int
    INPUT_STARTED_AT_FIELD_NUMBER: builtins.int
    OUTPUT_CREATED_AT_FIELD_NUMBER: builtins.int
    RETRY_COUNT_FIELD_NUMBER: builtins.int
    FC_TRACE_TAG_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> global___GenericResult: ...
    idx: builtins.int
    input_id: builtins.str
    data_format: global___DataFormat.ValueType
    """for result.data_oneof"""
    task_id: builtins.str
    input_started_at: builtins.float
    output_created_at: builtins.float
    retry_count: builtins.int
    fc_trace_tag: builtins.str
    """datadog function call trace tag"""
    def __init__(
        self,
        *,
        result: global___GenericResult | None = ...,
        idx: builtins.int = ...,
        input_id: builtins.str = ...,
        data_format: global___DataFormat.ValueType = ...,
        task_id: builtins.str = ...,
        input_started_at: builtins.float = ...,
        output_created_at: builtins.float = ...,
        retry_count: builtins.int = ...,
        fc_trace_tag: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data_format", b"data_format", "fc_trace_tag", b"fc_trace_tag", "idx", b"idx", "input_id", b"input_id", "input_started_at", b"input_started_at", "output_created_at", b"output_created_at", "result", b"result", "retry_count", b"retry_count", "task_id", b"task_id"]) -> None: ...

global___FunctionGetOutputsItem = FunctionGetOutputsItem

class FunctionGetOutputsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    MAX_VALUES_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    LAST_ENTRY_ID_FIELD_NUMBER: builtins.int
    CLEAR_ON_SUCCESS_FIELD_NUMBER: builtins.int
    REQUESTED_AT_FIELD_NUMBER: builtins.int
    INPUT_JWTS_FIELD_NUMBER: builtins.int
    function_call_id: builtins.str
    max_values: builtins.int
    timeout: builtins.float
    last_entry_id: builtins.str
    clear_on_success: builtins.bool
    """expires *any* remaining outputs soon after this call, not just the returned ones"""
    requested_at: builtins.float
    """Used for waypoints."""
    @property
    def input_jwts(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """The jwts the client expects the server to be processing. This is optional and used for sync inputs only."""
    def __init__(
        self,
        *,
        function_call_id: builtins.str = ...,
        max_values: builtins.int = ...,
        timeout: builtins.float = ...,
        last_entry_id: builtins.str = ...,
        clear_on_success: builtins.bool = ...,
        requested_at: builtins.float = ...,
        input_jwts: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["clear_on_success", b"clear_on_success", "function_call_id", b"function_call_id", "input_jwts", b"input_jwts", "last_entry_id", b"last_entry_id", "max_values", b"max_values", "requested_at", b"requested_at", "timeout", b"timeout"]) -> None: ...

global___FunctionGetOutputsRequest = FunctionGetOutputsRequest

class FunctionGetOutputsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDXS_FIELD_NUMBER: builtins.int
    OUTPUTS_FIELD_NUMBER: builtins.int
    LAST_ENTRY_ID_FIELD_NUMBER: builtins.int
    NUM_UNFINISHED_INPUTS_FIELD_NUMBER: builtins.int
    @property
    def idxs(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def outputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionGetOutputsItem]: ...
    last_entry_id: builtins.str
    num_unfinished_inputs: builtins.int
    def __init__(
        self,
        *,
        idxs: collections.abc.Iterable[builtins.int] | None = ...,
        outputs: collections.abc.Iterable[global___FunctionGetOutputsItem] | None = ...,
        last_entry_id: builtins.str = ...,
        num_unfinished_inputs: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["idxs", b"idxs", "last_entry_id", b"last_entry_id", "num_unfinished_inputs", b"num_unfinished_inputs", "outputs", b"outputs"]) -> None: ...

global___FunctionGetOutputsResponse = FunctionGetOutputsResponse

class FunctionGetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_NAME_FIELD_NUMBER: builtins.int
    OBJECT_TAG_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    app_name: builtins.str
    object_tag: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    def __init__(
        self,
        *,
        app_name: builtins.str = ...,
        object_tag: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_name", b"app_name", "environment_name", b"environment_name", "namespace", b"namespace", "object_tag", b"object_tag"]) -> None: ...

global___FunctionGetRequest = FunctionGetRequest

class FunctionGetResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    HANDLE_METADATA_FIELD_NUMBER: builtins.int
    SERVER_WARNINGS_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    @property
    def handle_metadata(self) -> global___FunctionHandleMetadata: ...
    @property
    def server_warnings(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Warning]: ...
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        handle_metadata: global___FunctionHandleMetadata | None = ...,
        server_warnings: collections.abc.Iterable[global___Warning] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["handle_metadata", b"handle_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id", "handle_metadata", b"handle_metadata", "server_warnings", b"server_warnings"]) -> None: ...

global___FunctionGetResponse = FunctionGetResponse

class FunctionGetSerializedRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id"]) -> None: ...

global___FunctionGetSerializedRequest = FunctionGetSerializedRequest

class FunctionGetSerializedResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_SERIALIZED_FIELD_NUMBER: builtins.int
    CLASS_SERIALIZED_FIELD_NUMBER: builtins.int
    function_serialized: builtins.bytes
    class_serialized: builtins.bytes
    def __init__(
        self,
        *,
        function_serialized: builtins.bytes = ...,
        class_serialized: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["class_serialized", b"class_serialized", "function_serialized", b"function_serialized"]) -> None: ...

global___FunctionGetSerializedResponse = FunctionGetSerializedResponse

class FunctionHandleMetadata(google.protobuf.message.Message):
    """contains all the info about a function that is needed to trigger the right
    behaviour when using a FunctionHandler. Notably excludes things purely
    used for *executing* the function in a container entrypoint
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class MethodHandleMetadataEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___FunctionHandleMetadata: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___FunctionHandleMetadata | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    FUNCTION_NAME_FIELD_NUMBER: builtins.int
    FUNCTION_TYPE_FIELD_NUMBER: builtins.int
    WEB_URL_FIELD_NUMBER: builtins.int
    IS_METHOD_FIELD_NUMBER: builtins.int
    USE_FUNCTION_ID_FIELD_NUMBER: builtins.int
    USE_METHOD_NAME_FIELD_NUMBER: builtins.int
    DEFINITION_ID_FIELD_NUMBER: builtins.int
    CLASS_PARAMETER_INFO_FIELD_NUMBER: builtins.int
    METHOD_HANDLE_METADATA_FIELD_NUMBER: builtins.int
    FUNCTION_SCHEMA_FIELD_NUMBER: builtins.int
    INPUT_PLANE_URL_FIELD_NUMBER: builtins.int
    function_name: builtins.str
    """Should be a subset and use IDs/types from `Function` above"""
    function_type: global___Function.FunctionType.ValueType
    web_url: builtins.str
    is_method: builtins.bool
    use_function_id: builtins.str
    """used for methods"""
    use_method_name: builtins.str
    """used for methods"""
    definition_id: builtins.str
    @property
    def class_parameter_info(self) -> global___ClassParameterInfo: ...
    @property
    def method_handle_metadata(self) -> google.protobuf.internal.containers.MessageMap[builtins.str, global___FunctionHandleMetadata]:
        """Mapping of method names to their metadata, only non-empty for class service functions"""
    @property
    def function_schema(self) -> global___FunctionSchema: ...
    input_plane_url: builtins.str
    def __init__(
        self,
        *,
        function_name: builtins.str = ...,
        function_type: global___Function.FunctionType.ValueType = ...,
        web_url: builtins.str = ...,
        is_method: builtins.bool = ...,
        use_function_id: builtins.str = ...,
        use_method_name: builtins.str = ...,
        definition_id: builtins.str = ...,
        class_parameter_info: global___ClassParameterInfo | None = ...,
        method_handle_metadata: collections.abc.Mapping[builtins.str, global___FunctionHandleMetadata] | None = ...,
        function_schema: global___FunctionSchema | None = ...,
        input_plane_url: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_input_plane_url", b"_input_plane_url", "class_parameter_info", b"class_parameter_info", "function_schema", b"function_schema", "input_plane_url", b"input_plane_url"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_input_plane_url", b"_input_plane_url", "class_parameter_info", b"class_parameter_info", "definition_id", b"definition_id", "function_name", b"function_name", "function_schema", b"function_schema", "function_type", b"function_type", "input_plane_url", b"input_plane_url", "is_method", b"is_method", "method_handle_metadata", b"method_handle_metadata", "use_function_id", b"use_function_id", "use_method_name", b"use_method_name", "web_url", b"web_url"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_input_plane_url", b"_input_plane_url"]) -> typing_extensions.Literal["input_plane_url"] | None: ...

global___FunctionHandleMetadata = FunctionHandleMetadata

class FunctionInput(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARGS_FIELD_NUMBER: builtins.int
    ARGS_BLOB_ID_FIELD_NUMBER: builtins.int
    FINAL_INPUT_FIELD_NUMBER: builtins.int
    DATA_FORMAT_FIELD_NUMBER: builtins.int
    METHOD_NAME_FIELD_NUMBER: builtins.int
    args: builtins.bytes
    args_blob_id: builtins.str
    final_input: builtins.bool
    data_format: global___DataFormat.ValueType
    """For args_oneof."""
    method_name: builtins.str
    """specifies which method to call when calling a class/object function"""
    def __init__(
        self,
        *,
        args: builtins.bytes = ...,
        args_blob_id: builtins.str = ...,
        final_input: builtins.bool = ...,
        data_format: global___DataFormat.ValueType = ...,
        method_name: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_method_name", b"_method_name", "args", b"args", "args_blob_id", b"args_blob_id", "args_oneof", b"args_oneof", "method_name", b"method_name"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_method_name", b"_method_name", "args", b"args", "args_blob_id", b"args_blob_id", "args_oneof", b"args_oneof", "data_format", b"data_format", "final_input", b"final_input", "method_name", b"method_name"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_method_name", b"_method_name"]) -> typing_extensions.Literal["method_name"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["args_oneof", b"args_oneof"]) -> typing_extensions.Literal["args", "args_blob_id"] | None: ...

global___FunctionInput = FunctionInput

class FunctionMapRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    PARENT_INPUT_ID_FIELD_NUMBER: builtins.int
    RETURN_EXCEPTIONS_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_TYPE_FIELD_NUMBER: builtins.int
    PIPELINED_INPUTS_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_INVOCATION_TYPE_FIELD_NUMBER: builtins.int
    FROM_SPAWN_MAP_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    parent_input_id: builtins.str
    return_exceptions: builtins.bool
    function_call_type: global___FunctionCallType.ValueType
    @property
    def pipelined_inputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionPutInputsItem]: ...
    function_call_invocation_type: global___FunctionCallInvocationType.ValueType
    from_spawn_map: builtins.bool
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        parent_input_id: builtins.str = ...,
        return_exceptions: builtins.bool = ...,
        function_call_type: global___FunctionCallType.ValueType = ...,
        pipelined_inputs: collections.abc.Iterable[global___FunctionPutInputsItem] | None = ...,
        function_call_invocation_type: global___FunctionCallInvocationType.ValueType = ...,
        from_spawn_map: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["from_spawn_map", b"from_spawn_map", "function_call_invocation_type", b"function_call_invocation_type", "function_call_type", b"function_call_type", "function_id", b"function_id", "parent_input_id", b"parent_input_id", "pipelined_inputs", b"pipelined_inputs", "return_exceptions", b"return_exceptions"]) -> None: ...

global___FunctionMapRequest = FunctionMapRequest

class FunctionMapResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    PIPELINED_INPUTS_FIELD_NUMBER: builtins.int
    RETRY_POLICY_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_JWT_FIELD_NUMBER: builtins.int
    SYNC_CLIENT_RETRIES_ENABLED_FIELD_NUMBER: builtins.int
    MAX_INPUTS_OUTSTANDING_FIELD_NUMBER: builtins.int
    function_call_id: builtins.str
    @property
    def pipelined_inputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionPutInputsResponseItem]: ...
    @property
    def retry_policy(self) -> global___FunctionRetryPolicy: ...
    function_call_jwt: builtins.str
    sync_client_retries_enabled: builtins.bool
    max_inputs_outstanding: builtins.int
    def __init__(
        self,
        *,
        function_call_id: builtins.str = ...,
        pipelined_inputs: collections.abc.Iterable[global___FunctionPutInputsResponseItem] | None = ...,
        retry_policy: global___FunctionRetryPolicy | None = ...,
        function_call_jwt: builtins.str = ...,
        sync_client_retries_enabled: builtins.bool = ...,
        max_inputs_outstanding: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["retry_policy", b"retry_policy"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id", "function_call_jwt", b"function_call_jwt", "max_inputs_outstanding", b"max_inputs_outstanding", "pipelined_inputs", b"pipelined_inputs", "retry_policy", b"retry_policy", "sync_client_retries_enabled", b"sync_client_retries_enabled"]) -> None: ...

global___FunctionMapResponse = FunctionMapResponse

class FunctionOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SECRET_IDS_FIELD_NUMBER: builtins.int
    MOUNT_IDS_FIELD_NUMBER: builtins.int
    RESOURCES_FIELD_NUMBER: builtins.int
    RETRY_POLICY_FIELD_NUMBER: builtins.int
    CONCURRENCY_LIMIT_FIELD_NUMBER: builtins.int
    TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    TASK_IDLE_TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    WARM_POOL_SIZE_FIELD_NUMBER: builtins.int
    VOLUME_MOUNTS_FIELD_NUMBER: builtins.int
    TARGET_CONCURRENT_INPUTS_FIELD_NUMBER: builtins.int
    REPLACE_VOLUME_MOUNTS_FIELD_NUMBER: builtins.int
    REPLACE_SECRET_IDS_FIELD_NUMBER: builtins.int
    BUFFER_CONTAINERS_FIELD_NUMBER: builtins.int
    MAX_CONCURRENT_INPUTS_FIELD_NUMBER: builtins.int
    BATCH_MAX_SIZE_FIELD_NUMBER: builtins.int
    BATCH_LINGER_MS_FIELD_NUMBER: builtins.int
    @property
    def secret_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def mount_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """Currently not supported"""
    @property
    def resources(self) -> global___Resources: ...
    @property
    def retry_policy(self) -> global___FunctionRetryPolicy: ...
    concurrency_limit: builtins.int
    timeout_secs: builtins.int
    task_idle_timeout_secs: builtins.int
    warm_pool_size: builtins.int
    @property
    def volume_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___VolumeMount]: ...
    target_concurrent_inputs: builtins.int
    replace_volume_mounts: builtins.bool
    replace_secret_ids: builtins.bool
    buffer_containers: builtins.int
    max_concurrent_inputs: builtins.int
    batch_max_size: builtins.int
    batch_linger_ms: builtins.int
    def __init__(
        self,
        *,
        secret_ids: collections.abc.Iterable[builtins.str] | None = ...,
        mount_ids: collections.abc.Iterable[builtins.str] | None = ...,
        resources: global___Resources | None = ...,
        retry_policy: global___FunctionRetryPolicy | None = ...,
        concurrency_limit: builtins.int | None = ...,
        timeout_secs: builtins.int | None = ...,
        task_idle_timeout_secs: builtins.int | None = ...,
        warm_pool_size: builtins.int | None = ...,
        volume_mounts: collections.abc.Iterable[global___VolumeMount] | None = ...,
        target_concurrent_inputs: builtins.int | None = ...,
        replace_volume_mounts: builtins.bool = ...,
        replace_secret_ids: builtins.bool = ...,
        buffer_containers: builtins.int | None = ...,
        max_concurrent_inputs: builtins.int | None = ...,
        batch_max_size: builtins.int | None = ...,
        batch_linger_ms: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_batch_linger_ms", b"_batch_linger_ms", "_batch_max_size", b"_batch_max_size", "_buffer_containers", b"_buffer_containers", "_concurrency_limit", b"_concurrency_limit", "_max_concurrent_inputs", b"_max_concurrent_inputs", "_resources", b"_resources", "_retry_policy", b"_retry_policy", "_target_concurrent_inputs", b"_target_concurrent_inputs", "_task_idle_timeout_secs", b"_task_idle_timeout_secs", "_timeout_secs", b"_timeout_secs", "_warm_pool_size", b"_warm_pool_size", "batch_linger_ms", b"batch_linger_ms", "batch_max_size", b"batch_max_size", "buffer_containers", b"buffer_containers", "concurrency_limit", b"concurrency_limit", "max_concurrent_inputs", b"max_concurrent_inputs", "resources", b"resources", "retry_policy", b"retry_policy", "target_concurrent_inputs", b"target_concurrent_inputs", "task_idle_timeout_secs", b"task_idle_timeout_secs", "timeout_secs", b"timeout_secs", "warm_pool_size", b"warm_pool_size"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_batch_linger_ms", b"_batch_linger_ms", "_batch_max_size", b"_batch_max_size", "_buffer_containers", b"_buffer_containers", "_concurrency_limit", b"_concurrency_limit", "_max_concurrent_inputs", b"_max_concurrent_inputs", "_resources", b"_resources", "_retry_policy", b"_retry_policy", "_target_concurrent_inputs", b"_target_concurrent_inputs", "_task_idle_timeout_secs", b"_task_idle_timeout_secs", "_timeout_secs", b"_timeout_secs", "_warm_pool_size", b"_warm_pool_size", "batch_linger_ms", b"batch_linger_ms", "batch_max_size", b"batch_max_size", "buffer_containers", b"buffer_containers", "concurrency_limit", b"concurrency_limit", "max_concurrent_inputs", b"max_concurrent_inputs", "mount_ids", b"mount_ids", "replace_secret_ids", b"replace_secret_ids", "replace_volume_mounts", b"replace_volume_mounts", "resources", b"resources", "retry_policy", b"retry_policy", "secret_ids", b"secret_ids", "target_concurrent_inputs", b"target_concurrent_inputs", "task_idle_timeout_secs", b"task_idle_timeout_secs", "timeout_secs", b"timeout_secs", "volume_mounts", b"volume_mounts", "warm_pool_size", b"warm_pool_size"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_batch_linger_ms", b"_batch_linger_ms"]) -> typing_extensions.Literal["batch_linger_ms"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_batch_max_size", b"_batch_max_size"]) -> typing_extensions.Literal["batch_max_size"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_buffer_containers", b"_buffer_containers"]) -> typing_extensions.Literal["buffer_containers"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_concurrency_limit", b"_concurrency_limit"]) -> typing_extensions.Literal["concurrency_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_max_concurrent_inputs", b"_max_concurrent_inputs"]) -> typing_extensions.Literal["max_concurrent_inputs"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_resources", b"_resources"]) -> typing_extensions.Literal["resources"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_retry_policy", b"_retry_policy"]) -> typing_extensions.Literal["retry_policy"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_target_concurrent_inputs", b"_target_concurrent_inputs"]) -> typing_extensions.Literal["target_concurrent_inputs"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_task_idle_timeout_secs", b"_task_idle_timeout_secs"]) -> typing_extensions.Literal["task_idle_timeout_secs"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_timeout_secs", b"_timeout_secs"]) -> typing_extensions.Literal["timeout_secs"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_warm_pool_size", b"_warm_pool_size"]) -> typing_extensions.Literal["warm_pool_size"] | None: ...

global___FunctionOptions = FunctionOptions

class FunctionPrecreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class MethodDefinitionsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___MethodDefinition: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___MethodDefinition | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    APP_ID_FIELD_NUMBER: builtins.int
    FUNCTION_NAME_FIELD_NUMBER: builtins.int
    EXISTING_FUNCTION_ID_FIELD_NUMBER: builtins.int
    FUNCTION_TYPE_FIELD_NUMBER: builtins.int
    WEBHOOK_CONFIG_FIELD_NUMBER: builtins.int
    USE_FUNCTION_ID_FIELD_NUMBER: builtins.int
    USE_METHOD_NAME_FIELD_NUMBER: builtins.int
    METHOD_DEFINITIONS_FIELD_NUMBER: builtins.int
    FUNCTION_SCHEMA_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    function_name: builtins.str
    existing_function_id: builtins.str
    function_type: global___Function.FunctionType.ValueType
    @property
    def webhook_config(self) -> global___WebhookConfig: ...
    use_function_id: builtins.str
    """for class methods - use this function id instead for invocations - the *referenced* function should have is_class=True"""
    use_method_name: builtins.str
    """for class methods - this method name needs to be included in the FunctionInput"""
    @property
    def method_definitions(self) -> google.protobuf.internal.containers.MessageMap[builtins.str, global___MethodDefinition]:
        """Mapping of method names to method definitions, only non-empty for class service functions"""
    @property
    def function_schema(self) -> global___FunctionSchema: ...
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        function_name: builtins.str = ...,
        existing_function_id: builtins.str = ...,
        function_type: global___Function.FunctionType.ValueType = ...,
        webhook_config: global___WebhookConfig | None = ...,
        use_function_id: builtins.str = ...,
        use_method_name: builtins.str = ...,
        method_definitions: collections.abc.Mapping[builtins.str, global___MethodDefinition] | None = ...,
        function_schema: global___FunctionSchema | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["function_schema", b"function_schema", "webhook_config", b"webhook_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "existing_function_id", b"existing_function_id", "function_name", b"function_name", "function_schema", b"function_schema", "function_type", b"function_type", "method_definitions", b"method_definitions", "use_function_id", b"use_function_id", "use_method_name", b"use_method_name", "webhook_config", b"webhook_config"]) -> None: ...

global___FunctionPrecreateRequest = FunctionPrecreateRequest

class FunctionPrecreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    HANDLE_METADATA_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    @property
    def handle_metadata(self) -> global___FunctionHandleMetadata: ...
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        handle_metadata: global___FunctionHandleMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["handle_metadata", b"handle_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id", "handle_metadata", b"handle_metadata"]) -> None: ...

global___FunctionPrecreateResponse = FunctionPrecreateResponse

class FunctionPutInputsItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDX_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    idx: builtins.int
    @property
    def input(self) -> global___FunctionInput: ...
    def __init__(
        self,
        *,
        idx: builtins.int = ...,
        input: global___FunctionInput | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["input", b"input"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["idx", b"idx", "input", b"input"]) -> None: ...

global___FunctionPutInputsItem = FunctionPutInputsItem

class FunctionPutInputsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    INPUTS_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    function_call_id: builtins.str
    @property
    def inputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionPutInputsItem]: ...
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        function_call_id: builtins.str = ...,
        inputs: collections.abc.Iterable[global___FunctionPutInputsItem] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id", "function_id", b"function_id", "inputs", b"inputs"]) -> None: ...

global___FunctionPutInputsRequest = FunctionPutInputsRequest

class FunctionPutInputsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUTS_FIELD_NUMBER: builtins.int
    @property
    def inputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionPutInputsResponseItem]: ...
    def __init__(
        self,
        *,
        inputs: collections.abc.Iterable[global___FunctionPutInputsResponseItem] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["inputs", b"inputs"]) -> None: ...

global___FunctionPutInputsResponse = FunctionPutInputsResponse

class FunctionPutInputsResponseItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDX_FIELD_NUMBER: builtins.int
    INPUT_ID_FIELD_NUMBER: builtins.int
    INPUT_JWT_FIELD_NUMBER: builtins.int
    idx: builtins.int
    input_id: builtins.str
    input_jwt: builtins.str
    def __init__(
        self,
        *,
        idx: builtins.int = ...,
        input_id: builtins.str = ...,
        input_jwt: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["idx", b"idx", "input_id", b"input_id", "input_jwt", b"input_jwt"]) -> None: ...

global___FunctionPutInputsResponseItem = FunctionPutInputsResponseItem

class FunctionPutOutputsItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_ID_FIELD_NUMBER: builtins.int
    RESULT_FIELD_NUMBER: builtins.int
    INPUT_STARTED_AT_FIELD_NUMBER: builtins.int
    OUTPUT_CREATED_AT_FIELD_NUMBER: builtins.int
    DATA_FORMAT_FIELD_NUMBER: builtins.int
    RETRY_COUNT_FIELD_NUMBER: builtins.int
    input_id: builtins.str
    @property
    def result(self) -> global___GenericResult: ...
    input_started_at: builtins.float
    output_created_at: builtins.float
    data_format: global___DataFormat.ValueType
    """for result.data_oneof"""
    retry_count: builtins.int
    def __init__(
        self,
        *,
        input_id: builtins.str = ...,
        result: global___GenericResult | None = ...,
        input_started_at: builtins.float = ...,
        output_created_at: builtins.float = ...,
        data_format: global___DataFormat.ValueType = ...,
        retry_count: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data_format", b"data_format", "input_id", b"input_id", "input_started_at", b"input_started_at", "output_created_at", b"output_created_at", "result", b"result", "retry_count", b"retry_count"]) -> None: ...

global___FunctionPutOutputsItem = FunctionPutOutputsItem

class FunctionPutOutputsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OUTPUTS_FIELD_NUMBER: builtins.int
    REQUESTED_AT_FIELD_NUMBER: builtins.int
    @property
    def outputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionPutOutputsItem]: ...
    requested_at: builtins.float
    """Used for waypoints."""
    def __init__(
        self,
        *,
        outputs: collections.abc.Iterable[global___FunctionPutOutputsItem] | None = ...,
        requested_at: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["outputs", b"outputs", "requested_at", b"requested_at"]) -> None: ...

global___FunctionPutOutputsRequest = FunctionPutOutputsRequest

class FunctionRetryInputsItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_JWT_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    RETRY_COUNT_FIELD_NUMBER: builtins.int
    input_jwt: builtins.str
    @property
    def input(self) -> global___FunctionInput: ...
    retry_count: builtins.int
    def __init__(
        self,
        *,
        input_jwt: builtins.str = ...,
        input: global___FunctionInput | None = ...,
        retry_count: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["input", b"input"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["input", b"input", "input_jwt", b"input_jwt", "retry_count", b"retry_count"]) -> None: ...

global___FunctionRetryInputsItem = FunctionRetryInputsItem

class FunctionRetryInputsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_CALL_JWT_FIELD_NUMBER: builtins.int
    INPUTS_FIELD_NUMBER: builtins.int
    function_call_jwt: builtins.str
    @property
    def inputs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FunctionRetryInputsItem]: ...
    def __init__(
        self,
        *,
        function_call_jwt: builtins.str = ...,
        inputs: collections.abc.Iterable[global___FunctionRetryInputsItem] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_jwt", b"function_call_jwt", "inputs", b"inputs"]) -> None: ...

global___FunctionRetryInputsRequest = FunctionRetryInputsRequest

class FunctionRetryInputsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_JWTS_FIELD_NUMBER: builtins.int
    @property
    def input_jwts(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        input_jwts: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["input_jwts", b"input_jwts"]) -> None: ...

global___FunctionRetryInputsResponse = FunctionRetryInputsResponse

class FunctionRetryPolicy(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BACKOFF_COEFFICIENT_FIELD_NUMBER: builtins.int
    INITIAL_DELAY_MS_FIELD_NUMBER: builtins.int
    MAX_DELAY_MS_FIELD_NUMBER: builtins.int
    RETRIES_FIELD_NUMBER: builtins.int
    backoff_coefficient: builtins.float
    initial_delay_ms: builtins.int
    max_delay_ms: builtins.int
    retries: builtins.int
    """NOTE: two-byte field number not used for special reason. copy-paste error. Ref: PR #2542"""
    def __init__(
        self,
        *,
        backoff_coefficient: builtins.float = ...,
        initial_delay_ms: builtins.int = ...,
        max_delay_ms: builtins.int = ...,
        retries: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["backoff_coefficient", b"backoff_coefficient", "initial_delay_ms", b"initial_delay_ms", "max_delay_ms", b"max_delay_ms", "retries", b"retries"]) -> None: ...

global___FunctionRetryPolicy = FunctionRetryPolicy

class FunctionSchema(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _FunctionSchemaType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _FunctionSchemaTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[FunctionSchema._FunctionSchemaType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        FUNCTION_SCHEMA_UNSPECIFIED: FunctionSchema._FunctionSchemaType.ValueType  # 0
        FUNCTION_SCHEMA_V1: FunctionSchema._FunctionSchemaType.ValueType  # 1

    class FunctionSchemaType(_FunctionSchemaType, metaclass=_FunctionSchemaTypeEnumTypeWrapper): ...
    FUNCTION_SCHEMA_UNSPECIFIED: FunctionSchema.FunctionSchemaType.ValueType  # 0
    FUNCTION_SCHEMA_V1: FunctionSchema.FunctionSchemaType.ValueType  # 1

    SCHEMA_TYPE_FIELD_NUMBER: builtins.int
    ARGUMENTS_FIELD_NUMBER: builtins.int
    RETURN_TYPE_FIELD_NUMBER: builtins.int
    schema_type: global___FunctionSchema.FunctionSchemaType.ValueType
    """allows easy disambiguation between empty schema and no schema collection etc."""
    @property
    def arguments(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ClassParameterSpec]: ...
    @property
    def return_type(self) -> global___GenericPayloadType: ...
    def __init__(
        self,
        *,
        schema_type: global___FunctionSchema.FunctionSchemaType.ValueType = ...,
        arguments: collections.abc.Iterable[global___ClassParameterSpec] | None = ...,
        return_type: global___GenericPayloadType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["return_type", b"return_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["arguments", b"arguments", "return_type", b"return_type", "schema_type", b"schema_type"]) -> None: ...

global___FunctionSchema = FunctionSchema

class FunctionStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BACKLOG_FIELD_NUMBER: builtins.int
    NUM_TOTAL_TASKS_FIELD_NUMBER: builtins.int
    backlog: builtins.int
    num_total_tasks: builtins.int
    def __init__(
        self,
        *,
        backlog: builtins.int = ...,
        num_total_tasks: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["backlog", b"backlog", "num_total_tasks", b"num_total_tasks"]) -> None: ...

global___FunctionStats = FunctionStats

class FunctionUpdateSchedulingParamsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_ID_FIELD_NUMBER: builtins.int
    WARM_POOL_SIZE_OVERRIDE_FIELD_NUMBER: builtins.int
    SETTINGS_FIELD_NUMBER: builtins.int
    function_id: builtins.str
    warm_pool_size_override: builtins.int
    @property
    def settings(self) -> global___AutoscalerSettings: ...
    def __init__(
        self,
        *,
        function_id: builtins.str = ...,
        warm_pool_size_override: builtins.int = ...,
        settings: global___AutoscalerSettings | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["settings", b"settings"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_id", b"function_id", "settings", b"settings", "warm_pool_size_override", b"warm_pool_size_override"]) -> None: ...

global___FunctionUpdateSchedulingParamsRequest = FunctionUpdateSchedulingParamsRequest

class FunctionUpdateSchedulingParamsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___FunctionUpdateSchedulingParamsResponse = FunctionUpdateSchedulingParamsResponse

class GPUConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    COUNT_FIELD_NUMBER: builtins.int
    GPU_TYPE_FIELD_NUMBER: builtins.int
    type: global___GPUType.ValueType
    """Deprecated, at some point"""
    count: builtins.int
    gpu_type: builtins.str
    def __init__(
        self,
        *,
        type: global___GPUType.ValueType = ...,
        count: builtins.int = ...,
        gpu_type: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["count", b"count", "gpu_type", b"gpu_type", "type", b"type"]) -> None: ...

global___GPUConfig = GPUConfig

class GeneratorDone(google.protobuf.message.Message):
    """Sent as the output when a generator finishes running."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEMS_TOTAL_FIELD_NUMBER: builtins.int
    items_total: builtins.int
    def __init__(
        self,
        *,
        items_total: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["items_total", b"items_total"]) -> None: ...

global___GeneratorDone = GeneratorDone

class GenericPayloadType(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BASE_TYPE_FIELD_NUMBER: builtins.int
    SUB_TYPES_FIELD_NUMBER: builtins.int
    base_type: global___ParameterType.ValueType
    @property
    def sub_types(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___GenericPayloadType]:
        """sub-type for generic types like lists"""
    def __init__(
        self,
        *,
        base_type: global___ParameterType.ValueType = ...,
        sub_types: collections.abc.Iterable[global___GenericPayloadType] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["base_type", b"base_type", "sub_types", b"sub_types"]) -> None: ...

global___GenericPayloadType = GenericPayloadType

class GenericResult(google.protobuf.message.Message):
    """Used for both tasks and function outputs"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _GenericStatus:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _GenericStatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[GenericResult._GenericStatus.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        GENERIC_STATUS_UNSPECIFIED: GenericResult._GenericStatus.ValueType  # 0
        GENERIC_STATUS_SUCCESS: GenericResult._GenericStatus.ValueType  # 1
        GENERIC_STATUS_FAILURE: GenericResult._GenericStatus.ValueType  # 2
        GENERIC_STATUS_TERMINATED: GenericResult._GenericStatus.ValueType  # 3
        """Used when a task was killed using an external signal."""
        GENERIC_STATUS_TIMEOUT: GenericResult._GenericStatus.ValueType  # 4
        GENERIC_STATUS_INIT_FAILURE: GenericResult._GenericStatus.ValueType  # 5
        """Used when the user's function fails to initialize (ex. S3 mount failed due to invalid credentials).
        Terminates the function and all remaining inputs.
        """
        GENERIC_STATUS_INTERNAL_FAILURE: GenericResult._GenericStatus.ValueType  # 6

    class GenericStatus(_GenericStatus, metaclass=_GenericStatusEnumTypeWrapper): ...
    GENERIC_STATUS_UNSPECIFIED: GenericResult.GenericStatus.ValueType  # 0
    GENERIC_STATUS_SUCCESS: GenericResult.GenericStatus.ValueType  # 1
    GENERIC_STATUS_FAILURE: GenericResult.GenericStatus.ValueType  # 2
    GENERIC_STATUS_TERMINATED: GenericResult.GenericStatus.ValueType  # 3
    """Used when a task was killed using an external signal."""
    GENERIC_STATUS_TIMEOUT: GenericResult.GenericStatus.ValueType  # 4
    GENERIC_STATUS_INIT_FAILURE: GenericResult.GenericStatus.ValueType  # 5
    """Used when the user's function fails to initialize (ex. S3 mount failed due to invalid credentials).
    Terminates the function and all remaining inputs.
    """
    GENERIC_STATUS_INTERNAL_FAILURE: GenericResult.GenericStatus.ValueType  # 6

    STATUS_FIELD_NUMBER: builtins.int
    EXCEPTION_FIELD_NUMBER: builtins.int
    EXITCODE_FIELD_NUMBER: builtins.int
    TRACEBACK_FIELD_NUMBER: builtins.int
    SERIALIZED_TB_FIELD_NUMBER: builtins.int
    TB_LINE_CACHE_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    DATA_BLOB_ID_FIELD_NUMBER: builtins.int
    PROPAGATION_REASON_FIELD_NUMBER: builtins.int
    status: global___GenericResult.GenericStatus.ValueType
    """Status of the task or function output."""
    exception: builtins.str
    """Exception message for failures, if available."""
    exitcode: builtins.int
    """Status code of the container entrypoint or builder process if it terminates unexpectedly."""
    traceback: builtins.str
    """String value of the Python traceback."""
    serialized_tb: builtins.bytes
    """Pickled traceback object."""
    tb_line_cache: builtins.bytes
    """Pickled line cache for traceback object."""
    data: builtins.bytes
    """Inline data of the result."""
    data_blob_id: builtins.str
    """Blob ID for large data."""
    propagation_reason: builtins.str
    """(?)"""
    def __init__(
        self,
        *,
        status: global___GenericResult.GenericStatus.ValueType = ...,
        exception: builtins.str = ...,
        exitcode: builtins.int = ...,
        traceback: builtins.str = ...,
        serialized_tb: builtins.bytes = ...,
        tb_line_cache: builtins.bytes = ...,
        data: builtins.bytes = ...,
        data_blob_id: builtins.str = ...,
        propagation_reason: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof", "exception", b"exception", "exitcode", b"exitcode", "propagation_reason", b"propagation_reason", "serialized_tb", b"serialized_tb", "status", b"status", "tb_line_cache", b"tb_line_cache", "traceback", b"traceback"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["data_oneof", b"data_oneof"]) -> typing_extensions.Literal["data", "data_blob_id"] | None: ...

global___GenericResult = GenericResult

class Image(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BASE_IMAGES_FIELD_NUMBER: builtins.int
    DOCKERFILE_COMMANDS_FIELD_NUMBER: builtins.int
    CONTEXT_FILES_FIELD_NUMBER: builtins.int
    VERSION_FIELD_NUMBER: builtins.int
    SECRET_IDS_FIELD_NUMBER: builtins.int
    CONTEXT_MOUNT_ID_FIELD_NUMBER: builtins.int
    GPU_CONFIG_FIELD_NUMBER: builtins.int
    IMAGE_REGISTRY_CONFIG_FIELD_NUMBER: builtins.int
    BUILD_FUNCTION_DEF_FIELD_NUMBER: builtins.int
    BUILD_FUNCTION_GLOBALS_FIELD_NUMBER: builtins.int
    RUNTIME_FIELD_NUMBER: builtins.int
    RUNTIME_DEBUG_FIELD_NUMBER: builtins.int
    BUILD_FUNCTION_FIELD_NUMBER: builtins.int
    @property
    def base_images(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___BaseImage]: ...
    @property
    def dockerfile_commands(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def context_files(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ImageContextFile]: ...
    version: builtins.str
    @property
    def secret_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    context_mount_id: builtins.str
    """Part of Image definition, because presence of GPU drivers
    affects the image that's built.
    """
    @property
    def gpu_config(self) -> global___GPUConfig: ...
    @property
    def image_registry_config(self) -> global___ImageRegistryConfig: ...
    build_function_def: builtins.str
    """deprecated after 0.58.96"""
    build_function_globals: builtins.bytes
    """deprecated after 0.58.96"""
    runtime: builtins.str
    """If set, overrides the runtime used by the function. Specify either "runc" or "gvisor"."""
    runtime_debug: builtins.bool
    """Not included in image definition checksum as debug features do not affect built image."""
    @property
    def build_function(self) -> global___BuildFunction: ...
    def __init__(
        self,
        *,
        base_images: collections.abc.Iterable[global___BaseImage] | None = ...,
        dockerfile_commands: collections.abc.Iterable[builtins.str] | None = ...,
        context_files: collections.abc.Iterable[global___ImageContextFile] | None = ...,
        version: builtins.str = ...,
        secret_ids: collections.abc.Iterable[builtins.str] | None = ...,
        context_mount_id: builtins.str = ...,
        gpu_config: global___GPUConfig | None = ...,
        image_registry_config: global___ImageRegistryConfig | None = ...,
        build_function_def: builtins.str = ...,
        build_function_globals: builtins.bytes = ...,
        runtime: builtins.str = ...,
        runtime_debug: builtins.bool = ...,
        build_function: global___BuildFunction | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["build_function", b"build_function", "gpu_config", b"gpu_config", "image_registry_config", b"image_registry_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["base_images", b"base_images", "build_function", b"build_function", "build_function_def", b"build_function_def", "build_function_globals", b"build_function_globals", "context_files", b"context_files", "context_mount_id", b"context_mount_id", "dockerfile_commands", b"dockerfile_commands", "gpu_config", b"gpu_config", "image_registry_config", b"image_registry_config", "runtime", b"runtime", "runtime_debug", b"runtime_debug", "secret_ids", b"secret_ids", "version", b"version"]) -> None: ...

global___Image = Image

class ImageContextFile(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILENAME_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    filename: builtins.str
    data: builtins.bytes
    def __init__(
        self,
        *,
        filename: builtins.str = ...,
        data: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "filename", b"filename"]) -> None: ...

global___ImageContextFile = ImageContextFile

class ImageFromIdRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMAGE_ID_FIELD_NUMBER: builtins.int
    image_id: builtins.str
    def __init__(
        self,
        *,
        image_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["image_id", b"image_id"]) -> None: ...

global___ImageFromIdRequest = ImageFromIdRequest

class ImageFromIdResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMAGE_ID_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    image_id: builtins.str
    @property
    def metadata(self) -> global___ImageMetadata: ...
    def __init__(
        self,
        *,
        image_id: builtins.str = ...,
        metadata: global___ImageMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["metadata", b"metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["image_id", b"image_id", "metadata", b"metadata"]) -> None: ...

global___ImageFromIdResponse = ImageFromIdResponse

class ImageGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMAGE_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    EXISTING_IMAGE_ID_FIELD_NUMBER: builtins.int
    BUILD_FUNCTION_ID_FIELD_NUMBER: builtins.int
    FORCE_BUILD_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    BUILDER_VERSION_FIELD_NUMBER: builtins.int
    ALLOW_GLOBAL_DEPLOYMENT_FIELD_NUMBER: builtins.int
    IGNORE_CACHE_FIELD_NUMBER: builtins.int
    @property
    def image(self) -> global___Image: ...
    app_id: builtins.str
    existing_image_id: builtins.str
    """ignored"""
    build_function_id: builtins.str
    force_build: builtins.bool
    namespace: global___DeploymentNamespace.ValueType
    builder_version: builtins.str
    allow_global_deployment: builtins.bool
    """Only admins can publish global images, but this provides an extra failsafe"""
    ignore_cache: builtins.bool
    """Force the Image to build but don't clobber any Images with the same recipe in the cache"""
    def __init__(
        self,
        *,
        image: global___Image | None = ...,
        app_id: builtins.str = ...,
        existing_image_id: builtins.str = ...,
        build_function_id: builtins.str = ...,
        force_build: builtins.bool = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        builder_version: builtins.str = ...,
        allow_global_deployment: builtins.bool = ...,
        ignore_cache: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["image", b"image"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["allow_global_deployment", b"allow_global_deployment", "app_id", b"app_id", "build_function_id", b"build_function_id", "builder_version", b"builder_version", "existing_image_id", b"existing_image_id", "force_build", b"force_build", "ignore_cache", b"ignore_cache", "image", b"image", "namespace", b"namespace"]) -> None: ...

global___ImageGetOrCreateRequest = ImageGetOrCreateRequest

class ImageGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMAGE_ID_FIELD_NUMBER: builtins.int
    RESULT_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    image_id: builtins.str
    """image_id is set regardless if the image is built (use ImageJoinStreaming to wait for build)"""
    @property
    def result(self) -> global___GenericResult:
        """result of build - only set if the image has finished building (regardless if success or not)"""
    @property
    def metadata(self) -> global___ImageMetadata:
        """image metadata - only set if the image has built successfully"""
    def __init__(
        self,
        *,
        image_id: builtins.str = ...,
        result: global___GenericResult | None = ...,
        metadata: global___ImageMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["metadata", b"metadata", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["image_id", b"image_id", "metadata", b"metadata", "result", b"result"]) -> None: ...

global___ImageGetOrCreateResponse = ImageGetOrCreateResponse

class ImageJoinStreamingRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMAGE_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    LAST_ENTRY_ID_FIELD_NUMBER: builtins.int
    INCLUDE_LOGS_FOR_FINISHED_FIELD_NUMBER: builtins.int
    image_id: builtins.str
    timeout: builtins.float
    last_entry_id: builtins.str
    include_logs_for_finished: builtins.bool
    def __init__(
        self,
        *,
        image_id: builtins.str = ...,
        timeout: builtins.float = ...,
        last_entry_id: builtins.str = ...,
        include_logs_for_finished: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["image_id", b"image_id", "include_logs_for_finished", b"include_logs_for_finished", "last_entry_id", b"last_entry_id", "timeout", b"timeout"]) -> None: ...

global___ImageJoinStreamingRequest = ImageJoinStreamingRequest

class ImageJoinStreamingResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    TASK_LOGS_FIELD_NUMBER: builtins.int
    ENTRY_ID_FIELD_NUMBER: builtins.int
    EOF_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> global___GenericResult: ...
    @property
    def task_logs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TaskLogs]: ...
    entry_id: builtins.str
    eof: builtins.bool
    @property
    def metadata(self) -> global___ImageMetadata:
        """set on success"""
    def __init__(
        self,
        *,
        result: global___GenericResult | None = ...,
        task_logs: collections.abc.Iterable[global___TaskLogs] | None = ...,
        entry_id: builtins.str = ...,
        eof: builtins.bool = ...,
        metadata: global___ImageMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["metadata", b"metadata", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["entry_id", b"entry_id", "eof", b"eof", "metadata", b"metadata", "result", b"result", "task_logs", b"task_logs"]) -> None: ...

global___ImageJoinStreamingResponse = ImageJoinStreamingResponse

class ImageMetadata(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class PythonPackagesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    PYTHON_VERSION_INFO_FIELD_NUMBER: builtins.int
    PYTHON_PACKAGES_FIELD_NUMBER: builtins.int
    WORKDIR_FIELD_NUMBER: builtins.int
    LIBC_VERSION_INFO_FIELD_NUMBER: builtins.int
    IMAGE_BUILDER_VERSION_FIELD_NUMBER: builtins.int
    python_version_info: builtins.str
    """The output of `python -VV. Not set if missing"""
    @property
    def python_packages(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """Installed python packages, as listed by by `pip list`.
        package name -> version. Empty if missing
        """
    workdir: builtins.str
    """The working directory of the image, as an absolute file path.

    For most images, this is not set, which means to use the default workdir:
    - On function runners, the default is `/root` (home directory).
    - For image builds and sandbox environments, it is `/`.
    """
    libc_version_info: builtins.str
    """The version of glibc in this image, if any."""
    image_builder_version: builtins.str
    """The builder version for/with which the image was created."""
    def __init__(
        self,
        *,
        python_version_info: builtins.str | None = ...,
        python_packages: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        workdir: builtins.str | None = ...,
        libc_version_info: builtins.str | None = ...,
        image_builder_version: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_image_builder_version", b"_image_builder_version", "_libc_version_info", b"_libc_version_info", "_python_version_info", b"_python_version_info", "_workdir", b"_workdir", "image_builder_version", b"image_builder_version", "libc_version_info", b"libc_version_info", "python_version_info", b"python_version_info", "workdir", b"workdir"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_image_builder_version", b"_image_builder_version", "_libc_version_info", b"_libc_version_info", "_python_version_info", b"_python_version_info", "_workdir", b"_workdir", "image_builder_version", b"image_builder_version", "libc_version_info", b"libc_version_info", "python_packages", b"python_packages", "python_version_info", b"python_version_info", "workdir", b"workdir"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_image_builder_version", b"_image_builder_version"]) -> typing_extensions.Literal["image_builder_version"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_libc_version_info", b"_libc_version_info"]) -> typing_extensions.Literal["libc_version_info"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_python_version_info", b"_python_version_info"]) -> typing_extensions.Literal["python_version_info"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_workdir", b"_workdir"]) -> typing_extensions.Literal["workdir"] | None: ...

global___ImageMetadata = ImageMetadata

class ImageRegistryConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REGISTRY_AUTH_TYPE_FIELD_NUMBER: builtins.int
    SECRET_ID_FIELD_NUMBER: builtins.int
    registry_auth_type: global___RegistryAuthType.ValueType
    secret_id: builtins.str
    def __init__(
        self,
        *,
        registry_auth_type: global___RegistryAuthType.ValueType = ...,
        secret_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["registry_auth_type", b"registry_auth_type", "secret_id", b"secret_id"]) -> None: ...

global___ImageRegistryConfig = ImageRegistryConfig

class InputCallGraphInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_ID_FIELD_NUMBER: builtins.int
    STATUS_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    TASK_ID_FIELD_NUMBER: builtins.int
    input_id: builtins.str
    status: global___GenericResult.GenericStatus.ValueType
    function_call_id: builtins.str
    task_id: builtins.str
    def __init__(
        self,
        *,
        input_id: builtins.str = ...,
        status: global___GenericResult.GenericStatus.ValueType = ...,
        function_call_id: builtins.str = ...,
        task_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["function_call_id", b"function_call_id", "input_id", b"input_id", "status", b"status", "task_id", b"task_id"]) -> None: ...

global___InputCallGraphInfo = InputCallGraphInfo

class InputCategoryInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TOTAL_FIELD_NUMBER: builtins.int
    LATEST_FIELD_NUMBER: builtins.int
    total: builtins.int
    @property
    def latest(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___InputInfo]: ...
    def __init__(
        self,
        *,
        total: builtins.int = ...,
        latest: collections.abc.Iterable[global___InputInfo] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["latest", b"latest", "total", b"total"]) -> None: ...

global___InputCategoryInfo = InputCategoryInfo

class InputInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_ID_FIELD_NUMBER: builtins.int
    IDX_FIELD_NUMBER: builtins.int
    TASK_ID_FIELD_NUMBER: builtins.int
    STARTED_AT_FIELD_NUMBER: builtins.int
    FINISHED_AT_FIELD_NUMBER: builtins.int
    TASK_STARTUP_TIME_FIELD_NUMBER: builtins.int
    TASK_FIRST_INPUT_FIELD_NUMBER: builtins.int
    input_id: builtins.str
    idx: builtins.int
    task_id: builtins.str
    started_at: builtins.float
    finished_at: builtins.float
    task_startup_time: builtins.float
    task_first_input: builtins.bool
    def __init__(
        self,
        *,
        input_id: builtins.str = ...,
        idx: builtins.int = ...,
        task_id: builtins.str = ...,
        started_at: builtins.float = ...,
        finished_at: builtins.float = ...,
        task_startup_time: builtins.float = ...,
        task_first_input: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["finished_at", b"finished_at", "idx", b"idx", "input_id", b"input_id", "started_at", b"started_at", "task_first_input", b"task_first_input", "task_id", b"task_id", "task_startup_time", b"task_startup_time"]) -> None: ...

global___InputInfo = InputInfo

class MethodDefinition(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FUNCTION_NAME_FIELD_NUMBER: builtins.int
    FUNCTION_TYPE_FIELD_NUMBER: builtins.int
    WEBHOOK_CONFIG_FIELD_NUMBER: builtins.int
    WEB_URL_FIELD_NUMBER: builtins.int
    WEB_URL_INFO_FIELD_NUMBER: builtins.int
    CUSTOM_DOMAIN_INFO_FIELD_NUMBER: builtins.int
    FUNCTION_SCHEMA_FIELD_NUMBER: builtins.int
    function_name: builtins.str
    function_type: global___Function.FunctionType.ValueType
    @property
    def webhook_config(self) -> global___WebhookConfig: ...
    web_url: builtins.str
    @property
    def web_url_info(self) -> global___WebUrlInfo: ...
    @property
    def custom_domain_info(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CustomDomainInfo]: ...
    @property
    def function_schema(self) -> global___FunctionSchema: ...
    def __init__(
        self,
        *,
        function_name: builtins.str = ...,
        function_type: global___Function.FunctionType.ValueType = ...,
        webhook_config: global___WebhookConfig | None = ...,
        web_url: builtins.str = ...,
        web_url_info: global___WebUrlInfo | None = ...,
        custom_domain_info: collections.abc.Iterable[global___CustomDomainInfo] | None = ...,
        function_schema: global___FunctionSchema | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["function_schema", b"function_schema", "web_url_info", b"web_url_info", "webhook_config", b"webhook_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["custom_domain_info", b"custom_domain_info", "function_name", b"function_name", "function_schema", b"function_schema", "function_type", b"function_type", "web_url", b"web_url", "web_url_info", b"web_url_info", "webhook_config", b"webhook_config"]) -> None: ...

global___MethodDefinition = MethodDefinition

class MountFile(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILENAME_FIELD_NUMBER: builtins.int
    SHA256_HEX_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    MODE_FIELD_NUMBER: builtins.int
    filename: builtins.str
    sha256_hex: builtins.str
    """SHA-256 checksum of the file."""
    size: builtins.int
    """Size of the file in bytes — ignored in MountBuild()."""
    mode: builtins.int
    """Unix file permission bits `st_mode`."""
    def __init__(
        self,
        *,
        filename: builtins.str = ...,
        sha256_hex: builtins.str = ...,
        size: builtins.int | None = ...,
        mode: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_mode", b"_mode", "_size", b"_size", "mode", b"mode", "size", b"size"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_mode", b"_mode", "_size", b"_size", "filename", b"filename", "mode", b"mode", "sha256_hex", b"sha256_hex", "size", b"size"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_mode", b"_mode"]) -> typing_extensions.Literal["mode"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_size", b"_size"]) -> typing_extensions.Literal["size"] | None: ...

global___MountFile = MountFile

class MountGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEPLOYMENT_NAME_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    FILES_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    deployment_name: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    @property
    def files(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MountFile]: ...
    app_id: builtins.str
    """only used with OBJECT_CREATION_TYPE_ANONYMOUS_OWNED_BY_APP"""
    def __init__(
        self,
        *,
        deployment_name: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
        files: collections.abc.Iterable[global___MountFile] | None = ...,
        app_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "deployment_name", b"deployment_name", "environment_name", b"environment_name", "files", b"files", "namespace", b"namespace", "object_creation_type", b"object_creation_type"]) -> None: ...

global___MountGetOrCreateRequest = MountGetOrCreateRequest

class MountGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MOUNT_ID_FIELD_NUMBER: builtins.int
    HANDLE_METADATA_FIELD_NUMBER: builtins.int
    mount_id: builtins.str
    @property
    def handle_metadata(self) -> global___MountHandleMetadata: ...
    def __init__(
        self,
        *,
        mount_id: builtins.str = ...,
        handle_metadata: global___MountHandleMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["handle_metadata", b"handle_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["handle_metadata", b"handle_metadata", "mount_id", b"mount_id"]) -> None: ...

global___MountGetOrCreateResponse = MountGetOrCreateResponse

class MountHandleMetadata(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONTENT_CHECKSUM_SHA256_HEX_FIELD_NUMBER: builtins.int
    content_checksum_sha256_hex: builtins.str
    def __init__(
        self,
        *,
        content_checksum_sha256_hex: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["content_checksum_sha256_hex", b"content_checksum_sha256_hex"]) -> None: ...

global___MountHandleMetadata = MountHandleMetadata

class MountPutFileRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHA256_HEX_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    DATA_BLOB_ID_FIELD_NUMBER: builtins.int
    sha256_hex: builtins.str
    data: builtins.bytes
    data_blob_id: builtins.str
    def __init__(
        self,
        *,
        sha256_hex: builtins.str = ...,
        data: builtins.bytes = ...,
        data_blob_id: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof", "sha256_hex", b"sha256_hex"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["data_oneof", b"data_oneof"]) -> typing_extensions.Literal["data", "data_blob_id"] | None: ...

global___MountPutFileRequest = MountPutFileRequest

class MountPutFileResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXISTS_FIELD_NUMBER: builtins.int
    exists: builtins.bool
    def __init__(
        self,
        *,
        exists: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["exists", b"exists"]) -> None: ...

global___MountPutFileResponse = MountPutFileResponse

class MultiPartUpload(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PART_LENGTH_FIELD_NUMBER: builtins.int
    UPLOAD_URLS_FIELD_NUMBER: builtins.int
    COMPLETION_URL_FIELD_NUMBER: builtins.int
    part_length: builtins.int
    """split upload based on this part length - all except the last part must have this length"""
    @property
    def upload_urls(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    completion_url: builtins.str
    def __init__(
        self,
        *,
        part_length: builtins.int = ...,
        upload_urls: collections.abc.Iterable[builtins.str] | None = ...,
        completion_url: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["completion_url", b"completion_url", "part_length", b"part_length", "upload_urls", b"upload_urls"]) -> None: ...

global___MultiPartUpload = MultiPartUpload

class NetworkAccess(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _NetworkAccessType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _NetworkAccessTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[NetworkAccess._NetworkAccessType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSPECIFIED: NetworkAccess._NetworkAccessType.ValueType  # 0
        OPEN: NetworkAccess._NetworkAccessType.ValueType  # 1
        BLOCKED: NetworkAccess._NetworkAccessType.ValueType  # 2
        ALLOWLIST: NetworkAccess._NetworkAccessType.ValueType  # 3

    class NetworkAccessType(_NetworkAccessType, metaclass=_NetworkAccessTypeEnumTypeWrapper): ...
    UNSPECIFIED: NetworkAccess.NetworkAccessType.ValueType  # 0
    OPEN: NetworkAccess.NetworkAccessType.ValueType  # 1
    BLOCKED: NetworkAccess.NetworkAccessType.ValueType  # 2
    ALLOWLIST: NetworkAccess.NetworkAccessType.ValueType  # 3

    NETWORK_ACCESS_TYPE_FIELD_NUMBER: builtins.int
    ALLOWED_CIDRS_FIELD_NUMBER: builtins.int
    network_access_type: global___NetworkAccess.NetworkAccessType.ValueType
    @property
    def allowed_cidrs(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        network_access_type: global___NetworkAccess.NetworkAccessType.ValueType = ...,
        allowed_cidrs: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["allowed_cidrs", b"allowed_cidrs", "network_access_type", b"network_access_type"]) -> None: ...

global___NetworkAccess = NetworkAccess

class NotebookKernelPublishResultsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class ExecuteReply(google.protobuf.message.Message):
        """See kernelshim.py for the differences between this and `ExecuteResult`.
        https://jupyter-client.readthedocs.io/en/stable/messaging.html#execution-results
        """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        STATUS_FIELD_NUMBER: builtins.int
        EXECUTION_COUNT_FIELD_NUMBER: builtins.int
        DURATION_FIELD_NUMBER: builtins.int
        status: builtins.str
        execution_count: builtins.int
        duration: builtins.float
        def __init__(
            self,
            *,
            status: builtins.str = ...,
            execution_count: builtins.int = ...,
            duration: builtins.float = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["duration", b"duration", "execution_count", b"execution_count", "status", b"status"]) -> None: ...

    class CellResult(google.protobuf.message.Message):
        """IOPub message or reply received from the kernel for a cell."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        CELL_ID_FIELD_NUMBER: builtins.int
        OUTPUT_FIELD_NUMBER: builtins.int
        CLEAR_OUTPUT_FIELD_NUMBER: builtins.int
        EXECUTE_REPLY_FIELD_NUMBER: builtins.int
        cell_id: builtins.str
        @property
        def output(self) -> global___NotebookOutput:
            """Persistent output that is saved in the notebook."""
        clear_output: builtins.bool
        """Clear all previous outputs of the cell."""
        @property
        def execute_reply(self) -> global___NotebookKernelPublishResultsRequest.ExecuteReply:
            """Cell has finished executing, return the kernel's execute_reply."""
        def __init__(
            self,
            *,
            cell_id: builtins.str = ...,
            output: global___NotebookOutput | None = ...,
            clear_output: builtins.bool = ...,
            execute_reply: global___NotebookKernelPublishResultsRequest.ExecuteReply | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["clear_output", b"clear_output", "execute_reply", b"execute_reply", "output", b"output", "result_type", b"result_type"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["cell_id", b"cell_id", "clear_output", b"clear_output", "execute_reply", b"execute_reply", "output", b"output", "result_type", b"result_type"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["result_type", b"result_type"]) -> typing_extensions.Literal["output", "clear_output", "execute_reply"] | None: ...

    NOTEBOOK_ID_FIELD_NUMBER: builtins.int
    RESULTS_FIELD_NUMBER: builtins.int
    notebook_id: builtins.str
    @property
    def results(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___NotebookKernelPublishResultsRequest.CellResult]: ...
    def __init__(
        self,
        *,
        notebook_id: builtins.str = ...,
        results: collections.abc.Iterable[global___NotebookKernelPublishResultsRequest.CellResult] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["notebook_id", b"notebook_id", "results", b"results"]) -> None: ...

global___NotebookKernelPublishResultsRequest = NotebookKernelPublishResultsRequest

class NotebookOutput(google.protobuf.message.Message):
    """A single output from a notebook. When you execute a cell, it produces an
    array of these outputs as the code runs.

    https://github.com/jupyter/nbformat/blob/v5.10.4/nbformat/v4/nbformat.v4.schema.json#L301-L309
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class ExecuteResult(google.protobuf.message.Message):
        """Result of executing a code cell."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        EXECUTION_COUNT_FIELD_NUMBER: builtins.int
        DATA_FIELD_NUMBER: builtins.int
        METADATA_FIELD_NUMBER: builtins.int
        execution_count: builtins.int
        @property
        def data(self) -> google.protobuf.struct_pb2.Struct:
            """mimebundle"""
        @property
        def metadata(self) -> google.protobuf.struct_pb2.Struct: ...
        def __init__(
            self,
            *,
            execution_count: builtins.int = ...,
            data: google.protobuf.struct_pb2.Struct | None = ...,
            metadata: google.protobuf.struct_pb2.Struct | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["data", b"data", "metadata", b"metadata"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "execution_count", b"execution_count", "metadata", b"metadata"]) -> None: ...

    class DisplayData(google.protobuf.message.Message):
        """Data displayed as a result of code cell execution."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        DATA_FIELD_NUMBER: builtins.int
        METADATA_FIELD_NUMBER: builtins.int
        TRANSIENT_DISPLAY_ID_FIELD_NUMBER: builtins.int
        @property
        def data(self) -> google.protobuf.struct_pb2.Struct:
            """mimebundle"""
        @property
        def metadata(self) -> google.protobuf.struct_pb2.Struct: ...
        transient_display_id: builtins.str
        """This should not be included in saved notebook."""
        def __init__(
            self,
            *,
            data: google.protobuf.struct_pb2.Struct | None = ...,
            metadata: google.protobuf.struct_pb2.Struct | None = ...,
            transient_display_id: builtins.str | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_transient_display_id", b"_transient_display_id", "data", b"data", "metadata", b"metadata", "transient_display_id", b"transient_display_id"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_transient_display_id", b"_transient_display_id", "data", b"data", "metadata", b"metadata", "transient_display_id", b"transient_display_id"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_transient_display_id", b"_transient_display_id"]) -> typing_extensions.Literal["transient_display_id"] | None: ...

    class Stream(google.protobuf.message.Message):
        """Stream output from a code cell (stdout / stderr)."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        NAME_FIELD_NUMBER: builtins.int
        TEXT_FIELD_NUMBER: builtins.int
        name: builtins.str
        """stdout | stderr"""
        text: builtins.str
        """multiline_string"""
        def __init__(
            self,
            *,
            name: builtins.str = ...,
            text: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["name", b"name", "text", b"text"]) -> None: ...

    class Error(google.protobuf.message.Message):
        """Output of an error that occurred during code cell execution."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        ENAME_FIELD_NUMBER: builtins.int
        EVALUE_FIELD_NUMBER: builtins.int
        TRACEBACK_FIELD_NUMBER: builtins.int
        ename: builtins.str
        evalue: builtins.str
        @property
        def traceback(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
        def __init__(
            self,
            *,
            ename: builtins.str = ...,
            evalue: builtins.str = ...,
            traceback: collections.abc.Iterable[builtins.str] | None = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["ename", b"ename", "evalue", b"evalue", "traceback", b"traceback"]) -> None: ...

    EXECUTE_RESULT_FIELD_NUMBER: builtins.int
    DISPLAY_DATA_FIELD_NUMBER: builtins.int
    STREAM_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    @property
    def execute_result(self) -> global___NotebookOutput.ExecuteResult: ...
    @property
    def display_data(self) -> global___NotebookOutput.DisplayData: ...
    @property
    def stream(self) -> global___NotebookOutput.Stream: ...
    @property
    def error(self) -> global___NotebookOutput.Error: ...
    def __init__(
        self,
        *,
        execute_result: global___NotebookOutput.ExecuteResult | None = ...,
        display_data: global___NotebookOutput.DisplayData | None = ...,
        stream: global___NotebookOutput.Stream | None = ...,
        error: global___NotebookOutput.Error | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["display_data", b"display_data", "error", b"error", "execute_result", b"execute_result", "output_type", b"output_type", "stream", b"stream"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["display_data", b"display_data", "error", b"error", "execute_result", b"execute_result", "output_type", b"output_type", "stream", b"stream"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["output_type", b"output_type"]) -> typing_extensions.Literal["execute_result", "display_data", "stream", "error"] | None: ...

global___NotebookOutput = NotebookOutput

class Object(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OBJECT_ID_FIELD_NUMBER: builtins.int
    FUNCTION_HANDLE_METADATA_FIELD_NUMBER: builtins.int
    MOUNT_HANDLE_METADATA_FIELD_NUMBER: builtins.int
    CLASS_HANDLE_METADATA_FIELD_NUMBER: builtins.int
    SANDBOX_HANDLE_METADATA_FIELD_NUMBER: builtins.int
    VOLUME_METADATA_FIELD_NUMBER: builtins.int
    object_id: builtins.str
    @property
    def function_handle_metadata(self) -> global___FunctionHandleMetadata: ...
    @property
    def mount_handle_metadata(self) -> global___MountHandleMetadata: ...
    @property
    def class_handle_metadata(self) -> global___ClassHandleMetadata: ...
    @property
    def sandbox_handle_metadata(self) -> global___SandboxHandleMetadata: ...
    @property
    def volume_metadata(self) -> global___VolumeMetadata: ...
    def __init__(
        self,
        *,
        object_id: builtins.str = ...,
        function_handle_metadata: global___FunctionHandleMetadata | None = ...,
        mount_handle_metadata: global___MountHandleMetadata | None = ...,
        class_handle_metadata: global___ClassHandleMetadata | None = ...,
        sandbox_handle_metadata: global___SandboxHandleMetadata | None = ...,
        volume_metadata: global___VolumeMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["class_handle_metadata", b"class_handle_metadata", "function_handle_metadata", b"function_handle_metadata", "handle_metadata_oneof", b"handle_metadata_oneof", "mount_handle_metadata", b"mount_handle_metadata", "sandbox_handle_metadata", b"sandbox_handle_metadata", "volume_metadata", b"volume_metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["class_handle_metadata", b"class_handle_metadata", "function_handle_metadata", b"function_handle_metadata", "handle_metadata_oneof", b"handle_metadata_oneof", "mount_handle_metadata", b"mount_handle_metadata", "object_id", b"object_id", "sandbox_handle_metadata", b"sandbox_handle_metadata", "volume_metadata", b"volume_metadata"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["handle_metadata_oneof", b"handle_metadata_oneof"]) -> typing_extensions.Literal["function_handle_metadata", "mount_handle_metadata", "class_handle_metadata", "sandbox_handle_metadata", "volume_metadata"] | None: ...

global___Object = Object

class ObjectDependency(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OBJECT_ID_FIELD_NUMBER: builtins.int
    object_id: builtins.str
    def __init__(
        self,
        *,
        object_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["object_id", b"object_id"]) -> None: ...

global___ObjectDependency = ObjectDependency

class PTYInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _PTYType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _PTYTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[PTYInfo._PTYType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        PTY_TYPE_UNSPECIFIED: PTYInfo._PTYType.ValueType  # 0
        """Nothing"""
        PTY_TYPE_FUNCTION: PTYInfo._PTYType.ValueType  # 1
        """Run function in PTY"""
        PTY_TYPE_SHELL: PTYInfo._PTYType.ValueType  # 2
        """Replace function with shell"""

    class PTYType(_PTYType, metaclass=_PTYTypeEnumTypeWrapper): ...
    PTY_TYPE_UNSPECIFIED: PTYInfo.PTYType.ValueType  # 0
    """Nothing"""
    PTY_TYPE_FUNCTION: PTYInfo.PTYType.ValueType  # 1
    """Run function in PTY"""
    PTY_TYPE_SHELL: PTYInfo.PTYType.ValueType  # 2
    """Replace function with shell"""

    ENABLED_FIELD_NUMBER: builtins.int
    WINSZ_ROWS_FIELD_NUMBER: builtins.int
    WINSZ_COLS_FIELD_NUMBER: builtins.int
    ENV_TERM_FIELD_NUMBER: builtins.int
    ENV_COLORTERM_FIELD_NUMBER: builtins.int
    ENV_TERM_PROGRAM_FIELD_NUMBER: builtins.int
    PTY_TYPE_FIELD_NUMBER: builtins.int
    enabled: builtins.bool
    """Soon deprecated"""
    winsz_rows: builtins.int
    winsz_cols: builtins.int
    env_term: builtins.str
    env_colorterm: builtins.str
    env_term_program: builtins.str
    pty_type: global___PTYInfo.PTYType.ValueType
    def __init__(
        self,
        *,
        enabled: builtins.bool = ...,
        winsz_rows: builtins.int = ...,
        winsz_cols: builtins.int = ...,
        env_term: builtins.str = ...,
        env_colorterm: builtins.str = ...,
        env_term_program: builtins.str = ...,
        pty_type: global___PTYInfo.PTYType.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["enabled", b"enabled", "env_colorterm", b"env_colorterm", "env_term", b"env_term", "env_term_program", b"env_term_program", "pty_type", b"pty_type", "winsz_cols", b"winsz_cols", "winsz_rows", b"winsz_rows"]) -> None: ...

global___PTYInfo = PTYInfo

class PortSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PORT_FIELD_NUMBER: builtins.int
    UNENCRYPTED_FIELD_NUMBER: builtins.int
    TUNNEL_TYPE_FIELD_NUMBER: builtins.int
    port: builtins.int
    unencrypted: builtins.bool
    tunnel_type: global___TunnelType.ValueType
    def __init__(
        self,
        *,
        port: builtins.int = ...,
        unencrypted: builtins.bool = ...,
        tunnel_type: global___TunnelType.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_tunnel_type", b"_tunnel_type", "tunnel_type", b"tunnel_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_tunnel_type", b"_tunnel_type", "port", b"port", "tunnel_type", b"tunnel_type", "unencrypted", b"unencrypted"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_tunnel_type", b"_tunnel_type"]) -> typing_extensions.Literal["tunnel_type"] | None: ...

global___PortSpec = PortSpec

class PortSpecs(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PORTS_FIELD_NUMBER: builtins.int
    @property
    def ports(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___PortSpec]: ...
    def __init__(
        self,
        *,
        ports: collections.abc.Iterable[global___PortSpec] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["ports", b"ports"]) -> None: ...

global___PortSpecs = PortSpecs

class Proxy(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    PROXY_ID_FIELD_NUMBER: builtins.int
    PROXY_IPS_FIELD_NUMBER: builtins.int
    name: builtins.str
    created_at: builtins.float
    environment_name: builtins.str
    proxy_id: builtins.str
    @property
    def proxy_ips(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ProxyIp]: ...
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        created_at: builtins.float = ...,
        environment_name: builtins.str = ...,
        proxy_id: builtins.str = ...,
        proxy_ips: collections.abc.Iterable[global___ProxyIp] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["created_at", b"created_at", "environment_name", b"environment_name", "name", b"name", "proxy_id", b"proxy_id", "proxy_ips", b"proxy_ips"]) -> None: ...

global___Proxy = Proxy

class ProxyAddIpRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_ID_FIELD_NUMBER: builtins.int
    proxy_id: builtins.str
    def __init__(
        self,
        *,
        proxy_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["proxy_id", b"proxy_id"]) -> None: ...

global___ProxyAddIpRequest = ProxyAddIpRequest

class ProxyAddIpResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_IP_FIELD_NUMBER: builtins.int
    @property
    def proxy_ip(self) -> global___ProxyIp: ...
    def __init__(
        self,
        *,
        proxy_ip: global___ProxyIp | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["proxy_ip", b"proxy_ip"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["proxy_ip", b"proxy_ip"]) -> None: ...

global___ProxyAddIpResponse = ProxyAddIpResponse

class ProxyCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    environment_name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "name", b"name"]) -> None: ...

global___ProxyCreateRequest = ProxyCreateRequest

class ProxyCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_FIELD_NUMBER: builtins.int
    @property
    def proxy(self) -> global___Proxy: ...
    def __init__(
        self,
        *,
        proxy: global___Proxy | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["proxy", b"proxy"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["proxy", b"proxy"]) -> None: ...

global___ProxyCreateResponse = ProxyCreateResponse

class ProxyDeleteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_ID_FIELD_NUMBER: builtins.int
    proxy_id: builtins.str
    def __init__(
        self,
        *,
        proxy_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["proxy_id", b"proxy_id"]) -> None: ...

global___ProxyDeleteRequest = ProxyDeleteRequest

class ProxyGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEPLOYMENT_NAME_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    deployment_name: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    """must be UNSPECIFIED"""
    def __init__(
        self,
        *,
        deployment_name: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["deployment_name", b"deployment_name", "environment_name", b"environment_name", "namespace", b"namespace", "object_creation_type", b"object_creation_type"]) -> None: ...

global___ProxyGetOrCreateRequest = ProxyGetOrCreateRequest

class ProxyGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_ID_FIELD_NUMBER: builtins.int
    proxy_id: builtins.str
    def __init__(
        self,
        *,
        proxy_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["proxy_id", b"proxy_id"]) -> None: ...

global___ProxyGetOrCreateResponse = ProxyGetOrCreateResponse

class ProxyGetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    environment_name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "name", b"name"]) -> None: ...

global___ProxyGetRequest = ProxyGetRequest

class ProxyGetResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_FIELD_NUMBER: builtins.int
    @property
    def proxy(self) -> global___Proxy: ...
    def __init__(
        self,
        *,
        proxy: global___Proxy | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["proxy", b"proxy"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["proxy", b"proxy"]) -> None: ...

global___ProxyGetResponse = ProxyGetResponse

class ProxyInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ELASTIC_IP_FIELD_NUMBER: builtins.int
    PROXY_KEY_FIELD_NUMBER: builtins.int
    REMOTE_ADDR_FIELD_NUMBER: builtins.int
    REMOTE_PORT_FIELD_NUMBER: builtins.int
    PROXY_TYPE_FIELD_NUMBER: builtins.int
    elastic_ip: builtins.str
    proxy_key: builtins.str
    remote_addr: builtins.str
    remote_port: builtins.int
    proxy_type: global___ProxyType.ValueType
    def __init__(
        self,
        *,
        elastic_ip: builtins.str = ...,
        proxy_key: builtins.str = ...,
        remote_addr: builtins.str = ...,
        remote_port: builtins.int = ...,
        proxy_type: global___ProxyType.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["elastic_ip", b"elastic_ip", "proxy_key", b"proxy_key", "proxy_type", b"proxy_type", "remote_addr", b"remote_addr", "remote_port", b"remote_port"]) -> None: ...

global___ProxyInfo = ProxyInfo

class ProxyIp(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_IP_FIELD_NUMBER: builtins.int
    STATUS_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    proxy_ip: builtins.str
    status: global___ProxyIpStatus.ValueType
    created_at: builtins.float
    environment_name: builtins.str
    def __init__(
        self,
        *,
        proxy_ip: builtins.str = ...,
        status: global___ProxyIpStatus.ValueType = ...,
        created_at: builtins.float = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["created_at", b"created_at", "environment_name", b"environment_name", "proxy_ip", b"proxy_ip", "status", b"status"]) -> None: ...

global___ProxyIp = ProxyIp

class ProxyListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXIES_FIELD_NUMBER: builtins.int
    @property
    def proxies(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Proxy]: ...
    def __init__(
        self,
        *,
        proxies: collections.abc.Iterable[global___Proxy] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["proxies", b"proxies"]) -> None: ...

global___ProxyListResponse = ProxyListResponse

class ProxyRemoveIpRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PROXY_IP_FIELD_NUMBER: builtins.int
    proxy_ip: builtins.str
    def __init__(
        self,
        *,
        proxy_ip: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["proxy_ip", b"proxy_ip"]) -> None: ...

global___ProxyRemoveIpRequest = ProxyRemoveIpRequest

class QueueClearRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUEUE_ID_FIELD_NUMBER: builtins.int
    PARTITION_KEY_FIELD_NUMBER: builtins.int
    ALL_PARTITIONS_FIELD_NUMBER: builtins.int
    queue_id: builtins.str
    partition_key: builtins.bytes
    all_partitions: builtins.bool
    def __init__(
        self,
        *,
        queue_id: builtins.str = ...,
        partition_key: builtins.bytes = ...,
        all_partitions: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["all_partitions", b"all_partitions", "partition_key", b"partition_key", "queue_id", b"queue_id"]) -> None: ...

global___QueueClearRequest = QueueClearRequest

class QueueDeleteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUEUE_ID_FIELD_NUMBER: builtins.int
    queue_id: builtins.str
    def __init__(
        self,
        *,
        queue_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["queue_id", b"queue_id"]) -> None: ...

global___QueueDeleteRequest = QueueDeleteRequest

class QueueGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEPLOYMENT_NAME_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    deployment_name: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    def __init__(
        self,
        *,
        deployment_name: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["deployment_name", b"deployment_name", "environment_name", b"environment_name", "namespace", b"namespace", "object_creation_type", b"object_creation_type"]) -> None: ...

global___QueueGetOrCreateRequest = QueueGetOrCreateRequest

class QueueGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUEUE_ID_FIELD_NUMBER: builtins.int
    queue_id: builtins.str
    def __init__(
        self,
        *,
        queue_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["queue_id", b"queue_id"]) -> None: ...

global___QueueGetOrCreateResponse = QueueGetOrCreateResponse

class QueueGetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUEUE_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    N_VALUES_FIELD_NUMBER: builtins.int
    PARTITION_KEY_FIELD_NUMBER: builtins.int
    queue_id: builtins.str
    timeout: builtins.float
    n_values: builtins.int
    partition_key: builtins.bytes
    def __init__(
        self,
        *,
        queue_id: builtins.str = ...,
        timeout: builtins.float = ...,
        n_values: builtins.int = ...,
        partition_key: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["n_values", b"n_values", "partition_key", b"partition_key", "queue_id", b"queue_id", "timeout", b"timeout"]) -> None: ...

global___QueueGetRequest = QueueGetRequest

class QueueGetResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUES_FIELD_NUMBER: builtins.int
    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.bytes]: ...
    def __init__(
        self,
        *,
        values: collections.abc.Iterable[builtins.bytes] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["values", b"values"]) -> None: ...

global___QueueGetResponse = QueueGetResponse

class QueueHeartbeatRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUEUE_ID_FIELD_NUMBER: builtins.int
    queue_id: builtins.str
    def __init__(
        self,
        *,
        queue_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["queue_id", b"queue_id"]) -> None: ...

global___QueueHeartbeatRequest = QueueHeartbeatRequest

class QueueItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUE_FIELD_NUMBER: builtins.int
    ENTRY_ID_FIELD_NUMBER: builtins.int
    value: builtins.bytes
    entry_id: builtins.str
    def __init__(
        self,
        *,
        value: builtins.bytes = ...,
        entry_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["entry_id", b"entry_id", "value", b"value"]) -> None: ...

global___QueueItem = QueueItem

class QueueLenRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUEUE_ID_FIELD_NUMBER: builtins.int
    PARTITION_KEY_FIELD_NUMBER: builtins.int
    TOTAL_FIELD_NUMBER: builtins.int
    queue_id: builtins.str
    partition_key: builtins.bytes
    total: builtins.bool
    def __init__(
        self,
        *,
        queue_id: builtins.str = ...,
        partition_key: builtins.bytes = ...,
        total: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["partition_key", b"partition_key", "queue_id", b"queue_id", "total", b"total"]) -> None: ...

global___QueueLenRequest = QueueLenRequest

class QueueLenResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LEN_FIELD_NUMBER: builtins.int
    len: builtins.int
    def __init__(
        self,
        *,
        len: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["len", b"len"]) -> None: ...

global___QueueLenResponse = QueueLenResponse

class QueueListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    TOTAL_SIZE_LIMIT_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    total_size_limit: builtins.int
    """Allow client to report a bounded total size to reduce the number of partitions that need to be checked"""
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
        total_size_limit: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "total_size_limit", b"total_size_limit"]) -> None: ...

global___QueueListRequest = QueueListRequest

class QueueListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class QueueInfo(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        NAME_FIELD_NUMBER: builtins.int
        CREATED_AT_FIELD_NUMBER: builtins.int
        NUM_PARTITIONS_FIELD_NUMBER: builtins.int
        TOTAL_SIZE_FIELD_NUMBER: builtins.int
        name: builtins.str
        created_at: builtins.float
        num_partitions: builtins.int
        total_size: builtins.int
        def __init__(
            self,
            *,
            name: builtins.str = ...,
            created_at: builtins.float = ...,
            num_partitions: builtins.int = ...,
            total_size: builtins.int = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["created_at", b"created_at", "name", b"name", "num_partitions", b"num_partitions", "total_size", b"total_size"]) -> None: ...

    QUEUES_FIELD_NUMBER: builtins.int
    @property
    def queues(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___QueueListResponse.QueueInfo]: ...
    def __init__(
        self,
        *,
        queues: collections.abc.Iterable[global___QueueListResponse.QueueInfo] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["queues", b"queues"]) -> None: ...

global___QueueListResponse = QueueListResponse

class QueueNextItemsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUEUE_ID_FIELD_NUMBER: builtins.int
    PARTITION_KEY_FIELD_NUMBER: builtins.int
    LAST_ENTRY_ID_FIELD_NUMBER: builtins.int
    ITEM_POLL_TIMEOUT_FIELD_NUMBER: builtins.int
    queue_id: builtins.str
    partition_key: builtins.bytes
    last_entry_id: builtins.str
    item_poll_timeout: builtins.float
    """seconds"""
    def __init__(
        self,
        *,
        queue_id: builtins.str = ...,
        partition_key: builtins.bytes = ...,
        last_entry_id: builtins.str = ...,
        item_poll_timeout: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["item_poll_timeout", b"item_poll_timeout", "last_entry_id", b"last_entry_id", "partition_key", b"partition_key", "queue_id", b"queue_id"]) -> None: ...

global___QueueNextItemsRequest = QueueNextItemsRequest

class QueueNextItemsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEMS_FIELD_NUMBER: builtins.int
    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___QueueItem]: ...
    def __init__(
        self,
        *,
        items: collections.abc.Iterable[global___QueueItem] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["items", b"items"]) -> None: ...

global___QueueNextItemsResponse = QueueNextItemsResponse

class QueuePutRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QUEUE_ID_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    PARTITION_KEY_FIELD_NUMBER: builtins.int
    PARTITION_TTL_SECONDS_FIELD_NUMBER: builtins.int
    queue_id: builtins.str
    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.bytes]: ...
    partition_key: builtins.bytes
    partition_ttl_seconds: builtins.int
    def __init__(
        self,
        *,
        queue_id: builtins.str = ...,
        values: collections.abc.Iterable[builtins.bytes] | None = ...,
        partition_key: builtins.bytes = ...,
        partition_ttl_seconds: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["partition_key", b"partition_key", "partition_ttl_seconds", b"partition_ttl_seconds", "queue_id", b"queue_id", "values", b"values"]) -> None: ...

global___QueuePutRequest = QueuePutRequest

class RateLimit(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LIMIT_FIELD_NUMBER: builtins.int
    INTERVAL_FIELD_NUMBER: builtins.int
    limit: builtins.int
    interval: global___RateLimitInterval.ValueType
    def __init__(
        self,
        *,
        limit: builtins.int = ...,
        interval: global___RateLimitInterval.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["interval", b"interval", "limit", b"limit"]) -> None: ...

global___RateLimit = RateLimit

class Resources(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MEMORY_MB_FIELD_NUMBER: builtins.int
    MILLI_CPU_FIELD_NUMBER: builtins.int
    GPU_CONFIG_FIELD_NUMBER: builtins.int
    MEMORY_MB_MAX_FIELD_NUMBER: builtins.int
    EPHEMERAL_DISK_MB_FIELD_NUMBER: builtins.int
    MILLI_CPU_MAX_FIELD_NUMBER: builtins.int
    RDMA_FIELD_NUMBER: builtins.int
    memory_mb: builtins.int
    """MiB"""
    milli_cpu: builtins.int
    """milli CPU cores"""
    @property
    def gpu_config(self) -> global___GPUConfig: ...
    memory_mb_max: builtins.int
    """MiB"""
    ephemeral_disk_mb: builtins.int
    """MiB"""
    milli_cpu_max: builtins.int
    """milli CPU cores"""
    rdma: builtins.bool
    """Whether to use RDMA interfaces"""
    def __init__(
        self,
        *,
        memory_mb: builtins.int = ...,
        milli_cpu: builtins.int = ...,
        gpu_config: global___GPUConfig | None = ...,
        memory_mb_max: builtins.int = ...,
        ephemeral_disk_mb: builtins.int = ...,
        milli_cpu_max: builtins.int = ...,
        rdma: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["gpu_config", b"gpu_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["ephemeral_disk_mb", b"ephemeral_disk_mb", "gpu_config", b"gpu_config", "memory_mb", b"memory_mb", "memory_mb_max", b"memory_mb_max", "milli_cpu", b"milli_cpu", "milli_cpu_max", b"milli_cpu_max", "rdma", b"rdma"]) -> None: ...

global___Resources = Resources

class RuntimeInputMessage(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    MESSAGE_INDEX_FIELD_NUMBER: builtins.int
    EOF_FIELD_NUMBER: builtins.int
    message: builtins.bytes
    message_index: builtins.int
    eof: builtins.bool
    def __init__(
        self,
        *,
        message: builtins.bytes = ...,
        message_index: builtins.int = ...,
        eof: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["eof", b"eof", "message", b"message", "message_index", b"message_index"]) -> None: ...

global___RuntimeInputMessage = RuntimeInputMessage

class RuntimeOutputBatch(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEMS_FIELD_NUMBER: builtins.int
    BATCH_INDEX_FIELD_NUMBER: builtins.int
    EXIT_CODE_FIELD_NUMBER: builtins.int
    STDOUT_FIELD_NUMBER: builtins.int
    STDERR_FIELD_NUMBER: builtins.int
    INFO_FIELD_NUMBER: builtins.int
    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___RuntimeOutputMessage]: ...
    batch_index: builtins.int
    exit_code: builtins.int
    """if an exit code is given, this is the final message that will be sent."""
    @property
    def stdout(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___RuntimeOutputMessage]: ...
    @property
    def stderr(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___RuntimeOutputMessage]: ...
    @property
    def info(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___RuntimeOutputMessage]: ...
    def __init__(
        self,
        *,
        items: collections.abc.Iterable[global___RuntimeOutputMessage] | None = ...,
        batch_index: builtins.int = ...,
        exit_code: builtins.int | None = ...,
        stdout: collections.abc.Iterable[global___RuntimeOutputMessage] | None = ...,
        stderr: collections.abc.Iterable[global___RuntimeOutputMessage] | None = ...,
        info: collections.abc.Iterable[global___RuntimeOutputMessage] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_exit_code", b"_exit_code", "exit_code", b"exit_code"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_exit_code", b"_exit_code", "batch_index", b"batch_index", "exit_code", b"exit_code", "info", b"info", "items", b"items", "stderr", b"stderr", "stdout", b"stdout"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_exit_code", b"_exit_code"]) -> typing_extensions.Literal["exit_code"] | None: ...

global___RuntimeOutputBatch = RuntimeOutputBatch

class RuntimeOutputMessage(google.protobuf.message.Message):
    """Used for `modal container exec`, `modal shell`, and Sandboxes"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    MESSAGE_BYTES_FIELD_NUMBER: builtins.int
    file_descriptor: global___FileDescriptor.ValueType
    """only stdout / stderr is used"""
    message: builtins.str
    message_bytes: builtins.bytes
    def __init__(
        self,
        *,
        file_descriptor: global___FileDescriptor.ValueType = ...,
        message: builtins.str = ...,
        message_bytes: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["file_descriptor", b"file_descriptor", "message", b"message", "message_bytes", b"message_bytes"]) -> None: ...

global___RuntimeOutputMessage = RuntimeOutputMessage

class S3Mount(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BUCKET_NAME_FIELD_NUMBER: builtins.int
    MOUNT_PATH_FIELD_NUMBER: builtins.int
    CREDENTIALS_SECRET_ID_FIELD_NUMBER: builtins.int
    READ_ONLY_FIELD_NUMBER: builtins.int
    bucket_name: builtins.str
    mount_path: builtins.str
    credentials_secret_id: builtins.str
    read_only: builtins.bool
    def __init__(
        self,
        *,
        bucket_name: builtins.str = ...,
        mount_path: builtins.str = ...,
        credentials_secret_id: builtins.str = ...,
        read_only: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["bucket_name", b"bucket_name", "credentials_secret_id", b"credentials_secret_id", "mount_path", b"mount_path", "read_only", b"read_only"]) -> None: ...

global___S3Mount = S3Mount

class Sandbox(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENTRYPOINT_ARGS_FIELD_NUMBER: builtins.int
    MOUNT_IDS_FIELD_NUMBER: builtins.int
    IMAGE_ID_FIELD_NUMBER: builtins.int
    SECRET_IDS_FIELD_NUMBER: builtins.int
    RESOURCES_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_FIELD_NUMBER: builtins.int
    TIMEOUT_SECS_FIELD_NUMBER: builtins.int
    WORKDIR_FIELD_NUMBER: builtins.int
    NFS_MOUNTS_FIELD_NUMBER: builtins.int
    RUNTIME_DEBUG_FIELD_NUMBER: builtins.int
    BLOCK_NETWORK_FIELD_NUMBER: builtins.int
    S3_MOUNTS_FIELD_NUMBER: builtins.int
    CLOUD_BUCKET_MOUNTS_FIELD_NUMBER: builtins.int
    VOLUME_MOUNTS_FIELD_NUMBER: builtins.int
    PTY_INFO_FIELD_NUMBER: builtins.int
    SCHEDULER_PLACEMENT_FIELD_NUMBER: builtins.int
    WORKER_ID_FIELD_NUMBER: builtins.int
    OPEN_PORTS_FIELD_NUMBER: builtins.int
    I6PN_ENABLED_FIELD_NUMBER: builtins.int
    NETWORK_ACCESS_FIELD_NUMBER: builtins.int
    PROXY_ID_FIELD_NUMBER: builtins.int
    ENABLE_SNAPSHOT_FIELD_NUMBER: builtins.int
    SNAPSHOT_VERSION_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_STR_FIELD_NUMBER: builtins.int
    RUNSC_RUNTIME_VERSION_FIELD_NUMBER: builtins.int
    RUNTIME_FIELD_NUMBER: builtins.int
    VERBOSE_FIELD_NUMBER: builtins.int
    @property
    def entrypoint_args(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def mount_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    image_id: builtins.str
    @property
    def secret_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    @property
    def resources(self) -> global___Resources: ...
    cloud_provider: global___CloudProvider.ValueType
    """Deprecated at some point"""
    timeout_secs: builtins.int
    workdir: builtins.str
    @property
    def nfs_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SharedVolumeMount]: ...
    runtime_debug: builtins.bool
    """For internal debugging use only."""
    block_network: builtins.bool
    @property
    def s3_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___S3Mount]: ...
    @property
    def cloud_bucket_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CloudBucketMount]: ...
    @property
    def volume_mounts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___VolumeMount]: ...
    @property
    def pty_info(self) -> global___PTYInfo: ...
    @property
    def scheduler_placement(self) -> global___SchedulerPlacement: ...
    worker_id: builtins.str
    """for internal debugging use only"""
    @property
    def open_ports(self) -> global___PortSpecs: ...
    i6pn_enabled: builtins.bool
    @property
    def network_access(self) -> global___NetworkAccess:
        """Network access configuration beyond simple allow/block."""
    proxy_id: builtins.str
    enable_snapshot: builtins.bool
    """Enable snapshotting the sandbox (both memory and filesystem).
    This doesn't need to be enabled to save the filesystem as an image (i.e. a filesystem-only snapshot).
    """
    snapshot_version: builtins.int
    """Used to pin gVisor version for memory-snapshottable sandboxes.
    This field is set by the server, not the client.
    """
    cloud_provider_str: builtins.str
    """Supersedes cloud_provider"""
    runsc_runtime_version: builtins.str
    """Specifies container runtime behavior for sandboxes which are restored from a snapshot.
    Set by the backend at snapshot creation time.
    """
    runtime: builtins.str
    """If set, overrides the runtime used by the function, either "runc" or "gvisor"."""
    verbose: builtins.bool
    """If set, the sandbox will be created with verbose logging enabled."""
    def __init__(
        self,
        *,
        entrypoint_args: collections.abc.Iterable[builtins.str] | None = ...,
        mount_ids: collections.abc.Iterable[builtins.str] | None = ...,
        image_id: builtins.str = ...,
        secret_ids: collections.abc.Iterable[builtins.str] | None = ...,
        resources: global___Resources | None = ...,
        cloud_provider: global___CloudProvider.ValueType = ...,
        timeout_secs: builtins.int = ...,
        workdir: builtins.str | None = ...,
        nfs_mounts: collections.abc.Iterable[global___SharedVolumeMount] | None = ...,
        runtime_debug: builtins.bool = ...,
        block_network: builtins.bool = ...,
        s3_mounts: collections.abc.Iterable[global___S3Mount] | None = ...,
        cloud_bucket_mounts: collections.abc.Iterable[global___CloudBucketMount] | None = ...,
        volume_mounts: collections.abc.Iterable[global___VolumeMount] | None = ...,
        pty_info: global___PTYInfo | None = ...,
        scheduler_placement: global___SchedulerPlacement | None = ...,
        worker_id: builtins.str = ...,
        open_ports: global___PortSpecs | None = ...,
        i6pn_enabled: builtins.bool = ...,
        network_access: global___NetworkAccess | None = ...,
        proxy_id: builtins.str | None = ...,
        enable_snapshot: builtins.bool = ...,
        snapshot_version: builtins.int | None = ...,
        cloud_provider_str: builtins.str = ...,
        runsc_runtime_version: builtins.str | None = ...,
        runtime: builtins.str | None = ...,
        verbose: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_proxy_id", b"_proxy_id", "_runsc_runtime_version", b"_runsc_runtime_version", "_runtime", b"_runtime", "_scheduler_placement", b"_scheduler_placement", "_snapshot_version", b"_snapshot_version", "_workdir", b"_workdir", "network_access", b"network_access", "open_ports", b"open_ports", "open_ports_oneof", b"open_ports_oneof", "proxy_id", b"proxy_id", "pty_info", b"pty_info", "resources", b"resources", "runsc_runtime_version", b"runsc_runtime_version", "runtime", b"runtime", "scheduler_placement", b"scheduler_placement", "snapshot_version", b"snapshot_version", "workdir", b"workdir"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_proxy_id", b"_proxy_id", "_runsc_runtime_version", b"_runsc_runtime_version", "_runtime", b"_runtime", "_scheduler_placement", b"_scheduler_placement", "_snapshot_version", b"_snapshot_version", "_workdir", b"_workdir", "block_network", b"block_network", "cloud_bucket_mounts", b"cloud_bucket_mounts", "cloud_provider", b"cloud_provider", "cloud_provider_str", b"cloud_provider_str", "enable_snapshot", b"enable_snapshot", "entrypoint_args", b"entrypoint_args", "i6pn_enabled", b"i6pn_enabled", "image_id", b"image_id", "mount_ids", b"mount_ids", "network_access", b"network_access", "nfs_mounts", b"nfs_mounts", "open_ports", b"open_ports", "open_ports_oneof", b"open_ports_oneof", "proxy_id", b"proxy_id", "pty_info", b"pty_info", "resources", b"resources", "runsc_runtime_version", b"runsc_runtime_version", "runtime", b"runtime", "runtime_debug", b"runtime_debug", "s3_mounts", b"s3_mounts", "scheduler_placement", b"scheduler_placement", "secret_ids", b"secret_ids", "snapshot_version", b"snapshot_version", "timeout_secs", b"timeout_secs", "verbose", b"verbose", "volume_mounts", b"volume_mounts", "workdir", b"workdir", "worker_id", b"worker_id"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_proxy_id", b"_proxy_id"]) -> typing_extensions.Literal["proxy_id"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_runsc_runtime_version", b"_runsc_runtime_version"]) -> typing_extensions.Literal["runsc_runtime_version"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_runtime", b"_runtime"]) -> typing_extensions.Literal["runtime"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_scheduler_placement", b"_scheduler_placement"]) -> typing_extensions.Literal["scheduler_placement"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_snapshot_version", b"_snapshot_version"]) -> typing_extensions.Literal["snapshot_version"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_workdir", b"_workdir"]) -> typing_extensions.Literal["workdir"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["open_ports_oneof", b"open_ports_oneof"]) -> typing_extensions.Literal["open_ports"] | None: ...

global___Sandbox = Sandbox

class SandboxCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    DEFINITION_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    @property
    def definition(self) -> global___Sandbox: ...
    environment_name: builtins.str
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        definition: global___Sandbox | None = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["definition", b"definition"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "definition", b"definition", "environment_name", b"environment_name"]) -> None: ...

global___SandboxCreateRequest = SandboxCreateRequest

class SandboxCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandbox_id", b"sandbox_id"]) -> None: ...

global___SandboxCreateResponse = SandboxCreateResponse

class SandboxGetLogsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    LAST_ENTRY_ID_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    file_descriptor: global___FileDescriptor.ValueType
    timeout: builtins.float
    last_entry_id: builtins.str
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
        file_descriptor: global___FileDescriptor.ValueType = ...,
        timeout: builtins.float = ...,
        last_entry_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["file_descriptor", b"file_descriptor", "last_entry_id", b"last_entry_id", "sandbox_id", b"sandbox_id", "timeout", b"timeout"]) -> None: ...

global___SandboxGetLogsRequest = SandboxGetLogsRequest

class SandboxGetResourceUsageRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandbox_id", b"sandbox_id"]) -> None: ...

global___SandboxGetResourceUsageRequest = SandboxGetResourceUsageRequest

class SandboxGetResourceUsageResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CPU_CORE_NANOSECS_FIELD_NUMBER: builtins.int
    MEM_GIB_NANOSECS_FIELD_NUMBER: builtins.int
    GPU_NANOSECS_FIELD_NUMBER: builtins.int
    GPU_TYPE_FIELD_NUMBER: builtins.int
    cpu_core_nanosecs: builtins.int
    mem_gib_nanosecs: builtins.int
    gpu_nanosecs: builtins.int
    gpu_type: builtins.str
    def __init__(
        self,
        *,
        cpu_core_nanosecs: builtins.int = ...,
        mem_gib_nanosecs: builtins.int = ...,
        gpu_nanosecs: builtins.int = ...,
        gpu_type: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_gpu_type", b"_gpu_type", "gpu_type", b"gpu_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_gpu_type", b"_gpu_type", "cpu_core_nanosecs", b"cpu_core_nanosecs", "gpu_nanosecs", b"gpu_nanosecs", "gpu_type", b"gpu_type", "mem_gib_nanosecs", b"mem_gib_nanosecs"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_gpu_type", b"_gpu_type"]) -> typing_extensions.Literal["gpu_type"] | None: ...

global___SandboxGetResourceUsageResponse = SandboxGetResourceUsageResponse

class SandboxGetTaskIdRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    WAIT_UNTIL_READY_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    timeout: builtins.float
    """Legacy clients do not provide a timeout. New clients must always provide a timeout."""
    wait_until_ready: builtins.bool
    """If true, waits until the container's postStart hook has been run before returning. Useful for detecting init failures."""
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
        timeout: builtins.float | None = ...,
        wait_until_ready: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_timeout", b"_timeout", "timeout", b"timeout"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_timeout", b"_timeout", "sandbox_id", b"sandbox_id", "timeout", b"timeout", "wait_until_ready", b"wait_until_ready"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_timeout", b"_timeout"]) -> typing_extensions.Literal["timeout"] | None: ...

global___SandboxGetTaskIdRequest = SandboxGetTaskIdRequest

class SandboxGetTaskIdResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TASK_ID_FIELD_NUMBER: builtins.int
    TASK_RESULT_FIELD_NUMBER: builtins.int
    task_id: builtins.str
    """This is None if the sandbox was terminated before a task could be scheduled."""
    @property
    def task_result(self) -> global___GenericResult:
        """If the task has already exited, this is the result."""
    def __init__(
        self,
        *,
        task_id: builtins.str | None = ...,
        task_result: global___GenericResult | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_task_id", b"_task_id", "_task_result", b"_task_result", "task_id", b"task_id", "task_result", b"task_result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_task_id", b"_task_id", "_task_result", b"_task_result", "task_id", b"task_id", "task_result", b"task_result"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_task_id", b"_task_id"]) -> typing_extensions.Literal["task_id"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_task_result", b"_task_result"]) -> typing_extensions.Literal["task_result"] | None: ...

global___SandboxGetTaskIdResponse = SandboxGetTaskIdResponse

class SandboxGetTunnelsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    timeout: builtins.float
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
        timeout: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandbox_id", b"sandbox_id", "timeout", b"timeout"]) -> None: ...

global___SandboxGetTunnelsRequest = SandboxGetTunnelsRequest

class SandboxGetTunnelsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    TUNNELS_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> global___GenericResult: ...
    @property
    def tunnels(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TunnelData]: ...
    def __init__(
        self,
        *,
        result: global___GenericResult | None = ...,
        tunnels: collections.abc.Iterable[global___TunnelData] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["result", b"result", "tunnels", b"tunnels"]) -> None: ...

global___SandboxGetTunnelsResponse = SandboxGetTunnelsResponse

class SandboxHandleMetadata(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> global___GenericResult: ...
    def __init__(
        self,
        *,
        result: global___GenericResult | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["result", b"result"]) -> None: ...

global___SandboxHandleMetadata = SandboxHandleMetadata

class SandboxInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    TASK_INFO_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    id: builtins.str
    created_at: builtins.float
    @property
    def task_info(self) -> global___TaskInfo: ...
    app_id: builtins.str
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        created_at: builtins.float = ...,
        task_info: global___TaskInfo | None = ...,
        app_id: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["task_info", b"task_info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "created_at", b"created_at", "id", b"id", "task_info", b"task_info"]) -> None: ...

global___SandboxInfo = SandboxInfo

class SandboxListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    APP_ID_FIELD_NUMBER: builtins.int
    BEFORE_TIMESTAMP_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    INCLUDE_FINISHED_FIELD_NUMBER: builtins.int
    TAGS_FIELD_NUMBER: builtins.int
    app_id: builtins.str
    before_timestamp: builtins.float
    environment_name: builtins.str
    include_finished: builtins.bool
    @property
    def tags(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SandboxTag]: ...
    def __init__(
        self,
        *,
        app_id: builtins.str = ...,
        before_timestamp: builtins.float = ...,
        environment_name: builtins.str = ...,
        include_finished: builtins.bool = ...,
        tags: collections.abc.Iterable[global___SandboxTag] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "before_timestamp", b"before_timestamp", "environment_name", b"environment_name", "include_finished", b"include_finished", "tags", b"tags"]) -> None: ...

global___SandboxListRequest = SandboxListRequest

class SandboxListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOXES_FIELD_NUMBER: builtins.int
    @property
    def sandboxes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SandboxInfo]: ...
    def __init__(
        self,
        *,
        sandboxes: collections.abc.Iterable[global___SandboxInfo] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandboxes", b"sandboxes"]) -> None: ...

global___SandboxListResponse = SandboxListResponse

class SandboxRestoreRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SNAPSHOT_ID_FIELD_NUMBER: builtins.int
    snapshot_id: builtins.str
    def __init__(
        self,
        *,
        snapshot_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["snapshot_id", b"snapshot_id"]) -> None: ...

global___SandboxRestoreRequest = SandboxRestoreRequest

class SandboxRestoreResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandbox_id", b"sandbox_id"]) -> None: ...

global___SandboxRestoreResponse = SandboxRestoreResponse

class SandboxSnapshotFsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    timeout: builtins.float
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
        timeout: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandbox_id", b"sandbox_id", "timeout", b"timeout"]) -> None: ...

global___SandboxSnapshotFsRequest = SandboxSnapshotFsRequest

class SandboxSnapshotFsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IMAGE_ID_FIELD_NUMBER: builtins.int
    RESULT_FIELD_NUMBER: builtins.int
    IMAGE_METADATA_FIELD_NUMBER: builtins.int
    image_id: builtins.str
    @property
    def result(self) -> global___GenericResult: ...
    @property
    def image_metadata(self) -> global___ImageMetadata:
        """Metadata may be empty since we may skip it for performance reasons."""
    def __init__(
        self,
        *,
        image_id: builtins.str = ...,
        result: global___GenericResult | None = ...,
        image_metadata: global___ImageMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["image_metadata", b"image_metadata", "result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["image_id", b"image_id", "image_metadata", b"image_metadata", "result", b"result"]) -> None: ...

global___SandboxSnapshotFsResponse = SandboxSnapshotFsResponse

class SandboxSnapshotGetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SNAPSHOT_ID_FIELD_NUMBER: builtins.int
    snapshot_id: builtins.str
    def __init__(
        self,
        *,
        snapshot_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["snapshot_id", b"snapshot_id"]) -> None: ...

global___SandboxSnapshotGetRequest = SandboxSnapshotGetRequest

class SandboxSnapshotGetResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SNAPSHOT_ID_FIELD_NUMBER: builtins.int
    snapshot_id: builtins.str
    def __init__(
        self,
        *,
        snapshot_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["snapshot_id", b"snapshot_id"]) -> None: ...

global___SandboxSnapshotGetResponse = SandboxSnapshotGetResponse

class SandboxSnapshotRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandbox_id", b"sandbox_id"]) -> None: ...

global___SandboxSnapshotRequest = SandboxSnapshotRequest

class SandboxSnapshotResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SNAPSHOT_ID_FIELD_NUMBER: builtins.int
    snapshot_id: builtins.str
    def __init__(
        self,
        *,
        snapshot_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["snapshot_id", b"snapshot_id"]) -> None: ...

global___SandboxSnapshotResponse = SandboxSnapshotResponse

class SandboxSnapshotWaitRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SNAPSHOT_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    snapshot_id: builtins.str
    timeout: builtins.float
    def __init__(
        self,
        *,
        snapshot_id: builtins.str = ...,
        timeout: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["snapshot_id", b"snapshot_id", "timeout", b"timeout"]) -> None: ...

global___SandboxSnapshotWaitRequest = SandboxSnapshotWaitRequest

class SandboxSnapshotWaitResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> global___GenericResult: ...
    def __init__(
        self,
        *,
        result: global___GenericResult | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["result", b"result"]) -> None: ...

global___SandboxSnapshotWaitResponse = SandboxSnapshotWaitResponse

class SandboxStdinWriteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    INDEX_FIELD_NUMBER: builtins.int
    EOF_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    input: builtins.bytes
    index: builtins.int
    eof: builtins.bool
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
        input: builtins.bytes = ...,
        index: builtins.int = ...,
        eof: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["eof", b"eof", "index", b"index", "input", b"input", "sandbox_id", b"sandbox_id"]) -> None: ...

global___SandboxStdinWriteRequest = SandboxStdinWriteRequest

class SandboxStdinWriteResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___SandboxStdinWriteResponse = SandboxStdinWriteResponse

class SandboxTag(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TAG_NAME_FIELD_NUMBER: builtins.int
    TAG_VALUE_FIELD_NUMBER: builtins.int
    tag_name: builtins.str
    tag_value: builtins.str
    def __init__(
        self,
        *,
        tag_name: builtins.str = ...,
        tag_value: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["tag_name", b"tag_name", "tag_value", b"tag_value"]) -> None: ...

global___SandboxTag = SandboxTag

class SandboxTagsSetRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    SANDBOX_ID_FIELD_NUMBER: builtins.int
    TAGS_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    sandbox_id: builtins.str
    @property
    def tags(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SandboxTag]: ...
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
        sandbox_id: builtins.str = ...,
        tags: collections.abc.Iterable[global___SandboxTag] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "sandbox_id", b"sandbox_id", "tags", b"tags"]) -> None: ...

global___SandboxTagsSetRequest = SandboxTagsSetRequest

class SandboxTerminateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandbox_id", b"sandbox_id"]) -> None: ...

global___SandboxTerminateRequest = SandboxTerminateRequest

class SandboxTerminateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXISTING_RESULT_FIELD_NUMBER: builtins.int
    @property
    def existing_result(self) -> global___GenericResult: ...
    def __init__(
        self,
        *,
        existing_result: global___GenericResult | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["existing_result", b"existing_result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["existing_result", b"existing_result"]) -> None: ...

global___SandboxTerminateResponse = SandboxTerminateResponse

class SandboxWaitRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SANDBOX_ID_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    sandbox_id: builtins.str
    timeout: builtins.float
    def __init__(
        self,
        *,
        sandbox_id: builtins.str = ...,
        timeout: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["sandbox_id", b"sandbox_id", "timeout", b"timeout"]) -> None: ...

global___SandboxWaitRequest = SandboxWaitRequest

class SandboxWaitResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> global___GenericResult: ...
    def __init__(
        self,
        *,
        result: global___GenericResult | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["result", b"result"]) -> None: ...

global___SandboxWaitResponse = SandboxWaitResponse

class Schedule(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class Cron(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        CRON_STRING_FIELD_NUMBER: builtins.int
        TIMEZONE_FIELD_NUMBER: builtins.int
        cron_string: builtins.str
        timezone: builtins.str
        def __init__(
            self,
            *,
            cron_string: builtins.str = ...,
            timezone: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["cron_string", b"cron_string", "timezone", b"timezone"]) -> None: ...

    class Period(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        YEARS_FIELD_NUMBER: builtins.int
        MONTHS_FIELD_NUMBER: builtins.int
        WEEKS_FIELD_NUMBER: builtins.int
        DAYS_FIELD_NUMBER: builtins.int
        HOURS_FIELD_NUMBER: builtins.int
        MINUTES_FIELD_NUMBER: builtins.int
        SECONDS_FIELD_NUMBER: builtins.int
        years: builtins.int
        months: builtins.int
        weeks: builtins.int
        days: builtins.int
        hours: builtins.int
        minutes: builtins.int
        seconds: builtins.float
        def __init__(
            self,
            *,
            years: builtins.int = ...,
            months: builtins.int = ...,
            weeks: builtins.int = ...,
            days: builtins.int = ...,
            hours: builtins.int = ...,
            minutes: builtins.int = ...,
            seconds: builtins.float = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["days", b"days", "hours", b"hours", "minutes", b"minutes", "months", b"months", "seconds", b"seconds", "weeks", b"weeks", "years", b"years"]) -> None: ...

    CRON_FIELD_NUMBER: builtins.int
    PERIOD_FIELD_NUMBER: builtins.int
    @property
    def cron(self) -> global___Schedule.Cron: ...
    @property
    def period(self) -> global___Schedule.Period: ...
    def __init__(
        self,
        *,
        cron: global___Schedule.Cron | None = ...,
        period: global___Schedule.Period | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["cron", b"cron", "period", b"period", "schedule_oneof", b"schedule_oneof"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["cron", b"cron", "period", b"period", "schedule_oneof", b"schedule_oneof"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["schedule_oneof", b"schedule_oneof"]) -> typing_extensions.Literal["cron", "period"] | None: ...

global___Schedule = Schedule

class SchedulerPlacement(google.protobuf.message.Message):
    """TODO(irfansharif):
    - Fold in cloud, resource needs here too.
    - Allow specifying list of zones, cloud, fallback and alternative
      GPU types.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REGIONS_FIELD_NUMBER: builtins.int
    _ZONE_FIELD_NUMBER: builtins.int
    _LIFECYCLE_FIELD_NUMBER: builtins.int
    _INSTANCE_TYPES_FIELD_NUMBER: builtins.int
    @property
    def regions(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    _zone: builtins.str
    """TODO(irfansharif): Make these two repeated.
    admin-only
    """
    _lifecycle: builtins.str
    """admin-only, "on-demand" or "spot", else ignored"""
    @property
    def _instance_types(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """admin-only"""
    def __init__(
        self,
        *,
        regions: collections.abc.Iterable[builtins.str] | None = ...,
        _zone: builtins.str | None = ...,
        _lifecycle: builtins.str | None = ...,
        _instance_types: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["X_lifecycle", b"X_lifecycle", "X_zone", b"X_zone", "_lifecycle", b"_lifecycle", "_zone", b"_zone"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["X_lifecycle", b"X_lifecycle", "X_zone", b"X_zone", "_instance_types", b"_instance_types", "_lifecycle", b"_lifecycle", "_zone", b"_zone", "regions", b"regions"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["X_lifecycle", b"X_lifecycle"]) -> typing_extensions.Literal["_lifecycle"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["X_zone", b"X_zone"]) -> typing_extensions.Literal["_zone"] | None: ...

global___SchedulerPlacement = SchedulerPlacement

class SecretCreateRequest(google.protobuf.message.Message):
    """Not used by client anymore"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class EnvDictEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    ENV_DICT_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    TEMPLATE_TYPE_FIELD_NUMBER: builtins.int
    EXISTING_SECRET_ID_FIELD_NUMBER: builtins.int
    @property
    def env_dict(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    app_id: builtins.str
    template_type: builtins.str
    """todo: not used?"""
    existing_secret_id: builtins.str
    def __init__(
        self,
        *,
        env_dict: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        app_id: builtins.str = ...,
        template_type: builtins.str = ...,
        existing_secret_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "env_dict", b"env_dict", "existing_secret_id", b"existing_secret_id", "template_type", b"template_type"]) -> None: ...

global___SecretCreateRequest = SecretCreateRequest

class SecretCreateResponse(google.protobuf.message.Message):
    """Not used by client anymore"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SECRET_ID_FIELD_NUMBER: builtins.int
    secret_id: builtins.str
    def __init__(
        self,
        *,
        secret_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["secret_id", b"secret_id"]) -> None: ...

global___SecretCreateResponse = SecretCreateResponse

class SecretDeleteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SECRET_ID_FIELD_NUMBER: builtins.int
    secret_id: builtins.str
    def __init__(
        self,
        *,
        secret_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["secret_id", b"secret_id"]) -> None: ...

global___SecretDeleteRequest = SecretDeleteRequest

class SecretGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class EnvDictEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]) -> None: ...

    DEPLOYMENT_NAME_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    ENV_DICT_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    REQUIRED_KEYS_FIELD_NUMBER: builtins.int
    deployment_name: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    """Not used atm"""
    @property
    def env_dict(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    app_id: builtins.str
    """only used with OBJECT_CREATION_TYPE_ANONYMOUS_OWNED_BY_APP"""
    @property
    def required_keys(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        deployment_name: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
        env_dict: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        app_id: builtins.str = ...,
        required_keys: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "deployment_name", b"deployment_name", "env_dict", b"env_dict", "environment_name", b"environment_name", "namespace", b"namespace", "object_creation_type", b"object_creation_type", "required_keys", b"required_keys"]) -> None: ...

global___SecretGetOrCreateRequest = SecretGetOrCreateRequest

class SecretGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SECRET_ID_FIELD_NUMBER: builtins.int
    secret_id: builtins.str
    def __init__(
        self,
        *,
        secret_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["secret_id", b"secret_id"]) -> None: ...

global___SecretGetOrCreateResponse = SecretGetOrCreateResponse

class SecretListItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LABEL_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    LAST_USED_AT_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    SECRET_ID_FIELD_NUMBER: builtins.int
    label: builtins.str
    created_at: builtins.float
    last_used_at: builtins.float
    environment_name: builtins.str
    secret_id: builtins.str
    def __init__(
        self,
        *,
        label: builtins.str = ...,
        created_at: builtins.float = ...,
        last_used_at: builtins.float = ...,
        environment_name: builtins.str = ...,
        secret_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["created_at", b"created_at", "environment_name", b"environment_name", "label", b"label", "last_used_at", b"last_used_at", "secret_id", b"secret_id"]) -> None: ...

global___SecretListItem = SecretListItem

class SecretListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    """leaving empty will assume a singular environment"""
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name"]) -> None: ...

global___SecretListRequest = SecretListRequest

class SecretListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEMS_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SecretListItem]: ...
    environment_name: builtins.str
    """the environment that was listed (useful when relying on "default" logic)"""
    def __init__(
        self,
        *,
        items: collections.abc.Iterable[global___SecretListItem] | None = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "items", b"items"]) -> None: ...

global___SecretListResponse = SecretListResponse

class SharedVolumeDeleteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    shared_volume_id: builtins.str
    def __init__(
        self,
        *,
        shared_volume_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["shared_volume_id", b"shared_volume_id"]) -> None: ...

global___SharedVolumeDeleteRequest = SharedVolumeDeleteRequest

class SharedVolumeGetFileRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    shared_volume_id: builtins.str
    path: builtins.str
    def __init__(
        self,
        *,
        shared_volume_id: builtins.str = ...,
        path: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["path", b"path", "shared_volume_id", b"shared_volume_id"]) -> None: ...

global___SharedVolumeGetFileRequest = SharedVolumeGetFileRequest

class SharedVolumeGetFileResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    DATA_BLOB_ID_FIELD_NUMBER: builtins.int
    data: builtins.bytes
    data_blob_id: builtins.str
    def __init__(
        self,
        *,
        data: builtins.bytes = ...,
        data_blob_id: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["data_oneof", b"data_oneof"]) -> typing_extensions.Literal["data", "data_blob_id"] | None: ...

global___SharedVolumeGetFileResponse = SharedVolumeGetFileResponse

class SharedVolumeGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEPLOYMENT_NAME_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    deployment_name: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    app_id: builtins.str
    """only used with OBJECT_CREATION_TYPE_ANONYMOUS_OWNED_BY_APP"""
    def __init__(
        self,
        *,
        deployment_name: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
        app_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "deployment_name", b"deployment_name", "environment_name", b"environment_name", "namespace", b"namespace", "object_creation_type", b"object_creation_type"]) -> None: ...

global___SharedVolumeGetOrCreateRequest = SharedVolumeGetOrCreateRequest

class SharedVolumeGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    shared_volume_id: builtins.str
    def __init__(
        self,
        *,
        shared_volume_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["shared_volume_id", b"shared_volume_id"]) -> None: ...

global___SharedVolumeGetOrCreateResponse = SharedVolumeGetOrCreateResponse

class SharedVolumeHeartbeatRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    shared_volume_id: builtins.str
    def __init__(
        self,
        *,
        shared_volume_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["shared_volume_id", b"shared_volume_id"]) -> None: ...

global___SharedVolumeHeartbeatRequest = SharedVolumeHeartbeatRequest

class SharedVolumeListFilesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    shared_volume_id: builtins.str
    path: builtins.str
    def __init__(
        self,
        *,
        shared_volume_id: builtins.str = ...,
        path: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["path", b"path", "shared_volume_id", b"shared_volume_id"]) -> None: ...

global___SharedVolumeListFilesRequest = SharedVolumeListFilesRequest

class SharedVolumeListFilesResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENTRIES_FIELD_NUMBER: builtins.int
    @property
    def entries(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FileEntry]: ...
    def __init__(
        self,
        *,
        entries: collections.abc.Iterable[global___FileEntry] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["entries", b"entries"]) -> None: ...

global___SharedVolumeListFilesResponse = SharedVolumeListFilesResponse

class SharedVolumeListItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LABEL_FIELD_NUMBER: builtins.int
    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_FIELD_NUMBER: builtins.int
    label: builtins.str
    """app name of object entity app"""
    shared_volume_id: builtins.str
    created_at: builtins.float
    cloud_provider: global___CloudProvider.ValueType
    def __init__(
        self,
        *,
        label: builtins.str = ...,
        shared_volume_id: builtins.str = ...,
        created_at: builtins.float = ...,
        cloud_provider: global___CloudProvider.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["cloud_provider", b"cloud_provider", "created_at", b"created_at", "label", b"label", "shared_volume_id", b"shared_volume_id"]) -> None: ...

global___SharedVolumeListItem = SharedVolumeListItem

class SharedVolumeListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name"]) -> None: ...

global___SharedVolumeListRequest = SharedVolumeListRequest

class SharedVolumeListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEMS_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SharedVolumeListItem]: ...
    environment_name: builtins.str
    def __init__(
        self,
        *,
        items: collections.abc.Iterable[global___SharedVolumeListItem] | None = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "items", b"items"]) -> None: ...

global___SharedVolumeListResponse = SharedVolumeListResponse

class SharedVolumeMount(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MOUNT_PATH_FIELD_NUMBER: builtins.int
    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_FIELD_NUMBER: builtins.int
    ALLOW_CROSS_REGION_FIELD_NUMBER: builtins.int
    mount_path: builtins.str
    shared_volume_id: builtins.str
    cloud_provider: global___CloudProvider.ValueType
    allow_cross_region: builtins.bool
    def __init__(
        self,
        *,
        mount_path: builtins.str = ...,
        shared_volume_id: builtins.str = ...,
        cloud_provider: global___CloudProvider.ValueType = ...,
        allow_cross_region: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["allow_cross_region", b"allow_cross_region", "cloud_provider", b"cloud_provider", "mount_path", b"mount_path", "shared_volume_id", b"shared_volume_id"]) -> None: ...

global___SharedVolumeMount = SharedVolumeMount

class SharedVolumePutFileRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    SHA256_HEX_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    DATA_BLOB_ID_FIELD_NUMBER: builtins.int
    RESUMABLE_FIELD_NUMBER: builtins.int
    shared_volume_id: builtins.str
    path: builtins.str
    sha256_hex: builtins.str
    data: builtins.bytes
    data_blob_id: builtins.str
    resumable: builtins.bool
    """remove when required client version >= 47"""
    def __init__(
        self,
        *,
        shared_volume_id: builtins.str = ...,
        path: builtins.str = ...,
        sha256_hex: builtins.str = ...,
        data: builtins.bytes = ...,
        data_blob_id: builtins.str = ...,
        resumable: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof", "path", b"path", "resumable", b"resumable", "sha256_hex", b"sha256_hex", "shared_volume_id", b"shared_volume_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["data_oneof", b"data_oneof"]) -> typing_extensions.Literal["data", "data_blob_id"] | None: ...

global___SharedVolumePutFileRequest = SharedVolumePutFileRequest

class SharedVolumePutFileResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXISTS_FIELD_NUMBER: builtins.int
    exists: builtins.bool
    def __init__(
        self,
        *,
        exists: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["exists", b"exists"]) -> None: ...

global___SharedVolumePutFileResponse = SharedVolumePutFileResponse

class SharedVolumeRemoveFileRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHARED_VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    shared_volume_id: builtins.str
    path: builtins.str
    recursive: builtins.bool
    def __init__(
        self,
        *,
        shared_volume_id: builtins.str = ...,
        path: builtins.str = ...,
        recursive: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["path", b"path", "recursive", b"recursive", "shared_volume_id", b"shared_volume_id"]) -> None: ...

global___SharedVolumeRemoveFileRequest = SharedVolumeRemoveFileRequest

class SystemErrorMessage(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ERROR_CODE_FIELD_NUMBER: builtins.int
    ERROR_MESSAGE_FIELD_NUMBER: builtins.int
    error_code: global___SystemErrorCode.ValueType
    error_message: builtins.str
    def __init__(
        self,
        *,
        error_code: global___SystemErrorCode.ValueType = ...,
        error_message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["error_code", b"error_code", "error_message", b"error_message"]) -> None: ...

global___SystemErrorMessage = SystemErrorMessage

class TaskClusterHelloRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TASK_ID_FIELD_NUMBER: builtins.int
    CONTAINER_IP_FIELD_NUMBER: builtins.int
    task_id: builtins.str
    container_ip: builtins.str
    def __init__(
        self,
        *,
        task_id: builtins.str = ...,
        container_ip: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["container_ip", b"container_ip", "task_id", b"task_id"]) -> None: ...

global___TaskClusterHelloRequest = TaskClusterHelloRequest

class TaskClusterHelloResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CLUSTER_ID_FIELD_NUMBER: builtins.int
    CLUSTER_RANK_FIELD_NUMBER: builtins.int
    CONTAINER_IPS_FIELD_NUMBER: builtins.int
    cluster_id: builtins.str
    cluster_rank: builtins.int
    @property
    def container_ips(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """All IP addresses in cluster, ordered by cluster rank"""
    def __init__(
        self,
        *,
        cluster_id: builtins.str = ...,
        cluster_rank: builtins.int = ...,
        container_ips: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["cluster_id", b"cluster_id", "cluster_rank", b"cluster_rank", "container_ips", b"container_ips"]) -> None: ...

global___TaskClusterHelloResponse = TaskClusterHelloResponse

class TaskCurrentInputsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INPUT_IDS_FIELD_NUMBER: builtins.int
    @property
    def input_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    def __init__(
        self,
        *,
        input_ids: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["input_ids", b"input_ids"]) -> None: ...

global___TaskCurrentInputsResponse = TaskCurrentInputsResponse

class TaskInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    STARTED_AT_FIELD_NUMBER: builtins.int
    FINISHED_AT_FIELD_NUMBER: builtins.int
    RESULT_FIELD_NUMBER: builtins.int
    ENQUEUED_AT_FIELD_NUMBER: builtins.int
    GPU_TYPE_FIELD_NUMBER: builtins.int
    SANDBOX_ID_FIELD_NUMBER: builtins.int
    id: builtins.str
    started_at: builtins.float
    finished_at: builtins.float
    @property
    def result(self) -> global___GenericResult: ...
    enqueued_at: builtins.float
    gpu_type: builtins.str
    sandbox_id: builtins.str
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        started_at: builtins.float = ...,
        finished_at: builtins.float = ...,
        result: global___GenericResult | None = ...,
        enqueued_at: builtins.float = ...,
        gpu_type: builtins.str = ...,
        sandbox_id: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["enqueued_at", b"enqueued_at", "finished_at", b"finished_at", "gpu_type", b"gpu_type", "id", b"id", "result", b"result", "sandbox_id", b"sandbox_id", "started_at", b"started_at"]) -> None: ...

global___TaskInfo = TaskInfo

class TaskListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name"]) -> None: ...

global___TaskListRequest = TaskListRequest

class TaskListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TASKS_FIELD_NUMBER: builtins.int
    @property
    def tasks(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TaskStats]: ...
    def __init__(
        self,
        *,
        tasks: collections.abc.Iterable[global___TaskStats] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["tasks", b"tasks"]) -> None: ...

global___TaskListResponse = TaskListResponse

class TaskLogs(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    TASK_STATE_FIELD_NUMBER: builtins.int
    TIMESTAMP_FIELD_NUMBER: builtins.int
    FILE_DESCRIPTOR_FIELD_NUMBER: builtins.int
    TASK_PROGRESS_FIELD_NUMBER: builtins.int
    FUNCTION_CALL_ID_FIELD_NUMBER: builtins.int
    INPUT_ID_FIELD_NUMBER: builtins.int
    TIMESTAMP_NS_FIELD_NUMBER: builtins.int
    data: builtins.str
    task_state: global___TaskState.ValueType
    timestamp: builtins.float
    file_descriptor: global___FileDescriptor.ValueType
    @property
    def task_progress(self) -> global___TaskProgress: ...
    function_call_id: builtins.str
    input_id: builtins.str
    timestamp_ns: builtins.int
    def __init__(
        self,
        *,
        data: builtins.str = ...,
        task_state: global___TaskState.ValueType = ...,
        timestamp: builtins.float = ...,
        file_descriptor: global___FileDescriptor.ValueType = ...,
        task_progress: global___TaskProgress | None = ...,
        function_call_id: builtins.str = ...,
        input_id: builtins.str = ...,
        timestamp_ns: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["task_progress", b"task_progress"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "file_descriptor", b"file_descriptor", "function_call_id", b"function_call_id", "input_id", b"input_id", "task_progress", b"task_progress", "task_state", b"task_state", "timestamp", b"timestamp", "timestamp_ns", b"timestamp_ns"]) -> None: ...

global___TaskLogs = TaskLogs

class TaskLogsBatch(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TASK_ID_FIELD_NUMBER: builtins.int
    ITEMS_FIELD_NUMBER: builtins.int
    ENTRY_ID_FIELD_NUMBER: builtins.int
    APP_DONE_FIELD_NUMBER: builtins.int
    FUNCTION_ID_FIELD_NUMBER: builtins.int
    INPUT_ID_FIELD_NUMBER: builtins.int
    IMAGE_ID_FIELD_NUMBER: builtins.int
    EOF_FIELD_NUMBER: builtins.int
    PTY_EXEC_ID_FIELD_NUMBER: builtins.int
    ROOT_FUNCTION_ID_FIELD_NUMBER: builtins.int
    task_id: builtins.str
    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___TaskLogs]: ...
    entry_id: builtins.str
    app_done: builtins.bool
    function_id: builtins.str
    input_id: builtins.str
    image_id: builtins.str
    """Used for image logs"""
    eof: builtins.bool
    pty_exec_id: builtins.str
    """Used for interactive functions"""
    root_function_id: builtins.str
    def __init__(
        self,
        *,
        task_id: builtins.str = ...,
        items: collections.abc.Iterable[global___TaskLogs] | None = ...,
        entry_id: builtins.str = ...,
        app_done: builtins.bool = ...,
        function_id: builtins.str = ...,
        input_id: builtins.str = ...,
        image_id: builtins.str = ...,
        eof: builtins.bool = ...,
        pty_exec_id: builtins.str = ...,
        root_function_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_done", b"app_done", "entry_id", b"entry_id", "eof", b"eof", "function_id", b"function_id", "image_id", b"image_id", "input_id", b"input_id", "items", b"items", "pty_exec_id", b"pty_exec_id", "root_function_id", b"root_function_id", "task_id", b"task_id"]) -> None: ...

global___TaskLogsBatch = TaskLogsBatch

class TaskProgress(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LEN_FIELD_NUMBER: builtins.int
    POS_FIELD_NUMBER: builtins.int
    PROGRESS_TYPE_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    len: builtins.int
    pos: builtins.int
    progress_type: global___ProgressType.ValueType
    description: builtins.str
    def __init__(
        self,
        *,
        len: builtins.int = ...,
        pos: builtins.int = ...,
        progress_type: global___ProgressType.ValueType = ...,
        description: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["description", b"description", "len", b"len", "pos", b"pos", "progress_type", b"progress_type"]) -> None: ...

global___TaskProgress = TaskProgress

class TaskResultRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> global___GenericResult: ...
    def __init__(
        self,
        *,
        result: global___GenericResult | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["result", b"result"]) -> None: ...

global___TaskResultRequest = TaskResultRequest

class TaskStats(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TASK_ID_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    APP_DESCRIPTION_FIELD_NUMBER: builtins.int
    STARTED_AT_FIELD_NUMBER: builtins.int
    task_id: builtins.str
    app_id: builtins.str
    app_description: builtins.str
    started_at: builtins.float
    def __init__(
        self,
        *,
        task_id: builtins.str = ...,
        app_id: builtins.str = ...,
        app_description: builtins.str = ...,
        started_at: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_description", b"app_description", "app_id", b"app_id", "started_at", b"started_at", "task_id", b"task_id"]) -> None: ...

global___TaskStats = TaskStats

class TaskTemplate(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RANK_FIELD_NUMBER: builtins.int
    RESOURCES_FIELD_NUMBER: builtins.int
    TARGET_CONCURRENT_INPUTS_FIELD_NUMBER: builtins.int
    MAX_CONCURRENT_INPUTS_FIELD_NUMBER: builtins.int
    INDEX_FIELD_NUMBER: builtins.int
    rank: builtins.int
    @property
    def resources(self) -> global___Resources: ...
    target_concurrent_inputs: builtins.int
    max_concurrent_inputs: builtins.int
    index: builtins.int
    """TODO(irfansharif): Just move this into a column in the task table instead?
    Deprecate all above fields and get rid of this message altogether
    pointer into FunctionData, if using that as the underlying definition type
    """
    def __init__(
        self,
        *,
        rank: builtins.int = ...,
        resources: global___Resources | None = ...,
        target_concurrent_inputs: builtins.int = ...,
        max_concurrent_inputs: builtins.int = ...,
        index: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["resources", b"resources"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["index", b"index", "max_concurrent_inputs", b"max_concurrent_inputs", "rank", b"rank", "resources", b"resources", "target_concurrent_inputs", b"target_concurrent_inputs"]) -> None: ...

global___TaskTemplate = TaskTemplate

class TokenFlowCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    UTM_SOURCE_FIELD_NUMBER: builtins.int
    LOCALHOST_PORT_FIELD_NUMBER: builtins.int
    NEXT_URL_FIELD_NUMBER: builtins.int
    utm_source: builtins.str
    localhost_port: builtins.int
    next_url: builtins.str
    def __init__(
        self,
        *,
        utm_source: builtins.str = ...,
        localhost_port: builtins.int = ...,
        next_url: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["localhost_port", b"localhost_port", "next_url", b"next_url", "utm_source", b"utm_source"]) -> None: ...

global___TokenFlowCreateRequest = TokenFlowCreateRequest

class TokenFlowCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TOKEN_FLOW_ID_FIELD_NUMBER: builtins.int
    WEB_URL_FIELD_NUMBER: builtins.int
    CODE_FIELD_NUMBER: builtins.int
    WAIT_SECRET_FIELD_NUMBER: builtins.int
    token_flow_id: builtins.str
    web_url: builtins.str
    code: builtins.str
    wait_secret: builtins.str
    def __init__(
        self,
        *,
        token_flow_id: builtins.str = ...,
        web_url: builtins.str = ...,
        code: builtins.str = ...,
        wait_secret: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["code", b"code", "token_flow_id", b"token_flow_id", "wait_secret", b"wait_secret", "web_url", b"web_url"]) -> None: ...

global___TokenFlowCreateResponse = TokenFlowCreateResponse

class TokenFlowWaitRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TIMEOUT_FIELD_NUMBER: builtins.int
    TOKEN_FLOW_ID_FIELD_NUMBER: builtins.int
    WAIT_SECRET_FIELD_NUMBER: builtins.int
    timeout: builtins.float
    token_flow_id: builtins.str
    wait_secret: builtins.str
    def __init__(
        self,
        *,
        timeout: builtins.float = ...,
        token_flow_id: builtins.str = ...,
        wait_secret: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["timeout", b"timeout", "token_flow_id", b"token_flow_id", "wait_secret", b"wait_secret"]) -> None: ...

global___TokenFlowWaitRequest = TokenFlowWaitRequest

class TokenFlowWaitResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TOKEN_ID_FIELD_NUMBER: builtins.int
    TOKEN_SECRET_FIELD_NUMBER: builtins.int
    TIMEOUT_FIELD_NUMBER: builtins.int
    WORKSPACE_USERNAME_FIELD_NUMBER: builtins.int
    token_id: builtins.str
    token_secret: builtins.str
    timeout: builtins.bool
    workspace_username: builtins.str
    def __init__(
        self,
        *,
        token_id: builtins.str = ...,
        token_secret: builtins.str = ...,
        timeout: builtins.bool = ...,
        workspace_username: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["timeout", b"timeout", "token_id", b"token_id", "token_secret", b"token_secret", "workspace_username", b"workspace_username"]) -> None: ...

global___TokenFlowWaitResponse = TokenFlowWaitResponse

class TunnelData(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HOST_FIELD_NUMBER: builtins.int
    PORT_FIELD_NUMBER: builtins.int
    UNENCRYPTED_HOST_FIELD_NUMBER: builtins.int
    UNENCRYPTED_PORT_FIELD_NUMBER: builtins.int
    CONTAINER_PORT_FIELD_NUMBER: builtins.int
    host: builtins.str
    port: builtins.int
    unencrypted_host: builtins.str
    unencrypted_port: builtins.int
    container_port: builtins.int
    def __init__(
        self,
        *,
        host: builtins.str = ...,
        port: builtins.int = ...,
        unencrypted_host: builtins.str | None = ...,
        unencrypted_port: builtins.int | None = ...,
        container_port: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_unencrypted_host", b"_unencrypted_host", "_unencrypted_port", b"_unencrypted_port", "unencrypted_host", b"unencrypted_host", "unencrypted_port", b"unencrypted_port"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_unencrypted_host", b"_unencrypted_host", "_unencrypted_port", b"_unencrypted_port", "container_port", b"container_port", "host", b"host", "port", b"port", "unencrypted_host", b"unencrypted_host", "unencrypted_port", b"unencrypted_port"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_unencrypted_host", b"_unencrypted_host"]) -> typing_extensions.Literal["unencrypted_host"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_unencrypted_port", b"_unencrypted_port"]) -> typing_extensions.Literal["unencrypted_port"] | None: ...

global___TunnelData = TunnelData

class TunnelStartRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PORT_FIELD_NUMBER: builtins.int
    UNENCRYPTED_FIELD_NUMBER: builtins.int
    TUNNEL_TYPE_FIELD_NUMBER: builtins.int
    port: builtins.int
    unencrypted: builtins.bool
    tunnel_type: global___TunnelType.ValueType
    def __init__(
        self,
        *,
        port: builtins.int = ...,
        unencrypted: builtins.bool = ...,
        tunnel_type: global___TunnelType.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_tunnel_type", b"_tunnel_type", "tunnel_type", b"tunnel_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_tunnel_type", b"_tunnel_type", "port", b"port", "tunnel_type", b"tunnel_type", "unencrypted", b"unencrypted"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_tunnel_type", b"_tunnel_type"]) -> typing_extensions.Literal["tunnel_type"] | None: ...

global___TunnelStartRequest = TunnelStartRequest

class TunnelStartResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    HOST_FIELD_NUMBER: builtins.int
    PORT_FIELD_NUMBER: builtins.int
    UNENCRYPTED_HOST_FIELD_NUMBER: builtins.int
    UNENCRYPTED_PORT_FIELD_NUMBER: builtins.int
    host: builtins.str
    port: builtins.int
    unencrypted_host: builtins.str
    unencrypted_port: builtins.int
    def __init__(
        self,
        *,
        host: builtins.str = ...,
        port: builtins.int = ...,
        unencrypted_host: builtins.str | None = ...,
        unencrypted_port: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_unencrypted_host", b"_unencrypted_host", "_unencrypted_port", b"_unencrypted_port", "unencrypted_host", b"unencrypted_host", "unencrypted_port", b"unencrypted_port"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_unencrypted_host", b"_unencrypted_host", "_unencrypted_port", b"_unencrypted_port", "host", b"host", "port", b"port", "unencrypted_host", b"unencrypted_host", "unencrypted_port", b"unencrypted_port"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_unencrypted_host", b"_unencrypted_host"]) -> typing_extensions.Literal["unencrypted_host"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_unencrypted_port", b"_unencrypted_port"]) -> typing_extensions.Literal["unencrypted_port"] | None: ...

global___TunnelStartResponse = TunnelStartResponse

class TunnelStopRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PORT_FIELD_NUMBER: builtins.int
    port: builtins.int
    def __init__(
        self,
        *,
        port: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["port", b"port"]) -> None: ...

global___TunnelStopRequest = TunnelStopRequest

class TunnelStopResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXISTS_FIELD_NUMBER: builtins.int
    exists: builtins.bool
    def __init__(
        self,
        *,
        exists: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["exists", b"exists"]) -> None: ...

global___TunnelStopResponse = TunnelStopResponse

class VolumeCommitRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    """NOTE(staffan): Mounting a volume in multiple locations is not supported, so volume_id alone uniquely identifies
    a volume mount.
    """
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["volume_id", b"volume_id"]) -> None: ...

global___VolumeCommitRequest = VolumeCommitRequest

class VolumeCommitResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SKIP_RELOAD_FIELD_NUMBER: builtins.int
    skip_reload: builtins.bool
    def __init__(
        self,
        *,
        skip_reload: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["skip_reload", b"skip_reload"]) -> None: ...

global___VolumeCommitResponse = VolumeCommitResponse

class VolumeCopyFiles2Request(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    SRC_PATHS_FIELD_NUMBER: builtins.int
    DST_PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    @property
    def src_paths(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    dst_path: builtins.str
    recursive: builtins.bool
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        src_paths: collections.abc.Iterable[builtins.str] | None = ...,
        dst_path: builtins.str = ...,
        recursive: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dst_path", b"dst_path", "recursive", b"recursive", "src_paths", b"src_paths", "volume_id", b"volume_id"]) -> None: ...

global___VolumeCopyFiles2Request = VolumeCopyFiles2Request

class VolumeCopyFilesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    SRC_PATHS_FIELD_NUMBER: builtins.int
    DST_PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    @property
    def src_paths(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    dst_path: builtins.str
    recursive: builtins.bool
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        src_paths: collections.abc.Iterable[builtins.str] | None = ...,
        dst_path: builtins.str = ...,
        recursive: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["dst_path", b"dst_path", "recursive", b"recursive", "src_paths", b"src_paths", "volume_id", b"volume_id"]) -> None: ...

global___VolumeCopyFilesRequest = VolumeCopyFilesRequest

class VolumeDeleteRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    environment_name: builtins.str
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "volume_id", b"volume_id"]) -> None: ...

global___VolumeDeleteRequest = VolumeDeleteRequest

class VolumeGetFile2Request(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    START_FIELD_NUMBER: builtins.int
    LEN_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    path: builtins.str
    start: builtins.int
    len: builtins.int
    """0 is interpreted as 'read to end'"""
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        path: builtins.str = ...,
        start: builtins.int = ...,
        len: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["len", b"len", "path", b"path", "start", b"start", "volume_id", b"volume_id"]) -> None: ...

global___VolumeGetFile2Request = VolumeGetFile2Request

class VolumeGetFile2Response(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GET_URLS_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    START_FIELD_NUMBER: builtins.int
    LEN_FIELD_NUMBER: builtins.int
    @property
    def get_urls(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]: ...
    size: builtins.int
    """total file size"""
    start: builtins.int
    """file position of first byte returned"""
    len: builtins.int
    """number of bytes returned"""
    def __init__(
        self,
        *,
        get_urls: collections.abc.Iterable[builtins.str] | None = ...,
        size: builtins.int = ...,
        start: builtins.int = ...,
        len: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["get_urls", b"get_urls", "len", b"len", "size", b"size", "start", b"start"]) -> None: ...

global___VolumeGetFile2Response = VolumeGetFile2Response

class VolumeGetFileRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    START_FIELD_NUMBER: builtins.int
    LEN_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    path: builtins.str
    start: builtins.int
    len: builtins.int
    """0 is interpreted as 'read to end'"""
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        path: builtins.str = ...,
        start: builtins.int = ...,
        len: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["len", b"len", "path", b"path", "start", b"start", "volume_id", b"volume_id"]) -> None: ...

global___VolumeGetFileRequest = VolumeGetFileRequest

class VolumeGetFileResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_FIELD_NUMBER: builtins.int
    DATA_BLOB_ID_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    START_FIELD_NUMBER: builtins.int
    LEN_FIELD_NUMBER: builtins.int
    data: builtins.bytes
    data_blob_id: builtins.str
    size: builtins.int
    """total file size"""
    start: builtins.int
    """file position of first byte returned"""
    len: builtins.int
    """number of bytes returned"""
    def __init__(
        self,
        *,
        data: builtins.bytes = ...,
        data_blob_id: builtins.str = ...,
        size: builtins.int = ...,
        start: builtins.int = ...,
        len: builtins.int = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["data", b"data", "data_blob_id", b"data_blob_id", "data_oneof", b"data_oneof", "len", b"len", "size", b"size", "start", b"start"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["data_oneof", b"data_oneof"]) -> typing_extensions.Literal["data", "data_blob_id"] | None: ...

global___VolumeGetFileResponse = VolumeGetFileResponse

class VolumeGetOrCreateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DEPLOYMENT_NAME_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    OBJECT_CREATION_TYPE_FIELD_NUMBER: builtins.int
    APP_ID_FIELD_NUMBER: builtins.int
    VERSION_FIELD_NUMBER: builtins.int
    deployment_name: builtins.str
    namespace: global___DeploymentNamespace.ValueType
    environment_name: builtins.str
    object_creation_type: global___ObjectCreationType.ValueType
    app_id: builtins.str
    """only used with OBJECT_CREATION_TYPE_ANONYMOUS_OWNED_BY_APP"""
    version: global___VolumeFsVersion.ValueType
    def __init__(
        self,
        *,
        deployment_name: builtins.str = ...,
        namespace: global___DeploymentNamespace.ValueType = ...,
        environment_name: builtins.str = ...,
        object_creation_type: global___ObjectCreationType.ValueType = ...,
        app_id: builtins.str = ...,
        version: global___VolumeFsVersion.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["app_id", b"app_id", "deployment_name", b"deployment_name", "environment_name", b"environment_name", "namespace", b"namespace", "object_creation_type", b"object_creation_type", "version", b"version"]) -> None: ...

global___VolumeGetOrCreateRequest = VolumeGetOrCreateRequest

class VolumeGetOrCreateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    VERSION_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    version: global___VolumeFsVersion.ValueType
    @property
    def metadata(self) -> global___VolumeMetadata: ...
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        version: global___VolumeFsVersion.ValueType = ...,
        metadata: global___VolumeMetadata | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["metadata", b"metadata"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["metadata", b"metadata", "version", b"version", "volume_id", b"volume_id"]) -> None: ...

global___VolumeGetOrCreateResponse = VolumeGetOrCreateResponse

class VolumeHeartbeatRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["volume_id", b"volume_id"]) -> None: ...

global___VolumeHeartbeatRequest = VolumeHeartbeatRequest

class VolumeListFiles2Request(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    MAX_ENTRIES_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    path: builtins.str
    recursive: builtins.bool
    max_entries: builtins.int
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        path: builtins.str = ...,
        recursive: builtins.bool = ...,
        max_entries: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_max_entries", b"_max_entries", "max_entries", b"max_entries"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_max_entries", b"_max_entries", "max_entries", b"max_entries", "path", b"path", "recursive", b"recursive", "volume_id", b"volume_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_max_entries", b"_max_entries"]) -> typing_extensions.Literal["max_entries"] | None: ...

global___VolumeListFiles2Request = VolumeListFiles2Request

class VolumeListFiles2Response(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENTRIES_FIELD_NUMBER: builtins.int
    @property
    def entries(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FileEntry]: ...
    def __init__(
        self,
        *,
        entries: collections.abc.Iterable[global___FileEntry] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["entries", b"entries"]) -> None: ...

global___VolumeListFiles2Response = VolumeListFiles2Response

class VolumeListFilesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    MAX_ENTRIES_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    path: builtins.str
    recursive: builtins.bool
    max_entries: builtins.int
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        path: builtins.str = ...,
        recursive: builtins.bool = ...,
        max_entries: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing_extensions.Literal["_max_entries", b"_max_entries", "max_entries", b"max_entries"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing_extensions.Literal["_max_entries", b"_max_entries", "max_entries", b"max_entries", "path", b"path", "recursive", b"recursive", "volume_id", b"volume_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions.Literal["_max_entries", b"_max_entries"]) -> typing_extensions.Literal["max_entries"] | None: ...

global___VolumeListFilesRequest = VolumeListFilesRequest

class VolumeListFilesResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENTRIES_FIELD_NUMBER: builtins.int
    @property
    def entries(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___FileEntry]: ...
    def __init__(
        self,
        *,
        entries: collections.abc.Iterable[global___FileEntry] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["entries", b"entries"]) -> None: ...

global___VolumeListFilesResponse = VolumeListFilesResponse

class VolumeListItem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LABEL_FIELD_NUMBER: builtins.int
    VOLUME_ID_FIELD_NUMBER: builtins.int
    CREATED_AT_FIELD_NUMBER: builtins.int
    label: builtins.str
    """app name of object entity app"""
    volume_id: builtins.str
    created_at: builtins.float
    def __init__(
        self,
        *,
        label: builtins.str = ...,
        volume_id: builtins.str = ...,
        created_at: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["created_at", b"created_at", "label", b"label", "volume_id", b"volume_id"]) -> None: ...

global___VolumeListItem = VolumeListItem

class VolumeListRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    environment_name: builtins.str
    def __init__(
        self,
        *,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name"]) -> None: ...

global___VolumeListRequest = VolumeListRequest

class VolumeListResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEMS_FIELD_NUMBER: builtins.int
    ENVIRONMENT_NAME_FIELD_NUMBER: builtins.int
    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___VolumeListItem]: ...
    environment_name: builtins.str
    def __init__(
        self,
        *,
        items: collections.abc.Iterable[global___VolumeListItem] | None = ...,
        environment_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["environment_name", b"environment_name", "items", b"items"]) -> None: ...

global___VolumeListResponse = VolumeListResponse

class VolumeMetadata(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VERSION_FIELD_NUMBER: builtins.int
    version: global___VolumeFsVersion.ValueType
    def __init__(
        self,
        *,
        version: global___VolumeFsVersion.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["version", b"version"]) -> None: ...

global___VolumeMetadata = VolumeMetadata

class VolumeMount(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    MOUNT_PATH_FIELD_NUMBER: builtins.int
    ALLOW_BACKGROUND_COMMITS_FIELD_NUMBER: builtins.int
    READ_ONLY_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    mount_path: builtins.str
    allow_background_commits: builtins.bool
    read_only: builtins.bool
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        mount_path: builtins.str = ...,
        allow_background_commits: builtins.bool = ...,
        read_only: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["allow_background_commits", b"allow_background_commits", "mount_path", b"mount_path", "read_only", b"read_only", "volume_id", b"volume_id"]) -> None: ...

global___VolumeMount = VolumeMount

class VolumePutFiles2Request(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class File(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        PATH_FIELD_NUMBER: builtins.int
        SIZE_FIELD_NUMBER: builtins.int
        BLOCKS_FIELD_NUMBER: builtins.int
        MODE_FIELD_NUMBER: builtins.int
        path: builtins.str
        """Destination path of the file to be uploaded, including any parent dirs
        etc.; for example "foo/bar/baz.txt"
        """
        size: builtins.int
        """The total size of the file, in bytes."""
        @property
        def blocks(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___VolumePutFiles2Request.Block]:
            """The blocks, in units of 8MiB, that this file consists of."""
        mode: builtins.int
        """Unix file permission bits `st_mode`."""
        def __init__(
            self,
            *,
            path: builtins.str = ...,
            size: builtins.int = ...,
            blocks: collections.abc.Iterable[global___VolumePutFiles2Request.Block] | None = ...,
            mode: builtins.int | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_mode", b"_mode", "mode", b"mode"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_mode", b"_mode", "blocks", b"blocks", "mode", b"mode", "path", b"path", "size", b"size"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_mode", b"_mode"]) -> typing_extensions.Literal["mode"] | None: ...

    class Block(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        CONTENTS_SHA256_FIELD_NUMBER: builtins.int
        PUT_RESPONSE_FIELD_NUMBER: builtins.int
        contents_sha256: builtins.bytes
        """The SHA256 digest of the contents of this block, in raw (ie. 32 bytes)
        form for compactness.
        """
        put_response: builtins.bytes
        """From a previous call to `VolumePutFiles2`, we might have gotten a
        response indicating that this block was missing.

        For such a block, this field contains the raw bytes of the body that
        was returned from the HTTP PUT request when the client made a request
        for the `put_url` returned in the previous `VolumePutFiles2Response`.
        """
        def __init__(
            self,
            *,
            contents_sha256: builtins.bytes = ...,
            put_response: builtins.bytes | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing_extensions.Literal["_put_response", b"_put_response", "put_response", b"put_response"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing_extensions.Literal["_put_response", b"_put_response", "contents_sha256", b"contents_sha256", "put_response", b"put_response"]) -> None: ...
        def WhichOneof(self, oneof_group: typing_extensions.Literal["_put_response", b"_put_response"]) -> typing_extensions.Literal["put_response"] | None: ...

    VOLUME_ID_FIELD_NUMBER: builtins.int
    FILES_FIELD_NUMBER: builtins.int
    DISALLOW_OVERWRITE_EXISTING_FILES_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    """The ID of the volume to put/upload files into."""
    @property
    def files(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___VolumePutFiles2Request.File]:
        """List of files to put/upload."""
    disallow_overwrite_existing_files: builtins.bool
    """If set to true, prevent overwriting existing files. (Note that we don't
    allow overwriting existing directories with uploaded files regardless.)
    """
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        files: collections.abc.Iterable[global___VolumePutFiles2Request.File] | None = ...,
        disallow_overwrite_existing_files: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["disallow_overwrite_existing_files", b"disallow_overwrite_existing_files", "files", b"files", "volume_id", b"volume_id"]) -> None: ...

global___VolumePutFiles2Request = VolumePutFiles2Request

class VolumePutFiles2Response(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class MissingBlock(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        FILE_INDEX_FIELD_NUMBER: builtins.int
        BLOCK_INDEX_FIELD_NUMBER: builtins.int
        PUT_URL_FIELD_NUMBER: builtins.int
        file_index: builtins.int
        """Index of the file in the original `files` field of the request."""
        block_index: builtins.int
        """The index of the block in the original `files[file_index].blocks` of the
        request.
        """
        put_url: builtins.str
        """Make a HTTP PUT request to this endpoint with the blocks' contents as
        the body.
        """
        def __init__(
            self,
            *,
            file_index: builtins.int = ...,
            block_index: builtins.int = ...,
            put_url: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing_extensions.Literal["block_index", b"block_index", "file_index", b"file_index", "put_url", b"put_url"]) -> None: ...

    MISSING_BLOCKS_FIELD_NUMBER: builtins.int
    @property
    def missing_blocks(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___VolumePutFiles2Response.MissingBlock]:
        """Blocks that are currently missing in the volume, because the file did not
        exist, or because the block checksum from `blocks_sha256` in the request
        did not match the current contents of the file.

        Values will be returned sorted by `(file_index, block_index)`.

        If this field is empty, it means that the files were uploaded successfully
        and/or that the request was an idempotent no-op.
        """
    def __init__(
        self,
        *,
        missing_blocks: collections.abc.Iterable[global___VolumePutFiles2Response.MissingBlock] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["missing_blocks", b"missing_blocks"]) -> None: ...

global___VolumePutFiles2Response = VolumePutFiles2Response

class VolumePutFilesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    FILES_FIELD_NUMBER: builtins.int
    DISALLOW_OVERWRITE_EXISTING_FILES_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    @property
    def files(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MountFile]:
        """TODO(staffan): This is obviously unfortunately named, but provides what we need - consider renaming."""
    disallow_overwrite_existing_files: builtins.bool
    """If set to true, prevent overwriting existing files. (Note that we don't allow overwriting
    existing directories with uploaded files regardless.)
    """
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        files: collections.abc.Iterable[global___MountFile] | None = ...,
        disallow_overwrite_existing_files: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["disallow_overwrite_existing_files", b"disallow_overwrite_existing_files", "files", b"files", "volume_id", b"volume_id"]) -> None: ...

global___VolumePutFilesRequest = VolumePutFilesRequest

class VolumeReloadRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    """NOTE(staffan): Mounting a volume in multiple locations is not supported, so volume_id alone uniquely identifies
    a volume mount.
    """
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["volume_id", b"volume_id"]) -> None: ...

global___VolumeReloadRequest = VolumeReloadRequest

class VolumeRemoveFile2Request(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    path: builtins.str
    recursive: builtins.bool
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        path: builtins.str = ...,
        recursive: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["path", b"path", "recursive", b"recursive", "volume_id", b"volume_id"]) -> None: ...

global___VolumeRemoveFile2Request = VolumeRemoveFile2Request

class VolumeRemoveFileRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    PATH_FIELD_NUMBER: builtins.int
    RECURSIVE_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    path: builtins.str
    recursive: builtins.bool
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        path: builtins.str = ...,
        recursive: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["path", b"path", "recursive", b"recursive", "volume_id", b"volume_id"]) -> None: ...

global___VolumeRemoveFileRequest = VolumeRemoveFileRequest

class VolumeRenameRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VOLUME_ID_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    volume_id: builtins.str
    name: builtins.str
    def __init__(
        self,
        *,
        volume_id: builtins.str = ...,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["name", b"name", "volume_id", b"volume_id"]) -> None: ...

global___VolumeRenameRequest = VolumeRenameRequest

class Warning(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _WarningType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _WarningTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[Warning._WarningType.ValueType], builtins.type):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        WARNING_TYPE_UNSPECIFIED: Warning._WarningType.ValueType  # 0
        WARNING_TYPE_CLIENT_DEPRECATION: Warning._WarningType.ValueType  # 1
        WARNING_TYPE_RESOURCE_LIMIT: Warning._WarningType.ValueType  # 2
        WARNING_TYPE_FUNCTION_CONFIGURATION: Warning._WarningType.ValueType  # 3

    class WarningType(_WarningType, metaclass=_WarningTypeEnumTypeWrapper): ...
    WARNING_TYPE_UNSPECIFIED: Warning.WarningType.ValueType  # 0
    WARNING_TYPE_CLIENT_DEPRECATION: Warning.WarningType.ValueType  # 1
    WARNING_TYPE_RESOURCE_LIMIT: Warning.WarningType.ValueType  # 2
    WARNING_TYPE_FUNCTION_CONFIGURATION: Warning.WarningType.ValueType  # 3

    TYPE_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    type: global___Warning.WarningType.ValueType
    message: builtins.str
    def __init__(
        self,
        *,
        type: global___Warning.WarningType.ValueType = ...,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["message", b"message", "type", b"type"]) -> None: ...

global___Warning = Warning

class WebUrlInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TRUNCATED_FIELD_NUMBER: builtins.int
    HAS_UNIQUE_HASH_FIELD_NUMBER: builtins.int
    LABEL_STOLEN_FIELD_NUMBER: builtins.int
    truncated: builtins.bool
    has_unique_hash: builtins.bool
    label_stolen: builtins.bool
    def __init__(
        self,
        *,
        truncated: builtins.bool = ...,
        has_unique_hash: builtins.bool = ...,
        label_stolen: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["has_unique_hash", b"has_unique_hash", "label_stolen", b"label_stolen", "truncated", b"truncated"]) -> None: ...

global___WebUrlInfo = WebUrlInfo

class WebhookConfig(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    METHOD_FIELD_NUMBER: builtins.int
    REQUESTED_SUFFIX_FIELD_NUMBER: builtins.int
    ASYNC_MODE_FIELD_NUMBER: builtins.int
    CUSTOM_DOMAINS_FIELD_NUMBER: builtins.int
    WEB_SERVER_PORT_FIELD_NUMBER: builtins.int
    WEB_SERVER_STARTUP_TIMEOUT_FIELD_NUMBER: builtins.int
    WEB_ENDPOINT_DOCS_FIELD_NUMBER: builtins.int
    REQUIRES_PROXY_AUTH_FIELD_NUMBER: builtins.int
    type: global___WebhookType.ValueType
    method: builtins.str
    requested_suffix: builtins.str
    async_mode: global___WebhookAsyncMode.ValueType
    @property
    def custom_domains(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CustomDomainConfig]: ...
    web_server_port: builtins.int
    web_server_startup_timeout: builtins.float
    web_endpoint_docs: builtins.bool
    requires_proxy_auth: builtins.bool
    def __init__(
        self,
        *,
        type: global___WebhookType.ValueType = ...,
        method: builtins.str = ...,
        requested_suffix: builtins.str = ...,
        async_mode: global___WebhookAsyncMode.ValueType = ...,
        custom_domains: collections.abc.Iterable[global___CustomDomainConfig] | None = ...,
        web_server_port: builtins.int = ...,
        web_server_startup_timeout: builtins.float = ...,
        web_endpoint_docs: builtins.bool = ...,
        requires_proxy_auth: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["async_mode", b"async_mode", "custom_domains", b"custom_domains", "method", b"method", "requested_suffix", b"requested_suffix", "requires_proxy_auth", b"requires_proxy_auth", "type", b"type", "web_endpoint_docs", b"web_endpoint_docs", "web_server_port", b"web_server_port", "web_server_startup_timeout", b"web_server_startup_timeout"]) -> None: ...

global___WebhookConfig = WebhookConfig

class WorkspaceNameLookupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WORKSPACE_NAME_FIELD_NUMBER: builtins.int
    USERNAME_FIELD_NUMBER: builtins.int
    workspace_name: builtins.str
    username: builtins.str
    def __init__(
        self,
        *,
        workspace_name: builtins.str = ...,
        username: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing_extensions.Literal["username", b"username", "workspace_name", b"workspace_name"]) -> None: ...

global___WorkspaceNameLookupResponse = WorkspaceNameLookupResponse
