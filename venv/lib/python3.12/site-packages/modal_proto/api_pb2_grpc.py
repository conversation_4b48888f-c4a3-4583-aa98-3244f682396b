# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from modal_proto import api_pb2 as modal__proto_dot_api__pb2


class ModalClientStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.AppClientDisconnect = channel.unary_unary(
                '/modal.client.ModalClient/AppClientDisconnect',
                request_serializer=modal__proto_dot_api__pb2.AppClientDisconnectRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.AppCreate = channel.unary_unary(
                '/modal.client.ModalClient/AppCreate',
                request_serializer=modal__proto_dot_api__pb2.AppCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppCreateResponse.FromString,
                )
        self.AppDeploy = channel.unary_unary(
                '/modal.client.ModalClient/AppDeploy',
                request_serializer=modal__proto_dot_api__pb2.AppDeployRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppDeployResponse.FromString,
                )
        self.AppDeploymentHistory = channel.unary_unary(
                '/modal.client.ModalClient/AppDeploymentHistory',
                request_serializer=modal__proto_dot_api__pb2.AppDeploymentHistoryRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppDeploymentHistoryResponse.FromString,
                )
        self.AppGetByDeploymentName = channel.unary_unary(
                '/modal.client.ModalClient/AppGetByDeploymentName',
                request_serializer=modal__proto_dot_api__pb2.AppGetByDeploymentNameRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppGetByDeploymentNameResponse.FromString,
                )
        self.AppGetLayout = channel.unary_unary(
                '/modal.client.ModalClient/AppGetLayout',
                request_serializer=modal__proto_dot_api__pb2.AppGetLayoutRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppGetLayoutResponse.FromString,
                )
        self.AppGetLogs = channel.unary_stream(
                '/modal.client.ModalClient/AppGetLogs',
                request_serializer=modal__proto_dot_api__pb2.AppGetLogsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TaskLogsBatch.FromString,
                )
        self.AppGetObjects = channel.unary_unary(
                '/modal.client.ModalClient/AppGetObjects',
                request_serializer=modal__proto_dot_api__pb2.AppGetObjectsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppGetObjectsResponse.FromString,
                )
        self.AppGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/AppGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.AppGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppGetOrCreateResponse.FromString,
                )
        self.AppHeartbeat = channel.unary_unary(
                '/modal.client.ModalClient/AppHeartbeat',
                request_serializer=modal__proto_dot_api__pb2.AppHeartbeatRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.AppList = channel.unary_unary(
                '/modal.client.ModalClient/AppList',
                request_serializer=modal__proto_dot_api__pb2.AppListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppListResponse.FromString,
                )
        self.AppLookup = channel.unary_unary(
                '/modal.client.ModalClient/AppLookup',
                request_serializer=modal__proto_dot_api__pb2.AppLookupRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppLookupResponse.FromString,
                )
        self.AppPublish = channel.unary_unary(
                '/modal.client.ModalClient/AppPublish',
                request_serializer=modal__proto_dot_api__pb2.AppPublishRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AppPublishResponse.FromString,
                )
        self.AppRollback = channel.unary_unary(
                '/modal.client.ModalClient/AppRollback',
                request_serializer=modal__proto_dot_api__pb2.AppRollbackRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.AppSetObjects = channel.unary_unary(
                '/modal.client.ModalClient/AppSetObjects',
                request_serializer=modal__proto_dot_api__pb2.AppSetObjectsRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.AppStop = channel.unary_unary(
                '/modal.client.ModalClient/AppStop',
                request_serializer=modal__proto_dot_api__pb2.AppStopRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.AttemptAwait = channel.unary_unary(
                '/modal.client.ModalClient/AttemptAwait',
                request_serializer=modal__proto_dot_api__pb2.AttemptAwaitRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AttemptAwaitResponse.FromString,
                )
        self.AttemptRetry = channel.unary_unary(
                '/modal.client.ModalClient/AttemptRetry',
                request_serializer=modal__proto_dot_api__pb2.AttemptRetryRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AttemptRetryResponse.FromString,
                )
        self.AttemptStart = channel.unary_unary(
                '/modal.client.ModalClient/AttemptStart',
                request_serializer=modal__proto_dot_api__pb2.AttemptStartRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.AttemptStartResponse.FromString,
                )
        self.BlobCreate = channel.unary_unary(
                '/modal.client.ModalClient/BlobCreate',
                request_serializer=modal__proto_dot_api__pb2.BlobCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.BlobCreateResponse.FromString,
                )
        self.BlobGet = channel.unary_unary(
                '/modal.client.ModalClient/BlobGet',
                request_serializer=modal__proto_dot_api__pb2.BlobGetRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.BlobGetResponse.FromString,
                )
        self.ClassCreate = channel.unary_unary(
                '/modal.client.ModalClient/ClassCreate',
                request_serializer=modal__proto_dot_api__pb2.ClassCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ClassCreateResponse.FromString,
                )
        self.ClassGet = channel.unary_unary(
                '/modal.client.ModalClient/ClassGet',
                request_serializer=modal__proto_dot_api__pb2.ClassGetRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ClassGetResponse.FromString,
                )
        self.ClientHello = channel.unary_unary(
                '/modal.client.ModalClient/ClientHello',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ClientHelloResponse.FromString,
                )
        self.ClusterGet = channel.unary_unary(
                '/modal.client.ModalClient/ClusterGet',
                request_serializer=modal__proto_dot_api__pb2.ClusterGetRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ClusterGetResponse.FromString,
                )
        self.ClusterList = channel.unary_unary(
                '/modal.client.ModalClient/ClusterList',
                request_serializer=modal__proto_dot_api__pb2.ClusterListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ClusterListResponse.FromString,
                )
        self.ContainerCheckpoint = channel.unary_unary(
                '/modal.client.ModalClient/ContainerCheckpoint',
                request_serializer=modal__proto_dot_api__pb2.ContainerCheckpointRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ContainerExec = channel.unary_unary(
                '/modal.client.ModalClient/ContainerExec',
                request_serializer=modal__proto_dot_api__pb2.ContainerExecRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ContainerExecResponse.FromString,
                )
        self.ContainerExecGetOutput = channel.unary_stream(
                '/modal.client.ModalClient/ContainerExecGetOutput',
                request_serializer=modal__proto_dot_api__pb2.ContainerExecGetOutputRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.RuntimeOutputBatch.FromString,
                )
        self.ContainerExecPutInput = channel.unary_unary(
                '/modal.client.ModalClient/ContainerExecPutInput',
                request_serializer=modal__proto_dot_api__pb2.ContainerExecPutInputRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ContainerExecWait = channel.unary_unary(
                '/modal.client.ModalClient/ContainerExecWait',
                request_serializer=modal__proto_dot_api__pb2.ContainerExecWaitRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ContainerExecWaitResponse.FromString,
                )
        self.ContainerFilesystemExec = channel.unary_unary(
                '/modal.client.ModalClient/ContainerFilesystemExec',
                request_serializer=modal__proto_dot_api__pb2.ContainerFilesystemExecRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ContainerFilesystemExecResponse.FromString,
                )
        self.ContainerFilesystemExecGetOutput = channel.unary_stream(
                '/modal.client.ModalClient/ContainerFilesystemExecGetOutput',
                request_serializer=modal__proto_dot_api__pb2.ContainerFilesystemExecGetOutputRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FilesystemRuntimeOutputBatch.FromString,
                )
        self.ContainerHeartbeat = channel.unary_unary(
                '/modal.client.ModalClient/ContainerHeartbeat',
                request_serializer=modal__proto_dot_api__pb2.ContainerHeartbeatRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ContainerHeartbeatResponse.FromString,
                )
        self.ContainerHello = channel.unary_unary(
                '/modal.client.ModalClient/ContainerHello',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ContainerLog = channel.unary_unary(
                '/modal.client.ModalClient/ContainerLog',
                request_serializer=modal__proto_dot_api__pb2.ContainerLogRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ContainerStop = channel.unary_unary(
                '/modal.client.ModalClient/ContainerStop',
                request_serializer=modal__proto_dot_api__pb2.ContainerStopRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ContainerStopResponse.FromString,
                )
        self.DictClear = channel.unary_unary(
                '/modal.client.ModalClient/DictClear',
                request_serializer=modal__proto_dot_api__pb2.DictClearRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.DictContains = channel.unary_unary(
                '/modal.client.ModalClient/DictContains',
                request_serializer=modal__proto_dot_api__pb2.DictContainsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DictContainsResponse.FromString,
                )
        self.DictContents = channel.unary_stream(
                '/modal.client.ModalClient/DictContents',
                request_serializer=modal__proto_dot_api__pb2.DictContentsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DictEntry.FromString,
                )
        self.DictDelete = channel.unary_unary(
                '/modal.client.ModalClient/DictDelete',
                request_serializer=modal__proto_dot_api__pb2.DictDeleteRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.DictGet = channel.unary_unary(
                '/modal.client.ModalClient/DictGet',
                request_serializer=modal__proto_dot_api__pb2.DictGetRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DictGetResponse.FromString,
                )
        self.DictGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/DictGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.DictGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DictGetOrCreateResponse.FromString,
                )
        self.DictHeartbeat = channel.unary_unary(
                '/modal.client.ModalClient/DictHeartbeat',
                request_serializer=modal__proto_dot_api__pb2.DictHeartbeatRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.DictLen = channel.unary_unary(
                '/modal.client.ModalClient/DictLen',
                request_serializer=modal__proto_dot_api__pb2.DictLenRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DictLenResponse.FromString,
                )
        self.DictList = channel.unary_unary(
                '/modal.client.ModalClient/DictList',
                request_serializer=modal__proto_dot_api__pb2.DictListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DictListResponse.FromString,
                )
        self.DictPop = channel.unary_unary(
                '/modal.client.ModalClient/DictPop',
                request_serializer=modal__proto_dot_api__pb2.DictPopRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DictPopResponse.FromString,
                )
        self.DictUpdate = channel.unary_unary(
                '/modal.client.ModalClient/DictUpdate',
                request_serializer=modal__proto_dot_api__pb2.DictUpdateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DictUpdateResponse.FromString,
                )
        self.DomainCertificateVerify = channel.unary_unary(
                '/modal.client.ModalClient/DomainCertificateVerify',
                request_serializer=modal__proto_dot_api__pb2.DomainCertificateVerifyRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DomainCertificateVerifyResponse.FromString,
                )
        self.DomainCreate = channel.unary_unary(
                '/modal.client.ModalClient/DomainCreate',
                request_serializer=modal__proto_dot_api__pb2.DomainCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DomainCreateResponse.FromString,
                )
        self.DomainList = channel.unary_unary(
                '/modal.client.ModalClient/DomainList',
                request_serializer=modal__proto_dot_api__pb2.DomainListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DomainListResponse.FromString,
                )
        self.EnvironmentCreate = channel.unary_unary(
                '/modal.client.ModalClient/EnvironmentCreate',
                request_serializer=modal__proto_dot_api__pb2.EnvironmentCreateRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.EnvironmentDelete = channel.unary_unary(
                '/modal.client.ModalClient/EnvironmentDelete',
                request_serializer=modal__proto_dot_api__pb2.EnvironmentDeleteRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.EnvironmentGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/EnvironmentGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.EnvironmentGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.EnvironmentGetOrCreateResponse.FromString,
                )
        self.EnvironmentList = channel.unary_unary(
                '/modal.client.ModalClient/EnvironmentList',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.EnvironmentListResponse.FromString,
                )
        self.EnvironmentUpdate = channel.unary_unary(
                '/modal.client.ModalClient/EnvironmentUpdate',
                request_serializer=modal__proto_dot_api__pb2.EnvironmentUpdateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.EnvironmentListItem.FromString,
                )
        self.FunctionAsyncInvoke = channel.unary_unary(
                '/modal.client.ModalClient/FunctionAsyncInvoke',
                request_serializer=modal__proto_dot_api__pb2.FunctionAsyncInvokeRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionAsyncInvokeResponse.FromString,
                )
        self.FunctionBindParams = channel.unary_unary(
                '/modal.client.ModalClient/FunctionBindParams',
                request_serializer=modal__proto_dot_api__pb2.FunctionBindParamsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionBindParamsResponse.FromString,
                )
        self.FunctionCallCancel = channel.unary_unary(
                '/modal.client.ModalClient/FunctionCallCancel',
                request_serializer=modal__proto_dot_api__pb2.FunctionCallCancelRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.FunctionCallGetDataIn = channel.unary_stream(
                '/modal.client.ModalClient/FunctionCallGetDataIn',
                request_serializer=modal__proto_dot_api__pb2.FunctionCallGetDataRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DataChunk.FromString,
                )
        self.FunctionCallGetDataOut = channel.unary_stream(
                '/modal.client.ModalClient/FunctionCallGetDataOut',
                request_serializer=modal__proto_dot_api__pb2.FunctionCallGetDataRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.DataChunk.FromString,
                )
        self.FunctionCallList = channel.unary_unary(
                '/modal.client.ModalClient/FunctionCallList',
                request_serializer=modal__proto_dot_api__pb2.FunctionCallListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionCallListResponse.FromString,
                )
        self.FunctionCallPutDataOut = channel.unary_unary(
                '/modal.client.ModalClient/FunctionCallPutDataOut',
                request_serializer=modal__proto_dot_api__pb2.FunctionCallPutDataRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.FunctionCreate = channel.unary_unary(
                '/modal.client.ModalClient/FunctionCreate',
                request_serializer=modal__proto_dot_api__pb2.FunctionCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionCreateResponse.FromString,
                )
        self.FunctionGet = channel.unary_unary(
                '/modal.client.ModalClient/FunctionGet',
                request_serializer=modal__proto_dot_api__pb2.FunctionGetRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionGetResponse.FromString,
                )
        self.FunctionGetCallGraph = channel.unary_unary(
                '/modal.client.ModalClient/FunctionGetCallGraph',
                request_serializer=modal__proto_dot_api__pb2.FunctionGetCallGraphRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionGetCallGraphResponse.FromString,
                )
        self.FunctionGetCurrentStats = channel.unary_unary(
                '/modal.client.ModalClient/FunctionGetCurrentStats',
                request_serializer=modal__proto_dot_api__pb2.FunctionGetCurrentStatsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionStats.FromString,
                )
        self.FunctionGetDynamicConcurrency = channel.unary_unary(
                '/modal.client.ModalClient/FunctionGetDynamicConcurrency',
                request_serializer=modal__proto_dot_api__pb2.FunctionGetDynamicConcurrencyRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionGetDynamicConcurrencyResponse.FromString,
                )
        self.FunctionGetInputs = channel.unary_unary(
                '/modal.client.ModalClient/FunctionGetInputs',
                request_serializer=modal__proto_dot_api__pb2.FunctionGetInputsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionGetInputsResponse.FromString,
                )
        self.FunctionGetOutputs = channel.unary_unary(
                '/modal.client.ModalClient/FunctionGetOutputs',
                request_serializer=modal__proto_dot_api__pb2.FunctionGetOutputsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionGetOutputsResponse.FromString,
                )
        self.FunctionGetSerialized = channel.unary_unary(
                '/modal.client.ModalClient/FunctionGetSerialized',
                request_serializer=modal__proto_dot_api__pb2.FunctionGetSerializedRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionGetSerializedResponse.FromString,
                )
        self.FunctionMap = channel.unary_unary(
                '/modal.client.ModalClient/FunctionMap',
                request_serializer=modal__proto_dot_api__pb2.FunctionMapRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionMapResponse.FromString,
                )
        self.FunctionPrecreate = channel.unary_unary(
                '/modal.client.ModalClient/FunctionPrecreate',
                request_serializer=modal__proto_dot_api__pb2.FunctionPrecreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionPrecreateResponse.FromString,
                )
        self.FunctionPutInputs = channel.unary_unary(
                '/modal.client.ModalClient/FunctionPutInputs',
                request_serializer=modal__proto_dot_api__pb2.FunctionPutInputsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionPutInputsResponse.FromString,
                )
        self.FunctionPutOutputs = channel.unary_unary(
                '/modal.client.ModalClient/FunctionPutOutputs',
                request_serializer=modal__proto_dot_api__pb2.FunctionPutOutputsRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.FunctionRetryInputs = channel.unary_unary(
                '/modal.client.ModalClient/FunctionRetryInputs',
                request_serializer=modal__proto_dot_api__pb2.FunctionRetryInputsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionRetryInputsResponse.FromString,
                )
        self.FunctionStartPtyShell = channel.unary_unary(
                '/modal.client.ModalClient/FunctionStartPtyShell',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.FunctionUpdateSchedulingParams = channel.unary_unary(
                '/modal.client.ModalClient/FunctionUpdateSchedulingParams',
                request_serializer=modal__proto_dot_api__pb2.FunctionUpdateSchedulingParamsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.FunctionUpdateSchedulingParamsResponse.FromString,
                )
        self.ImageFromId = channel.unary_unary(
                '/modal.client.ModalClient/ImageFromId',
                request_serializer=modal__proto_dot_api__pb2.ImageFromIdRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ImageFromIdResponse.FromString,
                )
        self.ImageGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/ImageGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.ImageGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ImageGetOrCreateResponse.FromString,
                )
        self.ImageJoinStreaming = channel.unary_stream(
                '/modal.client.ModalClient/ImageJoinStreaming',
                request_serializer=modal__proto_dot_api__pb2.ImageJoinStreamingRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ImageJoinStreamingResponse.FromString,
                )
        self.MountGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/MountGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.MountGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.MountGetOrCreateResponse.FromString,
                )
        self.MountPutFile = channel.unary_unary(
                '/modal.client.ModalClient/MountPutFile',
                request_serializer=modal__proto_dot_api__pb2.MountPutFileRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.MountPutFileResponse.FromString,
                )
        self.NotebookKernelPublishResults = channel.unary_unary(
                '/modal.client.ModalClient/NotebookKernelPublishResults',
                request_serializer=modal__proto_dot_api__pb2.NotebookKernelPublishResultsRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ProxyAddIp = channel.unary_unary(
                '/modal.client.ModalClient/ProxyAddIp',
                request_serializer=modal__proto_dot_api__pb2.ProxyAddIpRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ProxyAddIpResponse.FromString,
                )
        self.ProxyCreate = channel.unary_unary(
                '/modal.client.ModalClient/ProxyCreate',
                request_serializer=modal__proto_dot_api__pb2.ProxyCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ProxyCreateResponse.FromString,
                )
        self.ProxyDelete = channel.unary_unary(
                '/modal.client.ModalClient/ProxyDelete',
                request_serializer=modal__proto_dot_api__pb2.ProxyDeleteRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.ProxyGet = channel.unary_unary(
                '/modal.client.ModalClient/ProxyGet',
                request_serializer=modal__proto_dot_api__pb2.ProxyGetRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ProxyGetResponse.FromString,
                )
        self.ProxyGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/ProxyGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.ProxyGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ProxyGetOrCreateResponse.FromString,
                )
        self.ProxyList = channel.unary_unary(
                '/modal.client.ModalClient/ProxyList',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.ProxyListResponse.FromString,
                )
        self.ProxyRemoveIp = channel.unary_unary(
                '/modal.client.ModalClient/ProxyRemoveIp',
                request_serializer=modal__proto_dot_api__pb2.ProxyRemoveIpRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.QueueClear = channel.unary_unary(
                '/modal.client.ModalClient/QueueClear',
                request_serializer=modal__proto_dot_api__pb2.QueueClearRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.QueueDelete = channel.unary_unary(
                '/modal.client.ModalClient/QueueDelete',
                request_serializer=modal__proto_dot_api__pb2.QueueDeleteRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.QueueGet = channel.unary_unary(
                '/modal.client.ModalClient/QueueGet',
                request_serializer=modal__proto_dot_api__pb2.QueueGetRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.QueueGetResponse.FromString,
                )
        self.QueueGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/QueueGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.QueueGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.QueueGetOrCreateResponse.FromString,
                )
        self.QueueHeartbeat = channel.unary_unary(
                '/modal.client.ModalClient/QueueHeartbeat',
                request_serializer=modal__proto_dot_api__pb2.QueueHeartbeatRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.QueueLen = channel.unary_unary(
                '/modal.client.ModalClient/QueueLen',
                request_serializer=modal__proto_dot_api__pb2.QueueLenRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.QueueLenResponse.FromString,
                )
        self.QueueList = channel.unary_unary(
                '/modal.client.ModalClient/QueueList',
                request_serializer=modal__proto_dot_api__pb2.QueueListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.QueueListResponse.FromString,
                )
        self.QueueNextItems = channel.unary_unary(
                '/modal.client.ModalClient/QueueNextItems',
                request_serializer=modal__proto_dot_api__pb2.QueueNextItemsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.QueueNextItemsResponse.FromString,
                )
        self.QueuePut = channel.unary_unary(
                '/modal.client.ModalClient/QueuePut',
                request_serializer=modal__proto_dot_api__pb2.QueuePutRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.SandboxCreate = channel.unary_unary(
                '/modal.client.ModalClient/SandboxCreate',
                request_serializer=modal__proto_dot_api__pb2.SandboxCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxCreateResponse.FromString,
                )
        self.SandboxGetLogs = channel.unary_stream(
                '/modal.client.ModalClient/SandboxGetLogs',
                request_serializer=modal__proto_dot_api__pb2.SandboxGetLogsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TaskLogsBatch.FromString,
                )
        self.SandboxGetResourceUsage = channel.unary_unary(
                '/modal.client.ModalClient/SandboxGetResourceUsage',
                request_serializer=modal__proto_dot_api__pb2.SandboxGetResourceUsageRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxGetResourceUsageResponse.FromString,
                )
        self.SandboxGetTaskId = channel.unary_unary(
                '/modal.client.ModalClient/SandboxGetTaskId',
                request_serializer=modal__proto_dot_api__pb2.SandboxGetTaskIdRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxGetTaskIdResponse.FromString,
                )
        self.SandboxGetTunnels = channel.unary_unary(
                '/modal.client.ModalClient/SandboxGetTunnels',
                request_serializer=modal__proto_dot_api__pb2.SandboxGetTunnelsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxGetTunnelsResponse.FromString,
                )
        self.SandboxList = channel.unary_unary(
                '/modal.client.ModalClient/SandboxList',
                request_serializer=modal__proto_dot_api__pb2.SandboxListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxListResponse.FromString,
                )
        self.SandboxRestore = channel.unary_unary(
                '/modal.client.ModalClient/SandboxRestore',
                request_serializer=modal__proto_dot_api__pb2.SandboxRestoreRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxRestoreResponse.FromString,
                )
        self.SandboxSnapshot = channel.unary_unary(
                '/modal.client.ModalClient/SandboxSnapshot',
                request_serializer=modal__proto_dot_api__pb2.SandboxSnapshotRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxSnapshotResponse.FromString,
                )
        self.SandboxSnapshotFs = channel.unary_unary(
                '/modal.client.ModalClient/SandboxSnapshotFs',
                request_serializer=modal__proto_dot_api__pb2.SandboxSnapshotFsRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxSnapshotFsResponse.FromString,
                )
        self.SandboxSnapshotGet = channel.unary_unary(
                '/modal.client.ModalClient/SandboxSnapshotGet',
                request_serializer=modal__proto_dot_api__pb2.SandboxSnapshotGetRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxSnapshotGetResponse.FromString,
                )
        self.SandboxSnapshotWait = channel.unary_unary(
                '/modal.client.ModalClient/SandboxSnapshotWait',
                request_serializer=modal__proto_dot_api__pb2.SandboxSnapshotWaitRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxSnapshotWaitResponse.FromString,
                )
        self.SandboxStdinWrite = channel.unary_unary(
                '/modal.client.ModalClient/SandboxStdinWrite',
                request_serializer=modal__proto_dot_api__pb2.SandboxStdinWriteRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxStdinWriteResponse.FromString,
                )
        self.SandboxTagsSet = channel.unary_unary(
                '/modal.client.ModalClient/SandboxTagsSet',
                request_serializer=modal__proto_dot_api__pb2.SandboxTagsSetRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.SandboxTerminate = channel.unary_unary(
                '/modal.client.ModalClient/SandboxTerminate',
                request_serializer=modal__proto_dot_api__pb2.SandboxTerminateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxTerminateResponse.FromString,
                )
        self.SandboxWait = channel.unary_unary(
                '/modal.client.ModalClient/SandboxWait',
                request_serializer=modal__proto_dot_api__pb2.SandboxWaitRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SandboxWaitResponse.FromString,
                )
        self.SecretDelete = channel.unary_unary(
                '/modal.client.ModalClient/SecretDelete',
                request_serializer=modal__proto_dot_api__pb2.SecretDeleteRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.SecretGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/SecretGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.SecretGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SecretGetOrCreateResponse.FromString,
                )
        self.SecretList = channel.unary_unary(
                '/modal.client.ModalClient/SecretList',
                request_serializer=modal__proto_dot_api__pb2.SecretListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SecretListResponse.FromString,
                )
        self.SharedVolumeDelete = channel.unary_unary(
                '/modal.client.ModalClient/SharedVolumeDelete',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumeDeleteRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.SharedVolumeGetFile = channel.unary_unary(
                '/modal.client.ModalClient/SharedVolumeGetFile',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumeGetFileRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SharedVolumeGetFileResponse.FromString,
                )
        self.SharedVolumeGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/SharedVolumeGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumeGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SharedVolumeGetOrCreateResponse.FromString,
                )
        self.SharedVolumeHeartbeat = channel.unary_unary(
                '/modal.client.ModalClient/SharedVolumeHeartbeat',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumeHeartbeatRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.SharedVolumeList = channel.unary_unary(
                '/modal.client.ModalClient/SharedVolumeList',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumeListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SharedVolumeListResponse.FromString,
                )
        self.SharedVolumeListFiles = channel.unary_unary(
                '/modal.client.ModalClient/SharedVolumeListFiles',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumeListFilesRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SharedVolumeListFilesResponse.FromString,
                )
        self.SharedVolumeListFilesStream = channel.unary_stream(
                '/modal.client.ModalClient/SharedVolumeListFilesStream',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumeListFilesRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SharedVolumeListFilesResponse.FromString,
                )
        self.SharedVolumePutFile = channel.unary_unary(
                '/modal.client.ModalClient/SharedVolumePutFile',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumePutFileRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.SharedVolumePutFileResponse.FromString,
                )
        self.SharedVolumeRemoveFile = channel.unary_unary(
                '/modal.client.ModalClient/SharedVolumeRemoveFile',
                request_serializer=modal__proto_dot_api__pb2.SharedVolumeRemoveFileRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.TaskClusterHello = channel.unary_unary(
                '/modal.client.ModalClient/TaskClusterHello',
                request_serializer=modal__proto_dot_api__pb2.TaskClusterHelloRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TaskClusterHelloResponse.FromString,
                )
        self.TaskCurrentInputs = channel.unary_unary(
                '/modal.client.ModalClient/TaskCurrentInputs',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TaskCurrentInputsResponse.FromString,
                )
        self.TaskList = channel.unary_unary(
                '/modal.client.ModalClient/TaskList',
                request_serializer=modal__proto_dot_api__pb2.TaskListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TaskListResponse.FromString,
                )
        self.TaskResult = channel.unary_unary(
                '/modal.client.ModalClient/TaskResult',
                request_serializer=modal__proto_dot_api__pb2.TaskResultRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.TokenFlowCreate = channel.unary_unary(
                '/modal.client.ModalClient/TokenFlowCreate',
                request_serializer=modal__proto_dot_api__pb2.TokenFlowCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TokenFlowCreateResponse.FromString,
                )
        self.TokenFlowWait = channel.unary_unary(
                '/modal.client.ModalClient/TokenFlowWait',
                request_serializer=modal__proto_dot_api__pb2.TokenFlowWaitRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TokenFlowWaitResponse.FromString,
                )
        self.TunnelStart = channel.unary_unary(
                '/modal.client.ModalClient/TunnelStart',
                request_serializer=modal__proto_dot_api__pb2.TunnelStartRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TunnelStartResponse.FromString,
                )
        self.TunnelStop = channel.unary_unary(
                '/modal.client.ModalClient/TunnelStop',
                request_serializer=modal__proto_dot_api__pb2.TunnelStopRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.TunnelStopResponse.FromString,
                )
        self.VolumeCommit = channel.unary_unary(
                '/modal.client.ModalClient/VolumeCommit',
                request_serializer=modal__proto_dot_api__pb2.VolumeCommitRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.VolumeCommitResponse.FromString,
                )
        self.VolumeCopyFiles = channel.unary_unary(
                '/modal.client.ModalClient/VolumeCopyFiles',
                request_serializer=modal__proto_dot_api__pb2.VolumeCopyFilesRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.VolumeCopyFiles2 = channel.unary_unary(
                '/modal.client.ModalClient/VolumeCopyFiles2',
                request_serializer=modal__proto_dot_api__pb2.VolumeCopyFiles2Request.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.VolumeDelete = channel.unary_unary(
                '/modal.client.ModalClient/VolumeDelete',
                request_serializer=modal__proto_dot_api__pb2.VolumeDeleteRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.VolumeGetFile = channel.unary_unary(
                '/modal.client.ModalClient/VolumeGetFile',
                request_serializer=modal__proto_dot_api__pb2.VolumeGetFileRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.VolumeGetFileResponse.FromString,
                )
        self.VolumeGetFile2 = channel.unary_unary(
                '/modal.client.ModalClient/VolumeGetFile2',
                request_serializer=modal__proto_dot_api__pb2.VolumeGetFile2Request.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.VolumeGetFile2Response.FromString,
                )
        self.VolumeGetOrCreate = channel.unary_unary(
                '/modal.client.ModalClient/VolumeGetOrCreate',
                request_serializer=modal__proto_dot_api__pb2.VolumeGetOrCreateRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.VolumeGetOrCreateResponse.FromString,
                )
        self.VolumeHeartbeat = channel.unary_unary(
                '/modal.client.ModalClient/VolumeHeartbeat',
                request_serializer=modal__proto_dot_api__pb2.VolumeHeartbeatRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.VolumeList = channel.unary_unary(
                '/modal.client.ModalClient/VolumeList',
                request_serializer=modal__proto_dot_api__pb2.VolumeListRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.VolumeListResponse.FromString,
                )
        self.VolumeListFiles = channel.unary_stream(
                '/modal.client.ModalClient/VolumeListFiles',
                request_serializer=modal__proto_dot_api__pb2.VolumeListFilesRequest.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.VolumeListFilesResponse.FromString,
                )
        self.VolumeListFiles2 = channel.unary_stream(
                '/modal.client.ModalClient/VolumeListFiles2',
                request_serializer=modal__proto_dot_api__pb2.VolumeListFiles2Request.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.VolumeListFiles2Response.FromString,
                )
        self.VolumePutFiles = channel.unary_unary(
                '/modal.client.ModalClient/VolumePutFiles',
                request_serializer=modal__proto_dot_api__pb2.VolumePutFilesRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.VolumePutFiles2 = channel.unary_unary(
                '/modal.client.ModalClient/VolumePutFiles2',
                request_serializer=modal__proto_dot_api__pb2.VolumePutFiles2Request.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.VolumePutFiles2Response.FromString,
                )
        self.VolumeReload = channel.unary_unary(
                '/modal.client.ModalClient/VolumeReload',
                request_serializer=modal__proto_dot_api__pb2.VolumeReloadRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.VolumeRemoveFile = channel.unary_unary(
                '/modal.client.ModalClient/VolumeRemoveFile',
                request_serializer=modal__proto_dot_api__pb2.VolumeRemoveFileRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.VolumeRemoveFile2 = channel.unary_unary(
                '/modal.client.ModalClient/VolumeRemoveFile2',
                request_serializer=modal__proto_dot_api__pb2.VolumeRemoveFile2Request.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.VolumeRename = channel.unary_unary(
                '/modal.client.ModalClient/VolumeRename',
                request_serializer=modal__proto_dot_api__pb2.VolumeRenameRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                )
        self.WorkspaceNameLookup = channel.unary_unary(
                '/modal.client.ModalClient/WorkspaceNameLookup',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=modal__proto_dot_api__pb2.WorkspaceNameLookupResponse.FromString,
                )


class ModalClientServicer(object):
    """Missing associated documentation comment in .proto file."""

    def AppClientDisconnect(self, request, context):
        """Apps
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppDeploy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppDeploymentHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppGetByDeploymentName(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppGetLayout(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppGetLogs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppGetObjects(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppHeartbeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppLookup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppPublish(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppRollback(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppSetObjects(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AppStop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AttemptAwait(self, request, context):
        """Input Plane
        These RPCs are experimental, not deployed to production, and can be changed / removed
        without needing to worry about backwards compatibility.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AttemptRetry(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AttemptStart(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BlobCreate(self, request, context):
        """Blobs
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BlobGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClassCreate(self, request, context):
        """Classes
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClassGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClientHello(self, request, context):
        """Clients
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClusterGet(self, request, context):
        """Clusters
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClusterList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerCheckpoint(self, request, context):
        """Container
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerExec(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerExecGetOutput(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerExecPutInput(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerExecWait(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerFilesystemExec(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerFilesystemExecGetOutput(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerHeartbeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerHello(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerLog(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ContainerStop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictClear(self, request, context):
        """Dicts
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictContains(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictContents(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictDelete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictHeartbeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictLen(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictPop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DictUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DomainCertificateVerify(self, request, context):
        """Domains
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DomainCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DomainList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnvironmentCreate(self, request, context):
        """Environments
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnvironmentDelete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnvironmentGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnvironmentList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EnvironmentUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionAsyncInvoke(self, request, context):
        """Functions
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionBindParams(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionCallCancel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionCallGetDataIn(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionCallGetDataOut(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionCallList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionCallPutDataOut(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionGetCallGraph(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionGetCurrentStats(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionGetDynamicConcurrency(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionGetInputs(self, request, context):
        """For containers to request next call
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionGetOutputs(self, request, context):
        """Returns the next result(s) for an entire function call (FunctionMap)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionGetSerialized(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionMap(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionPrecreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionPutInputs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionPutOutputs(self, request, context):
        """For containers to return result
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionRetryInputs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionStartPtyShell(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FunctionUpdateSchedulingParams(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ImageFromId(self, request, context):
        """Images
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ImageGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ImageJoinStreaming(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MountGetOrCreate(self, request, context):
        """Mounts
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MountPutFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def NotebookKernelPublishResults(self, request, context):
        """Notebooks
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ProxyAddIp(self, request, context):
        """Proxies
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ProxyCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ProxyDelete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ProxyGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ProxyGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ProxyList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ProxyRemoveIp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueueClear(self, request, context):
        """Queues
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueueDelete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueueGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueueGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueueHeartbeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueueLen(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueueList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueueNextItems(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueuePut(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxCreate(self, request, context):
        """Sandboxes
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxGetLogs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxGetResourceUsage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxGetTaskId(self, request, context):
        """needed for modal container exec
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxGetTunnels(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxRestore(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxSnapshot(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxSnapshotFs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxSnapshotGet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxSnapshotWait(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxStdinWrite(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxTagsSet(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxTerminate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SandboxWait(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SecretDelete(self, request, context):
        """Secrets
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SecretGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SecretList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumeDelete(self, request, context):
        """SharedVolumes
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumeGetFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumeGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumeHeartbeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumeList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumeListFiles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumeListFilesStream(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumePutFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SharedVolumeRemoveFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TaskClusterHello(self, request, context):
        """Tasks
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TaskCurrentInputs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TaskList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TaskResult(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TokenFlowCreate(self, request, context):
        """Tokens (web auth flow)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TokenFlowWait(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TunnelStart(self, request, context):
        """Tunnels
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TunnelStop(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeCommit(self, request, context):
        """Volumes
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeCopyFiles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeCopyFiles2(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeDelete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeGetFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeGetFile2(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeGetOrCreate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeHeartbeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeListFiles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeListFiles2(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumePutFiles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumePutFiles2(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeReload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeRemoveFile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeRemoveFile2(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VolumeRename(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def WorkspaceNameLookup(self, request, context):
        """Workspaces
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModalClientServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'AppClientDisconnect': grpc.unary_unary_rpc_method_handler(
                    servicer.AppClientDisconnect,
                    request_deserializer=modal__proto_dot_api__pb2.AppClientDisconnectRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'AppCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.AppCreate,
                    request_deserializer=modal__proto_dot_api__pb2.AppCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppCreateResponse.SerializeToString,
            ),
            'AppDeploy': grpc.unary_unary_rpc_method_handler(
                    servicer.AppDeploy,
                    request_deserializer=modal__proto_dot_api__pb2.AppDeployRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppDeployResponse.SerializeToString,
            ),
            'AppDeploymentHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.AppDeploymentHistory,
                    request_deserializer=modal__proto_dot_api__pb2.AppDeploymentHistoryRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppDeploymentHistoryResponse.SerializeToString,
            ),
            'AppGetByDeploymentName': grpc.unary_unary_rpc_method_handler(
                    servicer.AppGetByDeploymentName,
                    request_deserializer=modal__proto_dot_api__pb2.AppGetByDeploymentNameRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppGetByDeploymentNameResponse.SerializeToString,
            ),
            'AppGetLayout': grpc.unary_unary_rpc_method_handler(
                    servicer.AppGetLayout,
                    request_deserializer=modal__proto_dot_api__pb2.AppGetLayoutRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppGetLayoutResponse.SerializeToString,
            ),
            'AppGetLogs': grpc.unary_stream_rpc_method_handler(
                    servicer.AppGetLogs,
                    request_deserializer=modal__proto_dot_api__pb2.AppGetLogsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TaskLogsBatch.SerializeToString,
            ),
            'AppGetObjects': grpc.unary_unary_rpc_method_handler(
                    servicer.AppGetObjects,
                    request_deserializer=modal__proto_dot_api__pb2.AppGetObjectsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppGetObjectsResponse.SerializeToString,
            ),
            'AppGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.AppGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.AppGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppGetOrCreateResponse.SerializeToString,
            ),
            'AppHeartbeat': grpc.unary_unary_rpc_method_handler(
                    servicer.AppHeartbeat,
                    request_deserializer=modal__proto_dot_api__pb2.AppHeartbeatRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'AppList': grpc.unary_unary_rpc_method_handler(
                    servicer.AppList,
                    request_deserializer=modal__proto_dot_api__pb2.AppListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppListResponse.SerializeToString,
            ),
            'AppLookup': grpc.unary_unary_rpc_method_handler(
                    servicer.AppLookup,
                    request_deserializer=modal__proto_dot_api__pb2.AppLookupRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppLookupResponse.SerializeToString,
            ),
            'AppPublish': grpc.unary_unary_rpc_method_handler(
                    servicer.AppPublish,
                    request_deserializer=modal__proto_dot_api__pb2.AppPublishRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AppPublishResponse.SerializeToString,
            ),
            'AppRollback': grpc.unary_unary_rpc_method_handler(
                    servicer.AppRollback,
                    request_deserializer=modal__proto_dot_api__pb2.AppRollbackRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'AppSetObjects': grpc.unary_unary_rpc_method_handler(
                    servicer.AppSetObjects,
                    request_deserializer=modal__proto_dot_api__pb2.AppSetObjectsRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'AppStop': grpc.unary_unary_rpc_method_handler(
                    servicer.AppStop,
                    request_deserializer=modal__proto_dot_api__pb2.AppStopRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'AttemptAwait': grpc.unary_unary_rpc_method_handler(
                    servicer.AttemptAwait,
                    request_deserializer=modal__proto_dot_api__pb2.AttemptAwaitRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AttemptAwaitResponse.SerializeToString,
            ),
            'AttemptRetry': grpc.unary_unary_rpc_method_handler(
                    servicer.AttemptRetry,
                    request_deserializer=modal__proto_dot_api__pb2.AttemptRetryRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AttemptRetryResponse.SerializeToString,
            ),
            'AttemptStart': grpc.unary_unary_rpc_method_handler(
                    servicer.AttemptStart,
                    request_deserializer=modal__proto_dot_api__pb2.AttemptStartRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.AttemptStartResponse.SerializeToString,
            ),
            'BlobCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.BlobCreate,
                    request_deserializer=modal__proto_dot_api__pb2.BlobCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.BlobCreateResponse.SerializeToString,
            ),
            'BlobGet': grpc.unary_unary_rpc_method_handler(
                    servicer.BlobGet,
                    request_deserializer=modal__proto_dot_api__pb2.BlobGetRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.BlobGetResponse.SerializeToString,
            ),
            'ClassCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.ClassCreate,
                    request_deserializer=modal__proto_dot_api__pb2.ClassCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ClassCreateResponse.SerializeToString,
            ),
            'ClassGet': grpc.unary_unary_rpc_method_handler(
                    servicer.ClassGet,
                    request_deserializer=modal__proto_dot_api__pb2.ClassGetRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ClassGetResponse.SerializeToString,
            ),
            'ClientHello': grpc.unary_unary_rpc_method_handler(
                    servicer.ClientHello,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ClientHelloResponse.SerializeToString,
            ),
            'ClusterGet': grpc.unary_unary_rpc_method_handler(
                    servicer.ClusterGet,
                    request_deserializer=modal__proto_dot_api__pb2.ClusterGetRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ClusterGetResponse.SerializeToString,
            ),
            'ClusterList': grpc.unary_unary_rpc_method_handler(
                    servicer.ClusterList,
                    request_deserializer=modal__proto_dot_api__pb2.ClusterListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ClusterListResponse.SerializeToString,
            ),
            'ContainerCheckpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerCheckpoint,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerCheckpointRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ContainerExec': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerExec,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerExecRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ContainerExecResponse.SerializeToString,
            ),
            'ContainerExecGetOutput': grpc.unary_stream_rpc_method_handler(
                    servicer.ContainerExecGetOutput,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerExecGetOutputRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.RuntimeOutputBatch.SerializeToString,
            ),
            'ContainerExecPutInput': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerExecPutInput,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerExecPutInputRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ContainerExecWait': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerExecWait,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerExecWaitRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ContainerExecWaitResponse.SerializeToString,
            ),
            'ContainerFilesystemExec': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerFilesystemExec,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerFilesystemExecRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ContainerFilesystemExecResponse.SerializeToString,
            ),
            'ContainerFilesystemExecGetOutput': grpc.unary_stream_rpc_method_handler(
                    servicer.ContainerFilesystemExecGetOutput,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerFilesystemExecGetOutputRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FilesystemRuntimeOutputBatch.SerializeToString,
            ),
            'ContainerHeartbeat': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerHeartbeat,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerHeartbeatRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ContainerHeartbeatResponse.SerializeToString,
            ),
            'ContainerHello': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerHello,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ContainerLog': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerLog,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerLogRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ContainerStop': grpc.unary_unary_rpc_method_handler(
                    servicer.ContainerStop,
                    request_deserializer=modal__proto_dot_api__pb2.ContainerStopRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ContainerStopResponse.SerializeToString,
            ),
            'DictClear': grpc.unary_unary_rpc_method_handler(
                    servicer.DictClear,
                    request_deserializer=modal__proto_dot_api__pb2.DictClearRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'DictContains': grpc.unary_unary_rpc_method_handler(
                    servicer.DictContains,
                    request_deserializer=modal__proto_dot_api__pb2.DictContainsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DictContainsResponse.SerializeToString,
            ),
            'DictContents': grpc.unary_stream_rpc_method_handler(
                    servicer.DictContents,
                    request_deserializer=modal__proto_dot_api__pb2.DictContentsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DictEntry.SerializeToString,
            ),
            'DictDelete': grpc.unary_unary_rpc_method_handler(
                    servicer.DictDelete,
                    request_deserializer=modal__proto_dot_api__pb2.DictDeleteRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'DictGet': grpc.unary_unary_rpc_method_handler(
                    servicer.DictGet,
                    request_deserializer=modal__proto_dot_api__pb2.DictGetRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DictGetResponse.SerializeToString,
            ),
            'DictGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.DictGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.DictGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DictGetOrCreateResponse.SerializeToString,
            ),
            'DictHeartbeat': grpc.unary_unary_rpc_method_handler(
                    servicer.DictHeartbeat,
                    request_deserializer=modal__proto_dot_api__pb2.DictHeartbeatRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'DictLen': grpc.unary_unary_rpc_method_handler(
                    servicer.DictLen,
                    request_deserializer=modal__proto_dot_api__pb2.DictLenRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DictLenResponse.SerializeToString,
            ),
            'DictList': grpc.unary_unary_rpc_method_handler(
                    servicer.DictList,
                    request_deserializer=modal__proto_dot_api__pb2.DictListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DictListResponse.SerializeToString,
            ),
            'DictPop': grpc.unary_unary_rpc_method_handler(
                    servicer.DictPop,
                    request_deserializer=modal__proto_dot_api__pb2.DictPopRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DictPopResponse.SerializeToString,
            ),
            'DictUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.DictUpdate,
                    request_deserializer=modal__proto_dot_api__pb2.DictUpdateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DictUpdateResponse.SerializeToString,
            ),
            'DomainCertificateVerify': grpc.unary_unary_rpc_method_handler(
                    servicer.DomainCertificateVerify,
                    request_deserializer=modal__proto_dot_api__pb2.DomainCertificateVerifyRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DomainCertificateVerifyResponse.SerializeToString,
            ),
            'DomainCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.DomainCreate,
                    request_deserializer=modal__proto_dot_api__pb2.DomainCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DomainCreateResponse.SerializeToString,
            ),
            'DomainList': grpc.unary_unary_rpc_method_handler(
                    servicer.DomainList,
                    request_deserializer=modal__proto_dot_api__pb2.DomainListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DomainListResponse.SerializeToString,
            ),
            'EnvironmentCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.EnvironmentCreate,
                    request_deserializer=modal__proto_dot_api__pb2.EnvironmentCreateRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'EnvironmentDelete': grpc.unary_unary_rpc_method_handler(
                    servicer.EnvironmentDelete,
                    request_deserializer=modal__proto_dot_api__pb2.EnvironmentDeleteRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'EnvironmentGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.EnvironmentGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.EnvironmentGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.EnvironmentGetOrCreateResponse.SerializeToString,
            ),
            'EnvironmentList': grpc.unary_unary_rpc_method_handler(
                    servicer.EnvironmentList,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=modal__proto_dot_api__pb2.EnvironmentListResponse.SerializeToString,
            ),
            'EnvironmentUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.EnvironmentUpdate,
                    request_deserializer=modal__proto_dot_api__pb2.EnvironmentUpdateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.EnvironmentListItem.SerializeToString,
            ),
            'FunctionAsyncInvoke': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionAsyncInvoke,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionAsyncInvokeRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionAsyncInvokeResponse.SerializeToString,
            ),
            'FunctionBindParams': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionBindParams,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionBindParamsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionBindParamsResponse.SerializeToString,
            ),
            'FunctionCallCancel': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionCallCancel,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionCallCancelRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'FunctionCallGetDataIn': grpc.unary_stream_rpc_method_handler(
                    servicer.FunctionCallGetDataIn,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionCallGetDataRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DataChunk.SerializeToString,
            ),
            'FunctionCallGetDataOut': grpc.unary_stream_rpc_method_handler(
                    servicer.FunctionCallGetDataOut,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionCallGetDataRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.DataChunk.SerializeToString,
            ),
            'FunctionCallList': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionCallList,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionCallListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionCallListResponse.SerializeToString,
            ),
            'FunctionCallPutDataOut': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionCallPutDataOut,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionCallPutDataRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'FunctionCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionCreate,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionCreateResponse.SerializeToString,
            ),
            'FunctionGet': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionGet,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionGetRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionGetResponse.SerializeToString,
            ),
            'FunctionGetCallGraph': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionGetCallGraph,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionGetCallGraphRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionGetCallGraphResponse.SerializeToString,
            ),
            'FunctionGetCurrentStats': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionGetCurrentStats,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionGetCurrentStatsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionStats.SerializeToString,
            ),
            'FunctionGetDynamicConcurrency': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionGetDynamicConcurrency,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionGetDynamicConcurrencyRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionGetDynamicConcurrencyResponse.SerializeToString,
            ),
            'FunctionGetInputs': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionGetInputs,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionGetInputsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionGetInputsResponse.SerializeToString,
            ),
            'FunctionGetOutputs': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionGetOutputs,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionGetOutputsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionGetOutputsResponse.SerializeToString,
            ),
            'FunctionGetSerialized': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionGetSerialized,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionGetSerializedRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionGetSerializedResponse.SerializeToString,
            ),
            'FunctionMap': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionMap,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionMapRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionMapResponse.SerializeToString,
            ),
            'FunctionPrecreate': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionPrecreate,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionPrecreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionPrecreateResponse.SerializeToString,
            ),
            'FunctionPutInputs': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionPutInputs,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionPutInputsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionPutInputsResponse.SerializeToString,
            ),
            'FunctionPutOutputs': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionPutOutputs,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionPutOutputsRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'FunctionRetryInputs': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionRetryInputs,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionRetryInputsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionRetryInputsResponse.SerializeToString,
            ),
            'FunctionStartPtyShell': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionStartPtyShell,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'FunctionUpdateSchedulingParams': grpc.unary_unary_rpc_method_handler(
                    servicer.FunctionUpdateSchedulingParams,
                    request_deserializer=modal__proto_dot_api__pb2.FunctionUpdateSchedulingParamsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.FunctionUpdateSchedulingParamsResponse.SerializeToString,
            ),
            'ImageFromId': grpc.unary_unary_rpc_method_handler(
                    servicer.ImageFromId,
                    request_deserializer=modal__proto_dot_api__pb2.ImageFromIdRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ImageFromIdResponse.SerializeToString,
            ),
            'ImageGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.ImageGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.ImageGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ImageGetOrCreateResponse.SerializeToString,
            ),
            'ImageJoinStreaming': grpc.unary_stream_rpc_method_handler(
                    servicer.ImageJoinStreaming,
                    request_deserializer=modal__proto_dot_api__pb2.ImageJoinStreamingRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ImageJoinStreamingResponse.SerializeToString,
            ),
            'MountGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.MountGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.MountGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.MountGetOrCreateResponse.SerializeToString,
            ),
            'MountPutFile': grpc.unary_unary_rpc_method_handler(
                    servicer.MountPutFile,
                    request_deserializer=modal__proto_dot_api__pb2.MountPutFileRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.MountPutFileResponse.SerializeToString,
            ),
            'NotebookKernelPublishResults': grpc.unary_unary_rpc_method_handler(
                    servicer.NotebookKernelPublishResults,
                    request_deserializer=modal__proto_dot_api__pb2.NotebookKernelPublishResultsRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ProxyAddIp': grpc.unary_unary_rpc_method_handler(
                    servicer.ProxyAddIp,
                    request_deserializer=modal__proto_dot_api__pb2.ProxyAddIpRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ProxyAddIpResponse.SerializeToString,
            ),
            'ProxyCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.ProxyCreate,
                    request_deserializer=modal__proto_dot_api__pb2.ProxyCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ProxyCreateResponse.SerializeToString,
            ),
            'ProxyDelete': grpc.unary_unary_rpc_method_handler(
                    servicer.ProxyDelete,
                    request_deserializer=modal__proto_dot_api__pb2.ProxyDeleteRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ProxyGet': grpc.unary_unary_rpc_method_handler(
                    servicer.ProxyGet,
                    request_deserializer=modal__proto_dot_api__pb2.ProxyGetRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ProxyGetResponse.SerializeToString,
            ),
            'ProxyGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.ProxyGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.ProxyGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ProxyGetOrCreateResponse.SerializeToString,
            ),
            'ProxyList': grpc.unary_unary_rpc_method_handler(
                    servicer.ProxyList,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=modal__proto_dot_api__pb2.ProxyListResponse.SerializeToString,
            ),
            'ProxyRemoveIp': grpc.unary_unary_rpc_method_handler(
                    servicer.ProxyRemoveIp,
                    request_deserializer=modal__proto_dot_api__pb2.ProxyRemoveIpRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'QueueClear': grpc.unary_unary_rpc_method_handler(
                    servicer.QueueClear,
                    request_deserializer=modal__proto_dot_api__pb2.QueueClearRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'QueueDelete': grpc.unary_unary_rpc_method_handler(
                    servicer.QueueDelete,
                    request_deserializer=modal__proto_dot_api__pb2.QueueDeleteRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'QueueGet': grpc.unary_unary_rpc_method_handler(
                    servicer.QueueGet,
                    request_deserializer=modal__proto_dot_api__pb2.QueueGetRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.QueueGetResponse.SerializeToString,
            ),
            'QueueGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.QueueGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.QueueGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.QueueGetOrCreateResponse.SerializeToString,
            ),
            'QueueHeartbeat': grpc.unary_unary_rpc_method_handler(
                    servicer.QueueHeartbeat,
                    request_deserializer=modal__proto_dot_api__pb2.QueueHeartbeatRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'QueueLen': grpc.unary_unary_rpc_method_handler(
                    servicer.QueueLen,
                    request_deserializer=modal__proto_dot_api__pb2.QueueLenRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.QueueLenResponse.SerializeToString,
            ),
            'QueueList': grpc.unary_unary_rpc_method_handler(
                    servicer.QueueList,
                    request_deserializer=modal__proto_dot_api__pb2.QueueListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.QueueListResponse.SerializeToString,
            ),
            'QueueNextItems': grpc.unary_unary_rpc_method_handler(
                    servicer.QueueNextItems,
                    request_deserializer=modal__proto_dot_api__pb2.QueueNextItemsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.QueueNextItemsResponse.SerializeToString,
            ),
            'QueuePut': grpc.unary_unary_rpc_method_handler(
                    servicer.QueuePut,
                    request_deserializer=modal__proto_dot_api__pb2.QueuePutRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'SandboxCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxCreate,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxCreateResponse.SerializeToString,
            ),
            'SandboxGetLogs': grpc.unary_stream_rpc_method_handler(
                    servicer.SandboxGetLogs,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxGetLogsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TaskLogsBatch.SerializeToString,
            ),
            'SandboxGetResourceUsage': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxGetResourceUsage,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxGetResourceUsageRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxGetResourceUsageResponse.SerializeToString,
            ),
            'SandboxGetTaskId': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxGetTaskId,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxGetTaskIdRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxGetTaskIdResponse.SerializeToString,
            ),
            'SandboxGetTunnels': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxGetTunnels,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxGetTunnelsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxGetTunnelsResponse.SerializeToString,
            ),
            'SandboxList': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxList,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxListResponse.SerializeToString,
            ),
            'SandboxRestore': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxRestore,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxRestoreRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxRestoreResponse.SerializeToString,
            ),
            'SandboxSnapshot': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxSnapshot,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxSnapshotRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxSnapshotResponse.SerializeToString,
            ),
            'SandboxSnapshotFs': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxSnapshotFs,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxSnapshotFsRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxSnapshotFsResponse.SerializeToString,
            ),
            'SandboxSnapshotGet': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxSnapshotGet,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxSnapshotGetRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxSnapshotGetResponse.SerializeToString,
            ),
            'SandboxSnapshotWait': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxSnapshotWait,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxSnapshotWaitRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxSnapshotWaitResponse.SerializeToString,
            ),
            'SandboxStdinWrite': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxStdinWrite,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxStdinWriteRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxStdinWriteResponse.SerializeToString,
            ),
            'SandboxTagsSet': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxTagsSet,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxTagsSetRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'SandboxTerminate': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxTerminate,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxTerminateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxTerminateResponse.SerializeToString,
            ),
            'SandboxWait': grpc.unary_unary_rpc_method_handler(
                    servicer.SandboxWait,
                    request_deserializer=modal__proto_dot_api__pb2.SandboxWaitRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SandboxWaitResponse.SerializeToString,
            ),
            'SecretDelete': grpc.unary_unary_rpc_method_handler(
                    servicer.SecretDelete,
                    request_deserializer=modal__proto_dot_api__pb2.SecretDeleteRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'SecretGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.SecretGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.SecretGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SecretGetOrCreateResponse.SerializeToString,
            ),
            'SecretList': grpc.unary_unary_rpc_method_handler(
                    servicer.SecretList,
                    request_deserializer=modal__proto_dot_api__pb2.SecretListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SecretListResponse.SerializeToString,
            ),
            'SharedVolumeDelete': grpc.unary_unary_rpc_method_handler(
                    servicer.SharedVolumeDelete,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumeDeleteRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'SharedVolumeGetFile': grpc.unary_unary_rpc_method_handler(
                    servicer.SharedVolumeGetFile,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumeGetFileRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SharedVolumeGetFileResponse.SerializeToString,
            ),
            'SharedVolumeGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.SharedVolumeGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumeGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SharedVolumeGetOrCreateResponse.SerializeToString,
            ),
            'SharedVolumeHeartbeat': grpc.unary_unary_rpc_method_handler(
                    servicer.SharedVolumeHeartbeat,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumeHeartbeatRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'SharedVolumeList': grpc.unary_unary_rpc_method_handler(
                    servicer.SharedVolumeList,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumeListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SharedVolumeListResponse.SerializeToString,
            ),
            'SharedVolumeListFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.SharedVolumeListFiles,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumeListFilesRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SharedVolumeListFilesResponse.SerializeToString,
            ),
            'SharedVolumeListFilesStream': grpc.unary_stream_rpc_method_handler(
                    servicer.SharedVolumeListFilesStream,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumeListFilesRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SharedVolumeListFilesResponse.SerializeToString,
            ),
            'SharedVolumePutFile': grpc.unary_unary_rpc_method_handler(
                    servicer.SharedVolumePutFile,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumePutFileRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.SharedVolumePutFileResponse.SerializeToString,
            ),
            'SharedVolumeRemoveFile': grpc.unary_unary_rpc_method_handler(
                    servicer.SharedVolumeRemoveFile,
                    request_deserializer=modal__proto_dot_api__pb2.SharedVolumeRemoveFileRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'TaskClusterHello': grpc.unary_unary_rpc_method_handler(
                    servicer.TaskClusterHello,
                    request_deserializer=modal__proto_dot_api__pb2.TaskClusterHelloRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TaskClusterHelloResponse.SerializeToString,
            ),
            'TaskCurrentInputs': grpc.unary_unary_rpc_method_handler(
                    servicer.TaskCurrentInputs,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TaskCurrentInputsResponse.SerializeToString,
            ),
            'TaskList': grpc.unary_unary_rpc_method_handler(
                    servicer.TaskList,
                    request_deserializer=modal__proto_dot_api__pb2.TaskListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TaskListResponse.SerializeToString,
            ),
            'TaskResult': grpc.unary_unary_rpc_method_handler(
                    servicer.TaskResult,
                    request_deserializer=modal__proto_dot_api__pb2.TaskResultRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'TokenFlowCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.TokenFlowCreate,
                    request_deserializer=modal__proto_dot_api__pb2.TokenFlowCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TokenFlowCreateResponse.SerializeToString,
            ),
            'TokenFlowWait': grpc.unary_unary_rpc_method_handler(
                    servicer.TokenFlowWait,
                    request_deserializer=modal__proto_dot_api__pb2.TokenFlowWaitRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TokenFlowWaitResponse.SerializeToString,
            ),
            'TunnelStart': grpc.unary_unary_rpc_method_handler(
                    servicer.TunnelStart,
                    request_deserializer=modal__proto_dot_api__pb2.TunnelStartRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TunnelStartResponse.SerializeToString,
            ),
            'TunnelStop': grpc.unary_unary_rpc_method_handler(
                    servicer.TunnelStop,
                    request_deserializer=modal__proto_dot_api__pb2.TunnelStopRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.TunnelStopResponse.SerializeToString,
            ),
            'VolumeCommit': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeCommit,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeCommitRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.VolumeCommitResponse.SerializeToString,
            ),
            'VolumeCopyFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeCopyFiles,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeCopyFilesRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'VolumeCopyFiles2': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeCopyFiles2,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeCopyFiles2Request.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'VolumeDelete': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeDelete,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeDeleteRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'VolumeGetFile': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeGetFile,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeGetFileRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.VolumeGetFileResponse.SerializeToString,
            ),
            'VolumeGetFile2': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeGetFile2,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeGetFile2Request.FromString,
                    response_serializer=modal__proto_dot_api__pb2.VolumeGetFile2Response.SerializeToString,
            ),
            'VolumeGetOrCreate': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeGetOrCreate,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeGetOrCreateRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.VolumeGetOrCreateResponse.SerializeToString,
            ),
            'VolumeHeartbeat': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeHeartbeat,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeHeartbeatRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'VolumeList': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeList,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeListRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.VolumeListResponse.SerializeToString,
            ),
            'VolumeListFiles': grpc.unary_stream_rpc_method_handler(
                    servicer.VolumeListFiles,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeListFilesRequest.FromString,
                    response_serializer=modal__proto_dot_api__pb2.VolumeListFilesResponse.SerializeToString,
            ),
            'VolumeListFiles2': grpc.unary_stream_rpc_method_handler(
                    servicer.VolumeListFiles2,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeListFiles2Request.FromString,
                    response_serializer=modal__proto_dot_api__pb2.VolumeListFiles2Response.SerializeToString,
            ),
            'VolumePutFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumePutFiles,
                    request_deserializer=modal__proto_dot_api__pb2.VolumePutFilesRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'VolumePutFiles2': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumePutFiles2,
                    request_deserializer=modal__proto_dot_api__pb2.VolumePutFiles2Request.FromString,
                    response_serializer=modal__proto_dot_api__pb2.VolumePutFiles2Response.SerializeToString,
            ),
            'VolumeReload': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeReload,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeReloadRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'VolumeRemoveFile': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeRemoveFile,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeRemoveFileRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'VolumeRemoveFile2': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeRemoveFile2,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeRemoveFile2Request.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'VolumeRename': grpc.unary_unary_rpc_method_handler(
                    servicer.VolumeRename,
                    request_deserializer=modal__proto_dot_api__pb2.VolumeRenameRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'WorkspaceNameLookup': grpc.unary_unary_rpc_method_handler(
                    servicer.WorkspaceNameLookup,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=modal__proto_dot_api__pb2.WorkspaceNameLookupResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'modal.client.ModalClient', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ModalClient(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def AppClientDisconnect(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppClientDisconnect',
            modal__proto_dot_api__pb2.AppClientDisconnectRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppCreate',
            modal__proto_dot_api__pb2.AppCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppDeploy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppDeploy',
            modal__proto_dot_api__pb2.AppDeployRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppDeployResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppDeploymentHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppDeploymentHistory',
            modal__proto_dot_api__pb2.AppDeploymentHistoryRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppDeploymentHistoryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppGetByDeploymentName(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppGetByDeploymentName',
            modal__proto_dot_api__pb2.AppGetByDeploymentNameRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppGetByDeploymentNameResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppGetLayout(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppGetLayout',
            modal__proto_dot_api__pb2.AppGetLayoutRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppGetLayoutResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppGetLogs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/AppGetLogs',
            modal__proto_dot_api__pb2.AppGetLogsRequest.SerializeToString,
            modal__proto_dot_api__pb2.TaskLogsBatch.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppGetObjects(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppGetObjects',
            modal__proto_dot_api__pb2.AppGetObjectsRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppGetObjectsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppGetOrCreate',
            modal__proto_dot_api__pb2.AppGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppHeartbeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppHeartbeat',
            modal__proto_dot_api__pb2.AppHeartbeatRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppList',
            modal__proto_dot_api__pb2.AppListRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppLookup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppLookup',
            modal__proto_dot_api__pb2.AppLookupRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppLookupResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppPublish(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppPublish',
            modal__proto_dot_api__pb2.AppPublishRequest.SerializeToString,
            modal__proto_dot_api__pb2.AppPublishResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppRollback(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppRollback',
            modal__proto_dot_api__pb2.AppRollbackRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppSetObjects(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppSetObjects',
            modal__proto_dot_api__pb2.AppSetObjectsRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AppStop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AppStop',
            modal__proto_dot_api__pb2.AppStopRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AttemptAwait(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AttemptAwait',
            modal__proto_dot_api__pb2.AttemptAwaitRequest.SerializeToString,
            modal__proto_dot_api__pb2.AttemptAwaitResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AttemptRetry(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AttemptRetry',
            modal__proto_dot_api__pb2.AttemptRetryRequest.SerializeToString,
            modal__proto_dot_api__pb2.AttemptRetryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AttemptStart(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/AttemptStart',
            modal__proto_dot_api__pb2.AttemptStartRequest.SerializeToString,
            modal__proto_dot_api__pb2.AttemptStartResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def BlobCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/BlobCreate',
            modal__proto_dot_api__pb2.BlobCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.BlobCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def BlobGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/BlobGet',
            modal__proto_dot_api__pb2.BlobGetRequest.SerializeToString,
            modal__proto_dot_api__pb2.BlobGetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClassCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ClassCreate',
            modal__proto_dot_api__pb2.ClassCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.ClassCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClassGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ClassGet',
            modal__proto_dot_api__pb2.ClassGetRequest.SerializeToString,
            modal__proto_dot_api__pb2.ClassGetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClientHello(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ClientHello',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            modal__proto_dot_api__pb2.ClientHelloResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClusterGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ClusterGet',
            modal__proto_dot_api__pb2.ClusterGetRequest.SerializeToString,
            modal__proto_dot_api__pb2.ClusterGetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClusterList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ClusterList',
            modal__proto_dot_api__pb2.ClusterListRequest.SerializeToString,
            modal__proto_dot_api__pb2.ClusterListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerCheckpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerCheckpoint',
            modal__proto_dot_api__pb2.ContainerCheckpointRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerExec(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerExec',
            modal__proto_dot_api__pb2.ContainerExecRequest.SerializeToString,
            modal__proto_dot_api__pb2.ContainerExecResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerExecGetOutput(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/ContainerExecGetOutput',
            modal__proto_dot_api__pb2.ContainerExecGetOutputRequest.SerializeToString,
            modal__proto_dot_api__pb2.RuntimeOutputBatch.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerExecPutInput(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerExecPutInput',
            modal__proto_dot_api__pb2.ContainerExecPutInputRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerExecWait(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerExecWait',
            modal__proto_dot_api__pb2.ContainerExecWaitRequest.SerializeToString,
            modal__proto_dot_api__pb2.ContainerExecWaitResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerFilesystemExec(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerFilesystemExec',
            modal__proto_dot_api__pb2.ContainerFilesystemExecRequest.SerializeToString,
            modal__proto_dot_api__pb2.ContainerFilesystemExecResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerFilesystemExecGetOutput(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/ContainerFilesystemExecGetOutput',
            modal__proto_dot_api__pb2.ContainerFilesystemExecGetOutputRequest.SerializeToString,
            modal__proto_dot_api__pb2.FilesystemRuntimeOutputBatch.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerHeartbeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerHeartbeat',
            modal__proto_dot_api__pb2.ContainerHeartbeatRequest.SerializeToString,
            modal__proto_dot_api__pb2.ContainerHeartbeatResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerHello(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerHello',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerLog(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerLog',
            modal__proto_dot_api__pb2.ContainerLogRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ContainerStop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ContainerStop',
            modal__proto_dot_api__pb2.ContainerStopRequest.SerializeToString,
            modal__proto_dot_api__pb2.ContainerStopResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictClear(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictClear',
            modal__proto_dot_api__pb2.DictClearRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictContains(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictContains',
            modal__proto_dot_api__pb2.DictContainsRequest.SerializeToString,
            modal__proto_dot_api__pb2.DictContainsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictContents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/DictContents',
            modal__proto_dot_api__pb2.DictContentsRequest.SerializeToString,
            modal__proto_dot_api__pb2.DictEntry.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictDelete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictDelete',
            modal__proto_dot_api__pb2.DictDeleteRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictGet',
            modal__proto_dot_api__pb2.DictGetRequest.SerializeToString,
            modal__proto_dot_api__pb2.DictGetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictGetOrCreate',
            modal__proto_dot_api__pb2.DictGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.DictGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictHeartbeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictHeartbeat',
            modal__proto_dot_api__pb2.DictHeartbeatRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictLen(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictLen',
            modal__proto_dot_api__pb2.DictLenRequest.SerializeToString,
            modal__proto_dot_api__pb2.DictLenResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictList',
            modal__proto_dot_api__pb2.DictListRequest.SerializeToString,
            modal__proto_dot_api__pb2.DictListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictPop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictPop',
            modal__proto_dot_api__pb2.DictPopRequest.SerializeToString,
            modal__proto_dot_api__pb2.DictPopResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DictUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DictUpdate',
            modal__proto_dot_api__pb2.DictUpdateRequest.SerializeToString,
            modal__proto_dot_api__pb2.DictUpdateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DomainCertificateVerify(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DomainCertificateVerify',
            modal__proto_dot_api__pb2.DomainCertificateVerifyRequest.SerializeToString,
            modal__proto_dot_api__pb2.DomainCertificateVerifyResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DomainCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DomainCreate',
            modal__proto_dot_api__pb2.DomainCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.DomainCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DomainList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/DomainList',
            modal__proto_dot_api__pb2.DomainListRequest.SerializeToString,
            modal__proto_dot_api__pb2.DomainListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnvironmentCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/EnvironmentCreate',
            modal__proto_dot_api__pb2.EnvironmentCreateRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnvironmentDelete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/EnvironmentDelete',
            modal__proto_dot_api__pb2.EnvironmentDeleteRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnvironmentGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/EnvironmentGetOrCreate',
            modal__proto_dot_api__pb2.EnvironmentGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.EnvironmentGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnvironmentList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/EnvironmentList',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            modal__proto_dot_api__pb2.EnvironmentListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EnvironmentUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/EnvironmentUpdate',
            modal__proto_dot_api__pb2.EnvironmentUpdateRequest.SerializeToString,
            modal__proto_dot_api__pb2.EnvironmentListItem.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionAsyncInvoke(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionAsyncInvoke',
            modal__proto_dot_api__pb2.FunctionAsyncInvokeRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionAsyncInvokeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionBindParams(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionBindParams',
            modal__proto_dot_api__pb2.FunctionBindParamsRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionBindParamsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionCallCancel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionCallCancel',
            modal__proto_dot_api__pb2.FunctionCallCancelRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionCallGetDataIn(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/FunctionCallGetDataIn',
            modal__proto_dot_api__pb2.FunctionCallGetDataRequest.SerializeToString,
            modal__proto_dot_api__pb2.DataChunk.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionCallGetDataOut(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/FunctionCallGetDataOut',
            modal__proto_dot_api__pb2.FunctionCallGetDataRequest.SerializeToString,
            modal__proto_dot_api__pb2.DataChunk.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionCallList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionCallList',
            modal__proto_dot_api__pb2.FunctionCallListRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionCallListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionCallPutDataOut(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionCallPutDataOut',
            modal__proto_dot_api__pb2.FunctionCallPutDataRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionCreate',
            modal__proto_dot_api__pb2.FunctionCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionGet',
            modal__proto_dot_api__pb2.FunctionGetRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionGetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionGetCallGraph(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionGetCallGraph',
            modal__proto_dot_api__pb2.FunctionGetCallGraphRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionGetCallGraphResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionGetCurrentStats(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionGetCurrentStats',
            modal__proto_dot_api__pb2.FunctionGetCurrentStatsRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionStats.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionGetDynamicConcurrency(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionGetDynamicConcurrency',
            modal__proto_dot_api__pb2.FunctionGetDynamicConcurrencyRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionGetDynamicConcurrencyResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionGetInputs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionGetInputs',
            modal__proto_dot_api__pb2.FunctionGetInputsRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionGetInputsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionGetOutputs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionGetOutputs',
            modal__proto_dot_api__pb2.FunctionGetOutputsRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionGetOutputsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionGetSerialized(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionGetSerialized',
            modal__proto_dot_api__pb2.FunctionGetSerializedRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionGetSerializedResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionMap(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionMap',
            modal__proto_dot_api__pb2.FunctionMapRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionMapResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionPrecreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionPrecreate',
            modal__proto_dot_api__pb2.FunctionPrecreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionPrecreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionPutInputs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionPutInputs',
            modal__proto_dot_api__pb2.FunctionPutInputsRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionPutInputsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionPutOutputs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionPutOutputs',
            modal__proto_dot_api__pb2.FunctionPutOutputsRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionRetryInputs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionRetryInputs',
            modal__proto_dot_api__pb2.FunctionRetryInputsRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionRetryInputsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionStartPtyShell(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionStartPtyShell',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def FunctionUpdateSchedulingParams(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/FunctionUpdateSchedulingParams',
            modal__proto_dot_api__pb2.FunctionUpdateSchedulingParamsRequest.SerializeToString,
            modal__proto_dot_api__pb2.FunctionUpdateSchedulingParamsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ImageFromId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ImageFromId',
            modal__proto_dot_api__pb2.ImageFromIdRequest.SerializeToString,
            modal__proto_dot_api__pb2.ImageFromIdResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ImageGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ImageGetOrCreate',
            modal__proto_dot_api__pb2.ImageGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.ImageGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ImageJoinStreaming(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/ImageJoinStreaming',
            modal__proto_dot_api__pb2.ImageJoinStreamingRequest.SerializeToString,
            modal__proto_dot_api__pb2.ImageJoinStreamingResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def MountGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/MountGetOrCreate',
            modal__proto_dot_api__pb2.MountGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.MountGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def MountPutFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/MountPutFile',
            modal__proto_dot_api__pb2.MountPutFileRequest.SerializeToString,
            modal__proto_dot_api__pb2.MountPutFileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def NotebookKernelPublishResults(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/NotebookKernelPublishResults',
            modal__proto_dot_api__pb2.NotebookKernelPublishResultsRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ProxyAddIp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ProxyAddIp',
            modal__proto_dot_api__pb2.ProxyAddIpRequest.SerializeToString,
            modal__proto_dot_api__pb2.ProxyAddIpResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ProxyCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ProxyCreate',
            modal__proto_dot_api__pb2.ProxyCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.ProxyCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ProxyDelete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ProxyDelete',
            modal__proto_dot_api__pb2.ProxyDeleteRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ProxyGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ProxyGet',
            modal__proto_dot_api__pb2.ProxyGetRequest.SerializeToString,
            modal__proto_dot_api__pb2.ProxyGetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ProxyGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ProxyGetOrCreate',
            modal__proto_dot_api__pb2.ProxyGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.ProxyGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ProxyList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ProxyList',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            modal__proto_dot_api__pb2.ProxyListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ProxyRemoveIp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/ProxyRemoveIp',
            modal__proto_dot_api__pb2.ProxyRemoveIpRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueueClear(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueueClear',
            modal__proto_dot_api__pb2.QueueClearRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueueDelete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueueDelete',
            modal__proto_dot_api__pb2.QueueDeleteRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueueGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueueGet',
            modal__proto_dot_api__pb2.QueueGetRequest.SerializeToString,
            modal__proto_dot_api__pb2.QueueGetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueueGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueueGetOrCreate',
            modal__proto_dot_api__pb2.QueueGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.QueueGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueueHeartbeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueueHeartbeat',
            modal__proto_dot_api__pb2.QueueHeartbeatRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueueLen(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueueLen',
            modal__proto_dot_api__pb2.QueueLenRequest.SerializeToString,
            modal__proto_dot_api__pb2.QueueLenResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueueList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueueList',
            modal__proto_dot_api__pb2.QueueListRequest.SerializeToString,
            modal__proto_dot_api__pb2.QueueListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueueNextItems(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueueNextItems',
            modal__proto_dot_api__pb2.QueueNextItemsRequest.SerializeToString,
            modal__proto_dot_api__pb2.QueueNextItemsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def QueuePut(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/QueuePut',
            modal__proto_dot_api__pb2.QueuePutRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxCreate',
            modal__proto_dot_api__pb2.SandboxCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxGetLogs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/SandboxGetLogs',
            modal__proto_dot_api__pb2.SandboxGetLogsRequest.SerializeToString,
            modal__proto_dot_api__pb2.TaskLogsBatch.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxGetResourceUsage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxGetResourceUsage',
            modal__proto_dot_api__pb2.SandboxGetResourceUsageRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxGetResourceUsageResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxGetTaskId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxGetTaskId',
            modal__proto_dot_api__pb2.SandboxGetTaskIdRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxGetTaskIdResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxGetTunnels(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxGetTunnels',
            modal__proto_dot_api__pb2.SandboxGetTunnelsRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxGetTunnelsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxList',
            modal__proto_dot_api__pb2.SandboxListRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxRestore(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxRestore',
            modal__proto_dot_api__pb2.SandboxRestoreRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxRestoreResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxSnapshot(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxSnapshot',
            modal__proto_dot_api__pb2.SandboxSnapshotRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxSnapshotResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxSnapshotFs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxSnapshotFs',
            modal__proto_dot_api__pb2.SandboxSnapshotFsRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxSnapshotFsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxSnapshotGet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxSnapshotGet',
            modal__proto_dot_api__pb2.SandboxSnapshotGetRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxSnapshotGetResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxSnapshotWait(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxSnapshotWait',
            modal__proto_dot_api__pb2.SandboxSnapshotWaitRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxSnapshotWaitResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxStdinWrite(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxStdinWrite',
            modal__proto_dot_api__pb2.SandboxStdinWriteRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxStdinWriteResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxTagsSet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxTagsSet',
            modal__proto_dot_api__pb2.SandboxTagsSetRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxTerminate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxTerminate',
            modal__proto_dot_api__pb2.SandboxTerminateRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxTerminateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SandboxWait(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SandboxWait',
            modal__proto_dot_api__pb2.SandboxWaitRequest.SerializeToString,
            modal__proto_dot_api__pb2.SandboxWaitResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SecretDelete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SecretDelete',
            modal__proto_dot_api__pb2.SecretDeleteRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SecretGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SecretGetOrCreate',
            modal__proto_dot_api__pb2.SecretGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.SecretGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SecretList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SecretList',
            modal__proto_dot_api__pb2.SecretListRequest.SerializeToString,
            modal__proto_dot_api__pb2.SecretListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumeDelete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SharedVolumeDelete',
            modal__proto_dot_api__pb2.SharedVolumeDeleteRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumeGetFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SharedVolumeGetFile',
            modal__proto_dot_api__pb2.SharedVolumeGetFileRequest.SerializeToString,
            modal__proto_dot_api__pb2.SharedVolumeGetFileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumeGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SharedVolumeGetOrCreate',
            modal__proto_dot_api__pb2.SharedVolumeGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.SharedVolumeGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumeHeartbeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SharedVolumeHeartbeat',
            modal__proto_dot_api__pb2.SharedVolumeHeartbeatRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumeList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SharedVolumeList',
            modal__proto_dot_api__pb2.SharedVolumeListRequest.SerializeToString,
            modal__proto_dot_api__pb2.SharedVolumeListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumeListFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SharedVolumeListFiles',
            modal__proto_dot_api__pb2.SharedVolumeListFilesRequest.SerializeToString,
            modal__proto_dot_api__pb2.SharedVolumeListFilesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumeListFilesStream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/SharedVolumeListFilesStream',
            modal__proto_dot_api__pb2.SharedVolumeListFilesRequest.SerializeToString,
            modal__proto_dot_api__pb2.SharedVolumeListFilesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumePutFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SharedVolumePutFile',
            modal__proto_dot_api__pb2.SharedVolumePutFileRequest.SerializeToString,
            modal__proto_dot_api__pb2.SharedVolumePutFileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SharedVolumeRemoveFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/SharedVolumeRemoveFile',
            modal__proto_dot_api__pb2.SharedVolumeRemoveFileRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TaskClusterHello(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/TaskClusterHello',
            modal__proto_dot_api__pb2.TaskClusterHelloRequest.SerializeToString,
            modal__proto_dot_api__pb2.TaskClusterHelloResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TaskCurrentInputs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/TaskCurrentInputs',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            modal__proto_dot_api__pb2.TaskCurrentInputsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TaskList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/TaskList',
            modal__proto_dot_api__pb2.TaskListRequest.SerializeToString,
            modal__proto_dot_api__pb2.TaskListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TaskResult(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/TaskResult',
            modal__proto_dot_api__pb2.TaskResultRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TokenFlowCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/TokenFlowCreate',
            modal__proto_dot_api__pb2.TokenFlowCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.TokenFlowCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TokenFlowWait(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/TokenFlowWait',
            modal__proto_dot_api__pb2.TokenFlowWaitRequest.SerializeToString,
            modal__proto_dot_api__pb2.TokenFlowWaitResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TunnelStart(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/TunnelStart',
            modal__proto_dot_api__pb2.TunnelStartRequest.SerializeToString,
            modal__proto_dot_api__pb2.TunnelStartResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TunnelStop(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/TunnelStop',
            modal__proto_dot_api__pb2.TunnelStopRequest.SerializeToString,
            modal__proto_dot_api__pb2.TunnelStopResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeCommit(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeCommit',
            modal__proto_dot_api__pb2.VolumeCommitRequest.SerializeToString,
            modal__proto_dot_api__pb2.VolumeCommitResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeCopyFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeCopyFiles',
            modal__proto_dot_api__pb2.VolumeCopyFilesRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeCopyFiles2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeCopyFiles2',
            modal__proto_dot_api__pb2.VolumeCopyFiles2Request.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeDelete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeDelete',
            modal__proto_dot_api__pb2.VolumeDeleteRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeGetFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeGetFile',
            modal__proto_dot_api__pb2.VolumeGetFileRequest.SerializeToString,
            modal__proto_dot_api__pb2.VolumeGetFileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeGetFile2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeGetFile2',
            modal__proto_dot_api__pb2.VolumeGetFile2Request.SerializeToString,
            modal__proto_dot_api__pb2.VolumeGetFile2Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeGetOrCreate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeGetOrCreate',
            modal__proto_dot_api__pb2.VolumeGetOrCreateRequest.SerializeToString,
            modal__proto_dot_api__pb2.VolumeGetOrCreateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeHeartbeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeHeartbeat',
            modal__proto_dot_api__pb2.VolumeHeartbeatRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeList',
            modal__proto_dot_api__pb2.VolumeListRequest.SerializeToString,
            modal__proto_dot_api__pb2.VolumeListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeListFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/VolumeListFiles',
            modal__proto_dot_api__pb2.VolumeListFilesRequest.SerializeToString,
            modal__proto_dot_api__pb2.VolumeListFilesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeListFiles2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/modal.client.ModalClient/VolumeListFiles2',
            modal__proto_dot_api__pb2.VolumeListFiles2Request.SerializeToString,
            modal__proto_dot_api__pb2.VolumeListFiles2Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumePutFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumePutFiles',
            modal__proto_dot_api__pb2.VolumePutFilesRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumePutFiles2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumePutFiles2',
            modal__proto_dot_api__pb2.VolumePutFiles2Request.SerializeToString,
            modal__proto_dot_api__pb2.VolumePutFiles2Response.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeReload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeReload',
            modal__proto_dot_api__pb2.VolumeReloadRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeRemoveFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeRemoveFile',
            modal__proto_dot_api__pb2.VolumeRemoveFileRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeRemoveFile2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeRemoveFile2',
            modal__proto_dot_api__pb2.VolumeRemoveFile2Request.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def VolumeRename(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/VolumeRename',
            modal__proto_dot_api__pb2.VolumeRenameRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def WorkspaceNameLookup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/modal.client.ModalClient/WorkspaceNameLookup',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            modal__proto_dot_api__pb2.WorkspaceNameLookupResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
