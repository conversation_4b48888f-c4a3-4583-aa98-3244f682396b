# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: modal_proto/api.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from modal_proto import options_pb2 as modal__proto_dot_options__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15modal_proto/api.proto\x12\x0cmodal.client\x1a\x19modal_proto/options.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1egoogle/protobuf/wrappers.proto\"r\n\x1a\x41ppClientDisconnectRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x31\n\x06reason\x18\x02 \x01(\x0e\x32!.modal.client.AppDisconnectReason\x12\x11\n\texception\x18\x03 \x01(\t\"\x85\x01\n\x10\x41ppCreateRequest\x12\x17\n\tclient_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x18\n\x10\x65nvironment_name\x18\x05 \x01(\t\x12)\n\tapp_state\x18\x06 \x01(\x0e\x32\x16.modal.client.AppState\"O\n\x11\x41ppCreateResponse\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x14\n\x0c\x61pp_page_url\x18\x02 \x01(\t\x12\x14\n\x0c\x61pp_logs_url\x18\x03 \x01(\t\"\xc7\x01\n\x10\x41ppDeployRequest\x12\x14\n\x06\x61pp_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x34\n\tnamespace\x18\x02 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x15\n\robject_entity\x18\x04 \x01(\t\x12\x35\n\nvisibility\x18\x05 \x01(\x0e\x32!.modal.client.AppDeployVisibility\x12\x0b\n\x03tag\x18\x06 \x01(\t\" \n\x11\x41ppDeployResponse\x12\x0b\n\x03url\x18\x01 \x01(\t\"\x9e\x02\n\x14\x41ppDeploymentHistory\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\r\x12\x16\n\x0e\x63lient_version\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65ployed_at\x18\x04 \x01(\x01\x12\x13\n\x0b\x64\x65ployed_by\x18\x05 \x01(\t\x12\x1e\n\x16\x64\x65ployed_by_avatar_url\x18\t \x01(\t\x12\x0b\n\x03tag\x18\x06 \x01(\t\x12\x18\n\x10rollback_version\x18\x07 \x01(\r\x12\x18\n\x10rollback_allowed\x18\x08 \x01(\x08\x12\x32\n\x0b\x63ommit_info\x18\n \x01(\x0b\x32\x18.modal.client.CommitInfoH\x00\x88\x01\x01\x42\x0e\n\x0c_commit_info\"-\n\x1b\x41ppDeploymentHistoryRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\"d\n\x1c\x41ppDeploymentHistoryResponse\x12\x44\n\x18\x61pp_deployment_histories\x18\x01 \x03(\x0b\x32\".modal.client.AppDeploymentHistory\"}\n\x1d\x41ppGetByDeploymentNameRequest\x12\x34\n\tnamespace\x18\x01 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x18\n\x10\x65nvironment_name\x18\x04 \x01(\t\"0\n\x1e\x41ppGetByDeploymentNameResponse\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\"%\n\x13\x41ppGetLayoutRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\"C\n\x14\x41ppGetLayoutResponse\x12+\n\napp_layout\x18\x01 \x01(\x0b\x32\x17.modal.client.AppLayout\"\xd4\x01\n\x11\x41ppGetLogsRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\x12\x15\n\rlast_entry_id\x18\x04 \x01(\t\x12\x13\n\x0b\x66unction_id\x18\x05 \x01(\t\x12\x10\n\x08input_id\x18\x06 \x01(\t\x12\x0f\n\x07task_id\x18\x07 \x01(\t\x12\x18\n\x10\x66unction_call_id\x18\t \x01(\t\x12\x35\n\x0f\x66ile_descriptor\x18\x08 \x01(\x0e\x32\x1c.modal.client.FileDescriptor\"F\n\x11\x41ppGetObjectsItem\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12$\n\x06object\x18\x06 \x01(\x0b\x32\x14.modal.client.Object\"^\n\x14\x41ppGetObjectsRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x19\n\x11include_unindexed\x18\x02 \x01(\x08\x12\x1b\n\x13only_class_function\x18\x03 \x01(\x08\"G\n\x15\x41ppGetObjectsResponse\x12.\n\x05items\x18\x02 \x03(\x0b\x32\x1f.modal.client.AppGetObjectsItem\"\x83\x01\n\x15\x41ppGetOrCreateRequest\x12\x10\n\x08\x61pp_name\x18\x01 \x01(\t\x12\x18\n\x10\x65nvironment_name\x18\x02 \x01(\t\x12>\n\x14object_creation_type\x18\x03 \x01(\x0e\x32 .modal.client.ObjectCreationType\"(\n\x16\x41ppGetOrCreateResponse\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\"%\n\x13\x41ppHeartbeatRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\"\x91\x02\n\tAppLayout\x12%\n\x07objects\x18\x01 \x03(\x0b\x32\x14.modal.client.Object\x12>\n\x0c\x66unction_ids\x18\x02 \x03(\x0b\x32(.modal.client.AppLayout.FunctionIdsEntry\x12\x38\n\tclass_ids\x18\x03 \x03(\x0b\x32%.modal.client.AppLayout.ClassIdsEntry\x1a\x32\n\x10\x46unctionIdsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a/\n\rClassIdsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"*\n\x0e\x41ppListRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\"\xf5\x01\n\x0f\x41ppListResponse\x12\x37\n\x04\x61pps\x18\x01 \x03(\x0b\x32).modal.client.AppListResponse.AppListItem\x1a\xa8\x01\n\x0b\x41ppListItem\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12%\n\x05state\x18\x04 \x01(\x0e\x32\x16.modal.client.AppState\x12\x12\n\ncreated_at\x18\x05 \x01(\x01\x12\x12\n\nstopped_at\x18\x06 \x01(\x01\x12\x17\n\x0fn_running_tasks\x18\x08 \x01(\x05\x12\x0c\n\x04name\x18\n \x01(\t\">\n\x10\x41ppLookupRequest\x12\x10\n\x08\x61pp_name\x18\x02 \x01(\t\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\"#\n\x11\x41ppLookupResponse\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\"\xcc\x04\n\x11\x41ppPublishRequest\x12\x14\n\x06\x61pp_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x16\n\x0e\x64\x65ployment_tag\x18\x03 \x01(\t\x12)\n\tapp_state\x18\x04 \x01(\x0e\x32\x16.modal.client.AppState\x12\x46\n\x0c\x66unction_ids\x18\x05 \x03(\x0b\x32\x30.modal.client.AppPublishRequest.FunctionIdsEntry\x12@\n\tclass_ids\x18\x06 \x03(\x0b\x32-.modal.client.AppPublishRequest.ClassIdsEntry\x12J\n\x0e\x64\x65\x66inition_ids\x18\x07 \x03(\x0b\x32\x32.modal.client.AppPublishRequest.DefinitionIdsEntry\x12\x18\n\x10rollback_version\x18\x08 \x01(\r\x12\x16\n\x0e\x63lient_version\x18\t \x01(\t\x12-\n\x0b\x63ommit_info\x18\n \x01(\x0b\x32\x18.modal.client.CommitInfo\x1a\x32\n\x10\x46unctionIdsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a/\n\rClassIdsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x34\n\x12\x44\x65\x66initionIdsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"Q\n\x12\x41ppPublishResponse\x12\x0b\n\x03url\x18\x01 \x01(\t\x12.\n\x0fserver_warnings\x18\x03 \x03(\x0b\x32\x15.modal.client.Warning\"5\n\x12\x41ppRollbackRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\x05\"\x9b\x02\n\x14\x41ppSetObjectsRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12T\n\x12indexed_object_ids\x18\x02 \x03(\x0b\x32\x38.modal.client.AppSetObjectsRequest.IndexedObjectIdsEntry\x12\x11\n\tclient_id\x18\x03 \x01(\t\x12\x1c\n\x14unindexed_object_ids\x18\x04 \x03(\t\x12-\n\rnew_app_state\x18\x05 \x01(\x0e\x32\x16.modal.client.AppState\x1a\x37\n\x15IndexedObjectIdsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01J\x04\x08\x06\x10\x07\"S\n\x0e\x41ppStopRequest\x12\x14\n\x06\x61pp_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12+\n\x06source\x18\x02 \x01(\x0e\x32\x1b.modal.client.AppStopSource\"\xa3\x0e\n\x04\x41sgi\x12\'\n\x04http\x18\x01 \x01(\x0b\x32\x17.modal.client.Asgi.HttpH\x00\x12\x36\n\x0chttp_request\x18\x02 \x01(\x0b\x32\x1e.modal.client.Asgi.HttpRequestH\x00\x12\x43\n\x13http_response_start\x18\x03 \x01(\x0b\x32$.modal.client.Asgi.HttpResponseStartH\x00\x12\x41\n\x12http_response_body\x18\x04 \x01(\x0b\x32#.modal.client.Asgi.HttpResponseBodyH\x00\x12I\n\x16http_response_trailers\x18\x05 \x01(\x0b\x32\'.modal.client.Asgi.HttpResponseTrailersH\x00\x12<\n\x0fhttp_disconnect\x18\x06 \x01(\x0b\x32!.modal.client.Asgi.HttpDisconnectH\x00\x12\x31\n\twebsocket\x18\x07 \x01(\x0b\x32\x1c.modal.client.Asgi.WebsocketH\x00\x12@\n\x11websocket_connect\x18\x08 \x01(\x0b\x32#.modal.client.Asgi.WebsocketConnectH\x00\x12>\n\x10websocket_accept\x18\t \x01(\x0b\x32\".modal.client.Asgi.WebsocketAcceptH\x00\x12@\n\x11websocket_receive\x18\n \x01(\x0b\x32#.modal.client.Asgi.WebsocketReceiveH\x00\x12:\n\x0ewebsocket_send\x18\x0b \x01(\x0b\x32 .modal.client.Asgi.WebsocketSendH\x00\x12\x46\n\x14websocket_disconnect\x18\x0c \x01(\x0b\x32&.modal.client.Asgi.WebsocketDisconnectH\x00\x12<\n\x0fwebsocket_close\x18\r \x01(\x0b\x32!.modal.client.Asgi.WebsocketCloseH\x00\x1a\xc5\x01\n\x04Http\x12\x14\n\x0chttp_version\x18\x01 \x01(\t\x12\x0e\n\x06method\x18\x02 \x01(\t\x12\x0e\n\x06scheme\x18\x03 \x01(\t\x12\x0c\n\x04path\x18\x04 \x01(\t\x12\x14\n\x0cquery_string\x18\x05 \x01(\x0c\x12\x0f\n\x07headers\x18\x06 \x03(\x0c\x12\x18\n\x0b\x63lient_host\x18\x07 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0b\x63lient_port\x18\x08 \x01(\rH\x01\x88\x01\x01\x42\x0e\n\x0c_client_hostB\x0e\n\x0c_client_port\x1a.\n\x0bHttpRequest\x12\x0c\n\x04\x62ody\x18\x01 \x01(\x0c\x12\x11\n\tmore_body\x18\x02 \x01(\x08\x1a\x46\n\x11HttpResponseStart\x12\x0e\n\x06status\x18\x01 \x01(\r\x12\x0f\n\x07headers\x18\x02 \x03(\x0c\x12\x10\n\x08trailers\x18\x03 \x01(\x08\x1a\x33\n\x10HttpResponseBody\x12\x0c\n\x04\x62ody\x18\x01 \x01(\x0c\x12\x11\n\tmore_body\x18\x02 \x01(\x08\x1a>\n\x14HttpResponseTrailers\x12\x0f\n\x07headers\x18\x01 \x03(\x0c\x12\x15\n\rmore_trailers\x18\x02 \x01(\x08\x1a\x10\n\x0eHttpDisconnect\x1a\xd0\x01\n\tWebsocket\x12\x14\n\x0chttp_version\x18\x01 \x01(\t\x12\x0e\n\x06scheme\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\x14\n\x0cquery_string\x18\x04 \x01(\x0c\x12\x0f\n\x07headers\x18\x05 \x03(\x0c\x12\x18\n\x0b\x63lient_host\x18\x06 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0b\x63lient_port\x18\x07 \x01(\rH\x01\x88\x01\x01\x12\x14\n\x0csubprotocols\x18\x08 \x03(\tB\x0e\n\x0c_client_hostB\x0e\n\x0c_client_port\x1a\x12\n\x10WebsocketConnect\x1aL\n\x0fWebsocketAccept\x12\x18\n\x0bsubprotocol\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x0f\n\x07headers\x18\x02 \x03(\x0c\x42\x0e\n\x0c_subprotocol\x1a>\n\x10WebsocketReceive\x12\x0f\n\x05\x62ytes\x18\x01 \x01(\x0cH\x00\x12\x0e\n\x04text\x18\x02 \x01(\tH\x00\x42\t\n\x07\x63ontent\x1a;\n\rWebsocketSend\x12\x0f\n\x05\x62ytes\x18\x01 \x01(\x0cH\x00\x12\x0e\n\x04text\x18\x02 \x01(\tH\x00\x42\t\n\x07\x63ontent\x1a\x31\n\x13WebsocketDisconnect\x12\x11\n\x04\x63ode\x18\x01 \x01(\rH\x00\x88\x01\x01\x42\x07\n\x05_code\x1a<\n\x0eWebsocketClose\x12\x11\n\x04\x63ode\x18\x01 \x01(\rH\x00\x88\x01\x01\x12\x0e\n\x06reason\x18\x02 \x01(\tB\x07\n\x05_codeB\x06\n\x04type\"X\n\x13\x41ttemptAwaitRequest\x12\x15\n\rattempt_token\x18\x01 \x01(\t\x12\x14\n\x0crequested_at\x18\x02 \x01(\x01\x12\x14\n\x0ctimeout_secs\x18\x03 \x01(\x02\"\\\n\x14\x41ttemptAwaitResponse\x12\x39\n\x06output\x18\x01 \x01(\x0b\x32$.modal.client.FunctionGetOutputsItemH\x00\x88\x01\x01\x42\t\n\x07_output\"\x8e\x01\n\x13\x41ttemptRetryRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x17\n\x0fparent_input_id\x18\x02 \x01(\t\x12\x32\n\x05input\x18\x03 \x01(\x0b\x32#.modal.client.FunctionPutInputsItem\x12\x15\n\rattempt_token\x18\x04 \x01(\t\"-\n\x14\x41ttemptRetryResponse\x12\x15\n\rattempt_token\x18\x01 \x01(\t\"w\n\x13\x41ttemptStartRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x17\n\x0fparent_input_id\x18\x02 \x01(\t\x12\x32\n\x05input\x18\x03 \x01(\x0b\x32#.modal.client.FunctionPutInputsItem\"f\n\x14\x41ttemptStartResponse\x12\x15\n\rattempt_token\x18\x01 \x01(\t\x12\x37\n\x0cretry_policy\x18\x02 \x01(\x0b\x32!.modal.client.FunctionRetryPolicy\"\x8e\x02\n\x12\x41utoscalerSettings\x12\x1b\n\x0emin_containers\x18\x01 \x01(\rH\x00\x88\x01\x01\x12\x1b\n\x0emax_containers\x18\x02 \x01(\rH\x01\x88\x01\x01\x12\x1e\n\x11\x62uffer_containers\x18\x03 \x01(\rH\x02\x88\x01\x01\x12\x1b\n\x0escaleup_window\x18\x04 \x01(\rH\x03\x88\x01\x01\x12\x1d\n\x10scaledown_window\x18\x05 \x01(\rH\x04\x88\x01\x01\x42\x11\n\x0f_min_containersB\x11\n\x0f_max_containersB\x14\n\x12_buffer_containersB\x11\n\x0f_scaleup_windowB\x13\n\x11_scaledown_window\"7\n\tBaseImage\x12\x10\n\x08image_id\x18\x01 \x01(\t\x12\x12\n\ndocker_tag\x18\x02 \x01(\tJ\x04\x08\x04\x10\x05\"_\n\x11\x42lobCreateRequest\x12\x13\n\x0b\x63ontent_md5\x18\x01 \x01(\t\x12\x1d\n\x15\x63ontent_sha256_base64\x18\x02 \x01(\t\x12\x16\n\x0e\x63ontent_length\x18\x03 \x01(\x03\"\x84\x01\n\x12\x42lobCreateResponse\x12\x0f\n\x07\x62lob_id\x18\x02 \x01(\t\x12\x14\n\nupload_url\x18\x01 \x01(\tH\x00\x12\x32\n\tmultipart\x18\x03 \x01(\x0b\x32\x1d.modal.client.MultiPartUploadH\x00\x42\x13\n\x11upload_type_oneof\"!\n\x0e\x42lobGetRequest\x12\x0f\n\x07\x62lob_id\x18\x01 \x01(\t\"\'\n\x0f\x42lobGetResponse\x12\x14\n\x0c\x64ownload_url\x18\x01 \x01(\t\"`\n\rBuildFunction\x12\x12\n\ndefinition\x18\x01 \x01(\t\x12\x0f\n\x07globals\x18\x02 \x01(\x0c\x12*\n\x05input\x18\x03 \x01(\x0b\x32\x1b.modal.client.FunctionInput\"C\n\x10\x43\x61ncelInputEvent\x12\x11\n\tinput_ids\x18\x01 \x03(\t\x12\x1c\n\x14terminate_containers\x18\x02 \x01(\x08\"\xf3\x01\n\x0e\x43heckpointInfo\x12\x10\n\x08\x63hecksum\x18\x01 \x01(\t\x12.\n\x06status\x18\x02 \x01(\x0e\x32\x1e.modal.client.CheckpointStatus\x12\x15\n\rcheckpoint_id\x18\x03 \x01(\t\x12\x1b\n\x13runtime_fingerprint\x18\x04 \x01(\t\x12\x0c\n\x04size\x18\x05 \x01(\x03\x12\x1e\n\x16\x63hecksum_is_file_index\x18\x06 \x01(\x08\x12\x18\n\x10original_task_id\x18\x07 \x01(\t\x12\x1d\n\x15runsc_runtime_version\x18\t \x01(\tJ\x04\x08\x08\x10\t\"\x94\x01\n\x12\x43lassCreateRequest\x12\x14\n\x06\x61pp_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x19\n\x11\x65xisting_class_id\x18\x02 \x01(\t\x12*\n\x07methods\x18\x03 \x03(\x0b\x32\x19.modal.client.ClassMethod\x12\x1b\n\x13only_class_function\x18\x05 \x01(\x08J\x04\x08\x04\x10\x05\"c\n\x13\x43lassCreateResponse\x12\x10\n\x08\x63lass_id\x18\x01 \x01(\t\x12:\n\x0fhandle_metadata\x18\x02 \x01(\x0b\x32!.modal.client.ClassHandleMetadata\"\xb0\x01\n\x0f\x43lassGetRequest\x12\x10\n\x08\x61pp_name\x18\x01 \x01(\t\x12\x12\n\nobject_tag\x18\x02 \x01(\t\x12\x34\n\tnamespace\x18\x03 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x04 \x01(\t\x12\x1b\n\x13only_class_function\x18\n \x01(\x08J\x04\x08\x08\x10\tJ\x04\x08\t\x10\n\"\x90\x01\n\x10\x43lassGetResponse\x12\x10\n\x08\x63lass_id\x18\x01 \x01(\t\x12:\n\x0fhandle_metadata\x18\x02 \x01(\x0b\x32!.modal.client.ClassHandleMetadata\x12.\n\x0fserver_warnings\x18\x03 \x03(\x0b\x32\x15.modal.client.Warning\"\xa3\x01\n\x13\x43lassHandleMetadata\x12*\n\x07methods\x18\x01 \x03(\x0b\x32\x19.modal.client.ClassMethod\x12\x19\n\x11\x63lass_function_id\x18\x02 \x01(\t\x12\x45\n\x17\x63lass_function_metadata\x18\x03 \x01(\x0b\x32$.modal.client.FunctionHandleMetadata\"\x81\x01\n\x0b\x43lassMethod\x12\x15\n\rfunction_name\x18\x01 \x01(\t\x12\x13\n\x0b\x66unction_id\x18\x02 \x01(\t\x12\x46\n\x18\x66unction_handle_metadata\x18\x03 \x01(\x0b\x32$.modal.client.FunctionHandleMetadata\"\xaf\x02\n\x12\x43lassParameterInfo\x12M\n\x06\x66ormat\x18\x01 \x01(\x0e\x32=.modal.client.ClassParameterInfo.ParameterSerializationFormat\x12\x30\n\x06schema\x18\x02 \x03(\x0b\x32 .modal.client.ClassParameterSpec\"\x97\x01\n\x1cParameterSerializationFormat\x12*\n&PARAM_SERIALIZATION_FORMAT_UNSPECIFIED\x10\x00\x12%\n!PARAM_SERIALIZATION_FORMAT_PICKLE\x10\x01\x12$\n PARAM_SERIALIZATION_FORMAT_PROTO\x10\x02\"J\n\x11\x43lassParameterSet\x12\x35\n\nparameters\x18\x01 \x03(\x0b\x32!.modal.client.ClassParameterValue\"\xa4\x02\n\x12\x43lassParameterSpec\x12\x0c\n\x04name\x18\x01 \x01(\t\x12)\n\x04type\x18\x02 \x01(\x0e\x32\x1b.modal.client.ParameterType\x12\x13\n\x0bhas_default\x18\x03 \x01(\x08\x12\x18\n\x0estring_default\x18\x04 \x01(\tH\x00\x12\x15\n\x0bint_default\x18\x05 \x01(\x03H\x00\x12\x18\n\x0epickle_default\x18\x06 \x01(\x0cH\x00\x12\x17\n\rbytes_default\x18\x07 \x01(\x0cH\x00\x12\x16\n\x0c\x62ool_default\x18\t \x01(\x08H\x00\x12\x33\n\tfull_type\x18\x08 \x01(\x0b\x32 .modal.client.GenericPayloadTypeB\x0f\n\rdefault_oneof\"\xcf\x01\n\x13\x43lassParameterValue\x12\x0c\n\x04name\x18\x01 \x01(\t\x12)\n\x04type\x18\x02 \x01(\x0e\x32\x1b.modal.client.ParameterType\x12\x16\n\x0cstring_value\x18\x03 \x01(\tH\x00\x12\x13\n\tint_value\x18\x04 \x01(\x03H\x00\x12\x16\n\x0cpickle_value\x18\x05 \x01(\x0cH\x00\x12\x15\n\x0b\x62ytes_value\x18\x06 \x01(\x0cH\x00\x12\x14\n\nbool_value\x18\x07 \x01(\x08H\x00\x42\r\n\x0bvalue_oneof\"u\n\x13\x43lientHelloResponse\x12\x0f\n\x07warning\x18\x01 \x01(\t\x12\x1d\n\x15image_builder_version\x18\x02 \x01(\t\x12.\n\x0fserver_warnings\x18\x04 \x03(\x0b\x32\x15.modal.client.Warning\"\x97\x03\n\x10\x43loudBucketMount\x12\x13\n\x0b\x62ucket_name\x18\x01 \x01(\t\x12\x12\n\nmount_path\x18\x02 \x01(\t\x12\x1d\n\x15\x63redentials_secret_id\x18\x03 \x01(\t\x12\x11\n\tread_only\x18\x04 \x01(\x08\x12>\n\x0b\x62ucket_type\x18\x05 \x01(\x0e\x32).modal.client.CloudBucketMount.BucketType\x12\x16\n\x0erequester_pays\x18\x06 \x01(\x08\x12 \n\x13\x62ucket_endpoint_url\x18\x07 \x01(\tH\x00\x88\x01\x01\x12\x17\n\nkey_prefix\x18\x08 \x01(\tH\x01\x88\x01\x01\x12\x1f\n\x12oidc_auth_role_arn\x18\t \x01(\tH\x02\x88\x01\x01\"6\n\nBucketType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x06\n\x02S3\x10\x01\x12\x06\n\x02R2\x10\x02\x12\x07\n\x03GCP\x10\x03\x42\x16\n\x14_bucket_endpoint_urlB\r\n\x0b_key_prefixB\x15\n\x13_oidc_auth_role_arn\"\'\n\x11\x43lusterGetRequest\x12\x12\n\ncluster_id\x18\x01 \x01(\t\"A\n\x12\x43lusterGetResponse\x12+\n\x07\x63luster\x18\x01 \x01(\x0b\x32\x1a.modal.client.ClusterStats\".\n\x12\x43lusterListRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\"C\n\x13\x43lusterListResponse\x12,\n\x08\x63lusters\x18\x01 \x03(\x0b\x32\x1a.modal.client.ClusterStats\"X\n\x0c\x43lusterStats\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x10\n\x08task_ids\x18\x02 \x03(\t\x12\x12\n\ncluster_id\x18\x03 \x01(\t\x12\x12\n\nstarted_at\x18\x04 \x01(\x01\"\xa4\x01\n\nCommitInfo\x12\x0b\n\x03vcs\x18\x01 \x01(\t\x12\x0e\n\x06\x62ranch\x18\x02 \x01(\t\x12\x13\n\x0b\x63ommit_hash\x18\x03 \x01(\t\x12\x18\n\x10\x63ommit_timestamp\x18\x04 \x01(\x03\x12\r\n\x05\x64irty\x18\x05 \x01(\x08\x12\x13\n\x0b\x61uthor_name\x18\x06 \x01(\t\x12\x14\n\x0c\x61uthor_email\x18\x07 \x01(\t\x12\x10\n\x08repo_url\x18\x08 \x01(\t\"\xcc\x03\n\x12\x43ontainerArguments\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x13\n\x0b\x66unction_id\x18\x02 \x01(\t\x12\x0e\n\x06\x61pp_id\x18\x04 \x01(\t\x12,\n\x0c\x66unction_def\x18\x07 \x01(\x0b\x32\x16.modal.client.Function\x12+\n\nproxy_info\x18\x08 \x01(\x0b\x32\x17.modal.client.ProxyInfo\x12M\n\x0ftracing_context\x18\t \x03(\x0b\x32\x34.modal.client.ContainerArguments.TracingContextEntry\x12\x19\n\x11serialized_params\x18\n \x01(\x0c\x12\x0f\n\x07runtime\x18\x0b \x01(\t\x12\x18\n\x10\x65nvironment_name\x18\r \x01(\t\x12\x1a\n\rcheckpoint_id\x18\x0e \x01(\tH\x00\x88\x01\x01\x12+\n\napp_layout\x18\x0f \x01(\x0b\x32\x17.modal.client.AppLayout\x1a\x35\n\x13TracingContextEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x10\n\x0e_checkpoint_id\"3\n\x1a\x43ontainerCheckpointRequest\x12\x15\n\rcheckpoint_id\x18\x01 \x01(\t\"\xa9\x01\n\x1d\x43ontainerExecGetOutputRequest\x12\x0f\n\x07\x65xec_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\x12\x18\n\x10last_batch_index\x18\x03 \x01(\x04\x12\x35\n\x0f\x66ile_descriptor\x18\x04 \x01(\x0e\x32\x1c.modal.client.FileDescriptor\x12\x15\n\rget_raw_bytes\x18\x05 \x01(\x08\"a\n\x1c\x43ontainerExecPutInputRequest\x12\x0f\n\x07\x65xec_id\x18\x01 \x01(\t\x12\x30\n\x05input\x18\x02 \x01(\x0b\x32!.modal.client.RuntimeInputMessage\"\xed\x02\n\x14\x43ontainerExecRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63ommand\x18\x02 \x03(\t\x12,\n\x08pty_info\x18\x03 \x01(\x0b\x32\x15.modal.client.PTYInfoH\x00\x88\x01\x01\x12\'\n\x1bterminate_container_on_exit\x18\x04 \x01(\x08\x42\x02\x18\x01\x12\x15\n\rruntime_debug\x18\x05 \x01(\x08\x12\x35\n\rstdout_output\x18\x06 \x01(\x0e\x32\x1e.modal.client.ExecOutputOption\x12\x35\n\rstderr_output\x18\x07 \x01(\x0e\x32\x1e.modal.client.ExecOutputOption\x12\x14\n\x0ctimeout_secs\x18\x08 \x01(\r\x12\x14\n\x07workdir\x18\t \x01(\tH\x01\x88\x01\x01\x12\x12\n\nsecret_ids\x18\n \x03(\tB\x0b\n\t_pty_infoB\n\n\x08_workdir\"(\n\x15\x43ontainerExecResponse\x12\x0f\n\x07\x65xec_id\x18\x01 \x01(\t\"<\n\x18\x43ontainerExecWaitRequest\x12\x0f\n\x07\x65xec_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\"T\n\x19\x43ontainerExecWaitResponse\x12\x16\n\texit_code\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x11\n\tcompleted\x18\x02 \x01(\x08\x42\x0c\n\n_exit_code\"4\n\x19\x43ontainerFileCloseRequest\x12\x17\n\x0f\x66ile_descriptor\x18\x01 \x01(\t\"\x9a\x01\n\x1f\x43ontainerFileDeleteBytesRequest\x12\x17\n\x0f\x66ile_descriptor\x18\x01 \x01(\t\x12\x1c\n\x0fstart_inclusive\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x1a\n\rend_exclusive\x18\x03 \x01(\rH\x01\x88\x01\x01\x42\x12\n\x10_start_inclusiveB\x10\n\x0e_end_exclusive\"4\n\x19\x43ontainerFileFlushRequest\x12\x17\n\x0f\x66ile_descriptor\x18\x01 \x01(\t\"&\n\x16\x43ontainerFileLsRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\"?\n\x19\x43ontainerFileMkdirRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x14\n\x0cmake_parents\x18\x02 \x01(\x08\"h\n\x18\x43ontainerFileOpenRequest\x12\x1c\n\x0f\x66ile_descriptor\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x0c\n\x04mode\x18\x03 \x01(\tB\x12\n\x10_file_descriptor\"7\n\x1c\x43ontainerFileReadLineRequest\x12\x17\n\x0f\x66ile_descriptor\x18\x01 \x01(\t\"I\n\x18\x43ontainerFileReadRequest\x12\x17\n\x0f\x66ile_descriptor\x18\x01 \x01(\t\x12\x0e\n\x01n\x18\x02 \x01(\rH\x00\x88\x01\x01\x42\x04\n\x02_n\"9\n\x16\x43ontainerFileRmRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x11\n\trecursive\x18\x02 \x01(\x08\"m\n\x18\x43ontainerFileSeekRequest\x12\x17\n\x0f\x66ile_descriptor\x18\x01 \x01(\t\x12\x0e\n\x06offset\x18\x02 \x01(\x05\x12(\n\x06whence\x18\x03 \x01(\x0e\x32\x18.modal.client.SeekWhence\"h\n\x19\x43ontainerFileWatchRequest\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x11\n\trecursive\x18\x02 \x01(\x08\x12\x19\n\x0ctimeout_secs\x18\x03 \x01(\x04H\x00\x88\x01\x01\x42\x0f\n\r_timeout_secs\"\xae\x01\n%ContainerFileWriteReplaceBytesRequest\x12\x17\n\x0f\x66ile_descriptor\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\x12\x1c\n\x0fstart_inclusive\x18\x03 \x01(\rH\x00\x88\x01\x01\x12\x1a\n\rend_exclusive\x18\x04 \x01(\rH\x01\x88\x01\x01\x42\x12\n\x10_start_inclusiveB\x10\n\x0e_end_exclusive\"B\n\x19\x43ontainerFileWriteRequest\x12\x17\n\x0f\x66ile_descriptor\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\"K\n\'ContainerFilesystemExecGetOutputRequest\x12\x0f\n\x07\x65xec_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\"\x83\x08\n\x1e\x43ontainerFilesystemExecRequest\x12\x43\n\x11\x66ile_open_request\x18\x01 \x01(\x0b\x32&.modal.client.ContainerFileOpenRequestH\x00\x12\x45\n\x12\x66ile_write_request\x18\x02 \x01(\x0b\x32\'.modal.client.ContainerFileWriteRequestH\x00\x12\x43\n\x11\x66ile_read_request\x18\x03 \x01(\x0b\x32&.modal.client.ContainerFileReadRequestH\x00\x12\x45\n\x12\x66ile_flush_request\x18\x04 \x01(\x0b\x32\'.modal.client.ContainerFileFlushRequestH\x00\x12L\n\x16\x66ile_read_line_request\x18\x05 \x01(\x0b\x32*.modal.client.ContainerFileReadLineRequestH\x00\x12\x43\n\x11\x66ile_seek_request\x18\x06 \x01(\x0b\x32&.modal.client.ContainerFileSeekRequestH\x00\x12R\n\x19\x66ile_delete_bytes_request\x18\x07 \x01(\x0b\x32-.modal.client.ContainerFileDeleteBytesRequestH\x00\x12_\n file_write_replace_bytes_request\x18\x08 \x01(\x0b\x32\x33.modal.client.ContainerFileWriteReplaceBytesRequestH\x00\x12\x45\n\x12\x66ile_close_request\x18\t \x01(\x0b\x32\'.modal.client.ContainerFileCloseRequestH\x00\x12?\n\x0f\x66ile_ls_request\x18\x0b \x01(\x0b\x32$.modal.client.ContainerFileLsRequestH\x00\x12\x45\n\x12\x66ile_mkdir_request\x18\x0c \x01(\x0b\x32\'.modal.client.ContainerFileMkdirRequestH\x00\x12?\n\x0f\x66ile_rm_request\x18\r \x01(\x0b\x32$.modal.client.ContainerFileRmRequestH\x00\x12\x45\n\x12\x66ile_watch_request\x18\x0e \x01(\x0b\x32\'.modal.client.ContainerFileWatchRequestH\x00\x12\x0f\n\x07task_id\x18\n \x01(\tB\x19\n\x17\x66ile_exec_request_oneof\"d\n\x1f\x43ontainerFilesystemExecResponse\x12\x0f\n\x07\x65xec_id\x18\x01 \x01(\t\x12\x1c\n\x0f\x66ile_descriptor\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\x12\n\x10_file_descriptor\"\x80\x01\n\x19\x43ontainerHeartbeatRequest\x12&\n\x1e\x63\x61nceled_inputs_return_outputs\x18\x04 \x01(\x08\x12)\n!canceled_inputs_return_outputs_v2\x18\x05 \x01(\x08J\x04\x08\x01\x10\x02J\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04\"t\n\x1a\x43ontainerHeartbeatResponse\x12?\n\x12\x63\x61ncel_input_event\x18\x01 \x01(\x0b\x32\x1e.modal.client.CancelInputEventH\x00\x88\x01\x01\x42\x15\n\x13_cancel_input_event\";\n\x13\x43ontainerLogRequest\x12$\n\x04logs\x18\x03 \x03(\x0b\x32\x16.modal.client.TaskLogs\"-\n\x14\x43ontainerStopRequest\x12\x15\n\x07task_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\"\x17\n\x15\x43ontainerStopResponse\"\"\n\x12\x43ustomDomainConfig\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x1f\n\x10\x43ustomDomainInfo\x12\x0b\n\x03url\x18\x01 \x01(\t\"S\n\tDNSRecord\x12)\n\x04type\x18\x01 \x01(\x0e\x32\x1b.modal.client.DNSRecordType\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\"\x7f\n\tDataChunk\x12-\n\x0b\x64\x61ta_format\x18\x01 \x01(\x0e\x32\x18.modal.client.DataFormat\x12\x0e\n\x04\x64\x61ta\x18\x02 \x01(\x0cH\x00\x12\x16\n\x0c\x64\x61ta_blob_id\x18\x03 \x01(\tH\x00\x12\r\n\x05index\x18\x04 \x01(\x04\x42\x0c\n\ndata_oneof\"#\n\x10\x44ictClearRequest\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\"3\n\x13\x44ictContainsRequest\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\x0c\"%\n\x14\x44ictContainsResponse\x12\r\n\x05\x66ound\x18\x01 \x01(\x08\"D\n\x13\x44ictContentsRequest\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\x12\x0c\n\x04keys\x18\x02 \x01(\x08\x12\x0e\n\x06values\x18\x03 \x01(\x08\"$\n\x11\x44ictDeleteRequest\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\"\'\n\tDictEntry\x12\x0b\n\x03key\x18\x01 \x01(\x0c\x12\r\n\x05value\x18\x02 \x01(\x0c\"\xee\x01\n\x16\x44ictGetOrCreateRequest\x12\x1d\n\x0f\x64\x65ployment_name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x34\n\tnamespace\x18\x02 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12>\n\x14object_creation_type\x18\x04 \x01(\x0e\x32 .modal.client.ObjectCreationType\x12%\n\x04\x64\x61ta\x18\x05 \x03(\x0b\x32\x17.modal.client.DictEntry\"*\n\x17\x44ictGetOrCreateResponse\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\".\n\x0e\x44ictGetRequest\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\x0c\">\n\x0f\x44ictGetResponse\x12\r\n\x05\x66ound\x18\x01 \x01(\x08\x12\x12\n\x05value\x18\x02 \x01(\x0cH\x00\x88\x01\x01\x42\x08\n\x06_value\"\'\n\x14\x44ictHeartbeatRequest\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\"!\n\x0e\x44ictLenRequest\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\"\x1e\n\x0f\x44ictLenResponse\x12\x0b\n\x03len\x18\x01 \x01(\x05\"+\n\x0f\x44ictListRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\"x\n\x10\x44ictListResponse\x12\x36\n\x05\x64icts\x18\x01 \x03(\x0b\x32\'.modal.client.DictListResponse.DictInfo\x1a,\n\x08\x44ictInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ncreated_at\x18\x02 \x01(\x01\".\n\x0e\x44ictPopRequest\x12\x0f\n\x07\x64ict_id\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\x0c\">\n\x0f\x44ictPopResponse\x12\r\n\x05\x66ound\x18\x01 \x01(\x08\x12\x12\n\x05value\x18\x02 \x01(\x0cH\x00\x88\x01\x01\x42\x08\n\x06_value\"k\n\x11\x44ictUpdateRequest\x12\x15\n\x07\x64ict_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12(\n\x07updates\x18\x02 \x03(\x0b\x32\x17.modal.client.DictEntry\x12\x15\n\rif_not_exists\x18\x03 \x01(\x08\"%\n\x12\x44ictUpdateResponse\x12\x0f\n\x07\x63reated\x18\x01 \x01(\x08\"\xaf\x01\n\x06\x44omain\x12\x11\n\tdomain_id\x18\x01 \x01(\t\x12\x13\n\x0b\x64omain_name\x18\x02 \x01(\t\x12\x12\n\ncreated_at\x18\x03 \x01(\x01\x12;\n\x12\x63\x65rtificate_status\x18\x04 \x01(\x0e\x32\x1f.modal.client.CertificateStatus\x12,\n\x0b\x64ns_records\x18\x05 \x03(\x0b\x32\x17.modal.client.DNSRecord\"3\n\x1e\x44omainCertificateVerifyRequest\x12\x11\n\tdomain_id\x18\x01 \x01(\t\"G\n\x1f\x44omainCertificateVerifyResponse\x12$\n\x06\x64omain\x18\x01 \x01(\x0b\x32\x14.modal.client.Domain\"0\n\x13\x44omainCreateRequest\x12\x19\n\x0b\x64omain_name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\"W\n\x14\x44omainCreateResponse\x12\x11\n\tdomain_id\x18\x01 \x01(\t\x12,\n\x0b\x64ns_records\x18\x02 \x03(\x0b\x32\x17.modal.client.DNSRecord\"\x13\n\x11\x44omainListRequest\";\n\x12\x44omainListResponse\x12%\n\x07\x64omains\x18\x01 \x03(\x0b\x32\x14.modal.client.Domain\".\n\x18\x45nvironmentCreateRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\".\n\x18\x45nvironmentDeleteRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\"~\n\x1d\x45nvironmentGetOrCreateRequest\x12\x1d\n\x0f\x64\x65ployment_name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12>\n\x14object_creation_type\x18\x02 \x01(\x0e\x32 .modal.client.ObjectCreationType\"m\n\x1e\x45nvironmentGetOrCreateResponse\x12\x16\n\x0e\x65nvironment_id\x18\x01 \x01(\t\x12\x33\n\x08metadata\x18\x02 \x01(\x0b\x32!.modal.client.EnvironmentMetadata\"`\n\x13\x45nvironmentListItem\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x16\n\x0ewebhook_suffix\x18\x02 \x01(\t\x12\x12\n\ncreated_at\x18\x03 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x04 \x01(\x08\"K\n\x17\x45nvironmentListResponse\x12\x30\n\x05items\x18\x02 \x03(\x0b\x32!.modal.client.EnvironmentListItem\"X\n\x13\x45nvironmentMetadata\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x33\n\x08settings\x18\x02 \x01(\x0b\x32!.modal.client.EnvironmentSettings\"L\n\x13\x45nvironmentSettings\x12\x1d\n\x15image_builder_version\x18\x01 \x01(\t\x12\x16\n\x0ewebhook_suffix\x18\x02 \x01(\t\"\x94\x01\n\x18\x45nvironmentUpdateRequest\x12\x1a\n\x0c\x63urrent_name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12*\n\x04name\x18\x02 \x01(\x0b\x32\x1c.google.protobuf.StringValue\x12\x30\n\nweb_suffix\x18\x03 \x01(\x0b\x32\x1c.google.protobuf.StringValue\"\xbf\x01\n\tFileEntry\x12\x0c\n\x04path\x18\x01 \x01(\t\x12.\n\x04type\x18\x02 \x01(\x0e\x32 .modal.client.FileEntry.FileType\x12\r\n\x05mtime\x18\x03 \x01(\x04\x12\x0c\n\x04size\x18\x04 \x01(\x04\"W\n\x08\x46ileType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x08\n\x04\x46ILE\x10\x01\x12\r\n\tDIRECTORY\x10\x02\x12\x0b\n\x07SYMLINK\x10\x03\x12\x08\n\x04\x46IFO\x10\x04\x12\n\n\x06SOCKET\x10\x05\"\x90\x01\n\x1c\x46ilesystemRuntimeOutputBatch\x12\x0e\n\x06output\x18\x01 \x03(\x0c\x12\x34\n\x05\x65rror\x18\x02 \x01(\x0b\x32 .modal.client.SystemErrorMessageH\x00\x88\x01\x01\x12\x13\n\x0b\x62\x61tch_index\x18\x03 \x01(\x04\x12\x0b\n\x03\x65of\x18\x04 \x01(\x08\x42\x08\n\x06_error\"\x8e\x18\n\x08\x46unction\x12\x13\n\x0bmodule_name\x18\x01 \x01(\t\x12\x15\n\rfunction_name\x18\x02 \x01(\t\x12\x11\n\tmount_ids\x18\x03 \x03(\t\x12\x10\n\x08image_id\x18\x04 \x01(\t\x12\x1b\n\x13\x66unction_serialized\x18\x06 \x01(\x0c\x12>\n\x0f\x64\x65\x66inition_type\x18\x07 \x01(\x0e\x32%.modal.client.Function.DefinitionType\x12:\n\rfunction_type\x18\x08 \x01(\x0e\x32#.modal.client.Function.FunctionType\x12*\n\tresources\x18\t \x01(\x0b\x32\x17.modal.client.Resources\x12\x12\n\nsecret_ids\x18\n \x03(\t\x12+\n\nrate_limit\x18\x0b \x01(\x0b\x32\x17.modal.client.RateLimit\x12\x33\n\x0ewebhook_config\x18\x0f \x01(\x0b\x32\x1b.modal.client.WebhookConfig\x12=\n\x14shared_volume_mounts\x18\x10 \x03(\x0b\x32\x1f.modal.client.SharedVolumeMount\x12\x15\n\x08proxy_id\x18\x11 \x01(\tH\x00\x88\x01\x01\x12\x37\n\x0cretry_policy\x18\x12 \x01(\x0b\x32!.modal.client.FunctionRetryPolicy\x12\x19\n\x11\x63oncurrency_limit\x18\x13 \x01(\r\x12\x14\n\x0ctimeout_secs\x18\x15 \x01(\r\x12\'\n\x08pty_info\x18\x16 \x01(\x0b\x32\x15.modal.client.PTYInfo\x12\x18\n\x10\x63lass_serialized\x18\x17 \x01(\x0c\x12\x1e\n\x16task_idle_timeout_secs\x18\x19 \x01(\r\x12\x38\n\x0e\x63loud_provider\x18\x1a \x01(\x0e\x32\x1b.modal.client.CloudProviderH\x01\x88\x01\x01\x12\x16\n\x0ewarm_pool_size\x18\x1b \x01(\r\x12\x0f\n\x07web_url\x18\x1c \x01(\t\x12.\n\x0cweb_url_info\x18\x1d \x01(\x0b\x32\x18.modal.client.WebUrlInfo\x12\x0f\n\x07runtime\x18\x1e \x01(\t\x12\x10\n\x08\x61pp_name\x18\x1f \x01(\t\x12\x30\n\rvolume_mounts\x18! \x03(\x0b\x32\x19.modal.client.VolumeMount\x12\x1d\n\x15max_concurrent_inputs\x18\" \x01(\r\x12:\n\x12\x63ustom_domain_info\x18# \x03(\x0b\x32\x1e.modal.client.CustomDomainInfo\x12\x11\n\tworker_id\x18$ \x01(\t\x12\x15\n\rruntime_debug\x18% \x01(\x08\x12\x1b\n\x13is_builder_function\x18  \x01(\x08\x12\x18\n\x10is_auto_snapshot\x18& \x01(\x08\x12\x11\n\tis_method\x18\' \x01(\x08\x12!\n\x19is_checkpointing_function\x18( \x01(\x08\x12\x1d\n\x15\x63heckpointing_enabled\x18) \x01(\x08\x12\x30\n\ncheckpoint\x18* \x01(\x0b\x32\x1c.modal.client.CheckpointInfo\x12;\n\x13object_dependencies\x18+ \x03(\x0b\x32\x1e.modal.client.ObjectDependency\x12\x15\n\rblock_network\x18, \x01(\x08\x12\x12\n\nmax_inputs\x18. \x01(\r\x12(\n\ts3_mounts\x18/ \x03(\x0b\x32\x15.modal.client.S3Mount\x12;\n\x13\x63loud_bucket_mounts\x18\x33 \x03(\x0b\x32\x1e.modal.client.CloudBucketMount\x12\x42\n\x13scheduler_placement\x18\x32 \x01(\x0b\x32 .modal.client.SchedulerPlacementH\x02\x88\x01\x01\x12\x10\n\x08is_class\x18\x35 \x01(\x08\x12\x17\n\x0fuse_function_id\x18\x36 \x01(\t\x12\x17\n\x0fuse_method_name\x18\x37 \x01(\t\x12>\n\x14\x63lass_parameter_info\x18\x38 \x01(\x0b\x32 .modal.client.ClassParameterInfo\x12\x16\n\x0e\x62\x61tch_max_size\x18< \x01(\r\x12\x17\n\x0f\x62\x61tch_linger_ms\x18= \x01(\x04\x12\x14\n\x0ci6pn_enabled\x18> \x01(\x08\x12.\n&_experimental_concurrent_cancellations\x18? \x01(\x08\x12 \n\x18target_concurrent_inputs\x18@ \x01(\r\x12,\n$_experimental_task_templates_enabled\x18\x41 \x01(\x08\x12@\n\x1c_experimental_task_templates\x18\x42 \x03(\x0b\x32\x1a.modal.client.TaskTemplate\x12 \n\x18_experimental_group_size\x18\x43 \x01(\r\x12\x11\n\tuntrusted\x18\x44 \x01(\x08\x12\'\n\x1f_experimental_buffer_containers\x18\x45 \x01(\r\x12#\n\x16_experimental_proxy_ip\x18\x46 \x01(\tH\x03\x88\x01\x01\x12\x1b\n\x13runtime_perf_record\x18G \x01(\x08\x12(\n\x08schedule\x18H \x01(\x0b\x32\x16.modal.client.Schedule\x12\x16\n\x0esnapshot_debug\x18I \x01(\x08\x12I\n\x12method_definitions\x18J \x03(\x0b\x32-.modal.client.Function.MethodDefinitionsEntry\x12\x1e\n\x16method_definitions_set\x18K \x01(\x08\x12$\n\x1c_experimental_custom_scaling\x18L \x01(\x08\x12\x1a\n\x12\x63loud_provider_str\x18M \x01(\t\x12)\n!_experimental_enable_gpu_snapshot\x18N \x01(\x08\x12=\n\x13\x61utoscaler_settings\x18O \x01(\x0b\x32 .modal.client.AutoscalerSettings\x12\x35\n\x0f\x66unction_schema\x18P \x01(\x0b\x32\x1c.modal.client.FunctionSchema\x12M\n\x14\x65xperimental_options\x18Q \x03(\x0b\x32/.modal.client.Function.ExperimentalOptionsEntry\x12!\n\x19mount_client_dependencies\x18R \x01(\x08\x1aX\n\x16MethodDefinitionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.modal.client.MethodDefinition:\x02\x38\x01\x1a:\n\x18\x45xperimentalOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"k\n\x0e\x44\x65\x66initionType\x12\x1f\n\x1b\x44\x45\x46INITION_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x44\x45\x46INITION_TYPE_SERIALIZED\x10\x01\x12\x18\n\x14\x44\x45\x46INITION_TYPE_FILE\x10\x02\"f\n\x0c\x46unctionType\x12\x1d\n\x19\x46UNCTION_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x46UNCTION_TYPE_GENERATOR\x10\x01\x12\x1a\n\x16\x46UNCTION_TYPE_FUNCTION\x10\x02\x42\x0b\n\t_proxy_idB\x11\n\x0f_cloud_providerB\x16\n\x14_scheduler_placementB\x19\n\x17X_experimental_proxy_ipJ\x04\x08\x14\x10\x15J\x04\x08\x30\x10\x31J\x04\x08\x31\x10\x32J\x04\x08\x34\x10\x35J\x04\x08\x39\x10:J\x04\x08:\x10;J\x04\x08;\x10<\"v\n\x1a\x46unctionAsyncInvokeRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x17\n\x0fparent_input_id\x18\x02 \x01(\t\x12*\n\x05input\x18\x03 \x01(\x0b\x32\x1b.modal.client.FunctionInput\"W\n\x1b\x46unctionAsyncInvokeResponse\x12\x1e\n\x16retry_with_blob_upload\x18\x01 \x01(\x08\x12\x18\n\x10\x66unction_call_id\x18\x02 \x01(\t\"\x9e\x01\n\x19\x46unctionBindParamsRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x19\n\x11serialized_params\x18\x02 \x01(\x0c\x12\x37\n\x10\x66unction_options\x18\x03 \x01(\x0b\x32\x1d.modal.client.FunctionOptions\x12\x18\n\x10\x65nvironment_name\x18\x04 \x01(\t\"v\n\x1a\x46unctionBindParamsResponse\x12\x19\n\x11\x62ound_function_id\x18\x01 \x01(\t\x12=\n\x0fhandle_metadata\x18\x02 \x01(\x0b\x32$.modal.client.FunctionHandleMetadata\"z\n\x19\x46unctionCallCallGraphInfo\x12\x18\n\x10\x66unction_call_id\x18\x01 \x01(\t\x12\x17\n\x0fparent_input_id\x18\x02 \x01(\t\x12\x15\n\rfunction_name\x18\x03 \x01(\t\x12\x13\n\x0bmodule_name\x18\x04 \x01(\t\"S\n\x19\x46unctionCallCancelRequest\x12\x18\n\x10\x66unction_call_id\x18\x01 \x01(\t\x12\x1c\n\x14terminate_containers\x18\x02 \x01(\x08\"J\n\x1a\x46unctionCallGetDataRequest\x12\x18\n\x10\x66unction_call_id\x18\x01 \x01(\t\x12\x12\n\nlast_index\x18\x02 \x01(\x04\"\xc3\x03\n\x10\x46unctionCallInfo\x12\x18\n\x10\x66unction_call_id\x18\x01 \x01(\t\x12\x0b\n\x03idx\x18\x02 \x01(\x05\x12\x12\n\ncreated_at\x18\x06 \x01(\x01\x12\x14\n\x0cscheduled_at\x18\x07 \x01(\x01\x12\x37\n\x0epending_inputs\x18\x0c \x01(\x0b\x32\x1f.modal.client.InputCategoryInfo\x12\x36\n\rfailed_inputs\x18\r \x01(\x0b\x32\x1f.modal.client.InputCategoryInfo\x12\x39\n\x10succeeded_inputs\x18\x0e \x01(\x0b\x32\x1f.modal.client.InputCategoryInfo\x12\x37\n\x0etimeout_inputs\x18\x0f \x01(\x0b\x32\x1f.modal.client.InputCategoryInfo\x12\x39\n\x10\x63\x61ncelled_inputs\x18\x10 \x01(\x0b\x32\x1f.modal.client.InputCategoryInfo\x12\x14\n\x0ctotal_inputs\x18\x11 \x01(\x05J\x04\x08\x03\x10\x04J\x04\x08\x04\x10\x05J\x04\x08\x05\x10\x06J\x04\x08\x08\x10\tJ\x04\x08\t\x10\nJ\x04\x08\n\x10\x0bJ\x04\x08\x0b\x10\x0c\".\n\x17\x46unctionCallListRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\"R\n\x18\x46unctionCallListResponse\x12\x36\n\x0e\x66unction_calls\x18\x01 \x03(\x0b\x32\x1e.modal.client.FunctionCallInfo\"d\n\x1a\x46unctionCallPutDataRequest\x12\x18\n\x10\x66unction_call_id\x18\x01 \x01(\t\x12,\n\x0b\x64\x61ta_chunks\x18\x02 \x03(\x0b\x32\x17.modal.client.DataChunk\"\xdc\x01\n\x15\x46unctionCreateRequest\x12(\n\x08\x66unction\x18\x01 \x01(\x0b\x32\x16.modal.client.Function\x12\x14\n\x06\x61pp_id\x18\x02 \x01(\tB\x04\x80\xb5\x18\x01\x12,\n\x08schedule\x18\x06 \x01(\x0b\x32\x16.modal.client.ScheduleB\x02\x18\x01\x12\x1c\n\x14\x65xisting_function_id\x18\x07 \x01(\t\x12\x31\n\rfunction_data\x18\t \x01(\x0b\x32\x1a.modal.client.FunctionDataJ\x04\x08\x08\x10\t\"\xe8\x01\n\x16\x46unctionCreateResponse\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12 \n\x14__deprecated_web_url\x18\x02 \x01(\tB\x02\x18\x01\x12(\n\x08\x66unction\x18\x04 \x01(\x0b\x32\x16.modal.client.Function\x12=\n\x0fhandle_metadata\x18\x05 \x01(\x0b\x32$.modal.client.FunctionHandleMetadata\x12.\n\x0fserver_warnings\x18\x06 \x03(\x0b\x32\x15.modal.client.Warning\"\xec\x0b\n\x0c\x46unctionData\x12\x13\n\x0bmodule_name\x18\x01 \x01(\t\x12\x15\n\rfunction_name\x18\x02 \x01(\t\x12:\n\rfunction_type\x18\x03 \x01(\x0e\x32#.modal.client.Function.FunctionType\x12\x16\n\x0ewarm_pool_size\x18\x04 \x01(\r\x12\x19\n\x11\x63oncurrency_limit\x18\x05 \x01(\r\x12\x1e\n\x16task_idle_timeout_secs\x18\x06 \x01(\r\x12 \n\x18_experimental_group_size\x18\x13 \x01(\r\x12\'\n\x1f_experimental_buffer_containers\x18\x16 \x01(\r\x12$\n\x1c_experimental_custom_scaling\x18\x17 \x01(\x08\x12)\n!_experimental_enable_gpu_snapshot\x18\x1e \x01(\x08\x12\x11\n\tworker_id\x18\x07 \x01(\t\x12\x14\n\x0ctimeout_secs\x18\x08 \x01(\r\x12\x0f\n\x07web_url\x18\t \x01(\t\x12.\n\x0cweb_url_info\x18\n \x01(\x0b\x32\x18.modal.client.WebUrlInfo\x12\x33\n\x0ewebhook_config\x18\x0b \x01(\x0b\x32\x1b.modal.client.WebhookConfig\x12:\n\x12\x63ustom_domain_info\x18\x0c \x03(\x0b\x32\x1e.modal.client.CustomDomainInfo\x12#\n\x16_experimental_proxy_ip\x18\x18 \x01(\tH\x00\x88\x01\x01\x12M\n\x12method_definitions\x18\x19 \x03(\x0b\x32\x31.modal.client.FunctionData.MethodDefinitionsEntry\x12\x1e\n\x16method_definitions_set\x18\x1a \x01(\x08\x12\x10\n\x08is_class\x18\r \x01(\x08\x12>\n\x14\x63lass_parameter_info\x18\x0e \x01(\x0b\x32 .modal.client.ClassParameterInfo\x12\x11\n\tis_method\x18\x0f \x01(\x08\x12\x17\n\x0fuse_function_id\x18\x10 \x01(\t\x12\x17\n\x0fuse_method_name\x18\x11 \x01(\t\x12\x43\n\x10ranked_functions\x18\x12 \x03(\x0b\x32).modal.client.FunctionData.RankedFunction\x12(\n\x08schedule\x18\x14 \x01(\x0b\x32\x16.modal.client.Schedule\x12\x11\n\tuntrusted\x18\x1b \x01(\x08\x12\x16\n\x0esnapshot_debug\x18\x1c \x01(\x08\x12\x1b\n\x13runtime_perf_record\x18\x1d \x01(\x08\x12=\n\x13\x61utoscaler_settings\x18\x1f \x01(\x0b\x32 .modal.client.AutoscalerSettings\x12\x35\n\x0f\x66unction_schema\x18  \x01(\x0b\x32\x1c.modal.client.FunctionSchema\x12Q\n\x14\x65xperimental_options\x18! \x03(\x0b\x32\x33.modal.client.FunctionData.ExperimentalOptionsEntry\x1aX\n\x16MethodDefinitionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.modal.client.MethodDefinition:\x02\x38\x01\x1aH\n\x0eRankedFunction\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12(\n\x08\x66unction\x18\x02 \x01(\x0b\x32\x16.modal.client.Function\x1a:\n\x18\x45xperimentalOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x19\n\x17X_experimental_proxy_ipJ\x04\x08\x15\x10\x16\"\xab\x01\n\x10\x46unctionExtended\x12\x17\n\x0ftype_identifier\x18\x01 \x01(\r\x12\x34\n\x12\x66unction_singleton\x18\x02 \x01(\x0b\x32\x16.modal.client.FunctionH\x00\x12\x33\n\rfunction_data\x18\x03 \x01(\x0b\x32\x1a.modal.client.FunctionDataH\x00\x42\x13\n\x11\x66unction_extended\"7\n\x1b\x46unctionGetCallGraphRequest\x12\x18\n\x10\x66unction_call_id\x18\x02 \x01(\t\"\x91\x01\n\x1c\x46unctionGetCallGraphResponse\x12\x30\n\x06inputs\x18\x01 \x03(\x0b\x32 .modal.client.InputCallGraphInfo\x12?\n\x0e\x66unction_calls\x18\x02 \x03(\x0b\x32\'.modal.client.FunctionCallCallGraphInfo\"5\n\x1e\x46unctionGetCurrentStatsRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\"p\n$FunctionGetDynamicConcurrencyRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x1a\n\x12target_concurrency\x18\x02 \x01(\r\x12\x17\n\x0fmax_concurrency\x18\x03 \x01(\r\"<\n%FunctionGetDynamicConcurrencyResponse\x12\x13\n\x0b\x63oncurrency\x18\x01 \x01(\r\"\xf0\x01\n\x15\x46unctionGetInputsItem\x12\x10\n\x08input_id\x18\x01 \x01(\t\x12*\n\x05input\x18\x02 \x01(\x0b\x32\x1b.modal.client.FunctionInput\x12\x13\n\x0bkill_switch\x18\x03 \x01(\x08\x12\x18\n\x10\x66unction_call_id\x18\x05 \x01(\t\x12O\n\x1d\x66unction_call_invocation_type\x18\x06 \x01(\x0e\x32(.modal.client.FunctionCallInvocationType\x12\x13\n\x0bretry_count\x18\x07 \x01(\rJ\x04\x08\x04\x10\x05\"\xb6\x01\n\x18\x46unctionGetInputsRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x12\n\nmax_values\x18\x03 \x01(\x05\x12\x19\n\x11\x61verage_call_time\x18\x05 \x01(\x02\x12\x19\n\x11input_concurrency\x18\x06 \x01(\x05\x12\x16\n\x0e\x62\x61tch_max_size\x18\x0b \x01(\r\x12\x17\n\x0f\x62\x61tch_linger_ms\x18\x0c \x01(\x04J\x04\x08\t\x10\nJ\x04\x08\n\x10\x0b\"s\n\x19\x46unctionGetInputsResponse\x12\x33\n\x06inputs\x18\x03 \x03(\x0b\x32#.modal.client.FunctionGetInputsItem\x12!\n\x19rate_limit_sleep_duration\x18\x04 \x01(\x02\"\x84\x02\n\x16\x46unctionGetOutputsItem\x12+\n\x06result\x18\x01 \x01(\x0b\x32\x1b.modal.client.GenericResult\x12\x0b\n\x03idx\x18\x02 \x01(\x05\x12\x10\n\x08input_id\x18\x03 \x01(\t\x12-\n\x0b\x64\x61ta_format\x18\x05 \x01(\x0e\x32\x18.modal.client.DataFormat\x12\x0f\n\x07task_id\x18\x06 \x01(\t\x12\x18\n\x10input_started_at\x18\x07 \x01(\x01\x12\x19\n\x11output_created_at\x18\x08 \x01(\x01\x12\x13\n\x0bretry_count\x18\t \x01(\r\x12\x14\n\x0c\x66\x63_trace_tag\x18\n \x01(\t\"\xb5\x01\n\x19\x46unctionGetOutputsRequest\x12\x18\n\x10\x66unction_call_id\x18\x01 \x01(\t\x12\x12\n\nmax_values\x18\x02 \x01(\x05\x12\x0f\n\x07timeout\x18\x03 \x01(\x02\x12\x15\n\rlast_entry_id\x18\x06 \x01(\t\x12\x18\n\x10\x63lear_on_success\x18\x07 \x01(\x08\x12\x14\n\x0crequested_at\x18\x08 \x01(\x01\x12\x12\n\ninput_jwts\x18\t \x03(\t\"\x97\x01\n\x1a\x46unctionGetOutputsResponse\x12\x0c\n\x04idxs\x18\x03 \x03(\x05\x12\x35\n\x07outputs\x18\x04 \x03(\x0b\x32$.modal.client.FunctionGetOutputsItem\x12\x15\n\rlast_entry_id\x18\x05 \x01(\t\x12\x1d\n\x15num_unfinished_inputs\x18\x06 \x01(\x05\"\x8a\x01\n\x12\x46unctionGetRequest\x12\x10\n\x08\x61pp_name\x18\x01 \x01(\t\x12\x12\n\nobject_tag\x18\x02 \x01(\t\x12\x34\n\tnamespace\x18\x03 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x04 \x01(\t\"\x99\x01\n\x13\x46unctionGetResponse\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12=\n\x0fhandle_metadata\x18\x02 \x01(\x0b\x32$.modal.client.FunctionHandleMetadata\x12.\n\x0fserver_warnings\x18\x04 \x03(\x0b\x32\x15.modal.client.Warning\"3\n\x1c\x46unctionGetSerializedRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\"V\n\x1d\x46unctionGetSerializedResponse\x12\x1b\n\x13\x66unction_serialized\x18\x01 \x01(\x0c\x12\x18\n\x10\x63lass_serialized\x18\x02 \x01(\x0c\"\xc4\x04\n\x16\x46unctionHandleMetadata\x12\x15\n\rfunction_name\x18\x02 \x01(\t\x12:\n\rfunction_type\x18\x08 \x01(\x0e\x32#.modal.client.Function.FunctionType\x12\x0f\n\x07web_url\x18\x1c \x01(\t\x12\x11\n\tis_method\x18\' \x01(\x08\x12\x17\n\x0fuse_function_id\x18( \x01(\t\x12\x17\n\x0fuse_method_name\x18) \x01(\t\x12\x15\n\rdefinition_id\x18* \x01(\t\x12>\n\x14\x63lass_parameter_info\x18+ \x01(\x0b\x32 .modal.client.ClassParameterInfo\x12^\n\x16method_handle_metadata\x18, \x03(\x0b\x32>.modal.client.FunctionHandleMetadata.MethodHandleMetadataEntry\x12\x35\n\x0f\x66unction_schema\x18- \x01(\x0b\x32\x1c.modal.client.FunctionSchema\x12\x1c\n\x0finput_plane_url\x18. \x01(\tH\x00\x88\x01\x01\x1a\x61\n\x19MethodHandleMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x33\n\x05value\x18\x02 \x01(\x0b\x32$.modal.client.FunctionHandleMetadata:\x02\x38\x01\x42\x12\n\x10_input_plane_url\"\xb3\x01\n\rFunctionInput\x12\x0e\n\x04\x61rgs\x18\x01 \x01(\x0cH\x00\x12\x16\n\x0c\x61rgs_blob_id\x18\x07 \x01(\tH\x00\x12\x13\n\x0b\x66inal_input\x18\t \x01(\x08\x12-\n\x0b\x64\x61ta_format\x18\n \x01(\x0e\x32\x18.modal.client.DataFormat\x12\x18\n\x0bmethod_name\x18\x0b \x01(\tH\x01\x88\x01\x01\x42\x0c\n\nargs_oneofB\x0e\n\x0c_method_name\"\xc1\x02\n\x12\x46unctionMapRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x17\n\x0fparent_input_id\x18\x02 \x01(\t\x12\x19\n\x11return_exceptions\x18\x03 \x01(\x08\x12:\n\x12\x66unction_call_type\x18\x04 \x01(\x0e\x32\x1e.modal.client.FunctionCallType\x12=\n\x10pipelined_inputs\x18\x05 \x03(\x0b\x32#.modal.client.FunctionPutInputsItem\x12O\n\x1d\x66unction_call_invocation_type\x18\x06 \x01(\x0e\x32(.modal.client.FunctionCallInvocationType\x12\x16\n\x0e\x66rom_spawn_map\x18\x07 \x01(\x08\"\x8f\x02\n\x13\x46unctionMapResponse\x12\x18\n\x10\x66unction_call_id\x18\x01 \x01(\t\x12\x45\n\x10pipelined_inputs\x18\x02 \x03(\x0b\x32+.modal.client.FunctionPutInputsResponseItem\x12\x37\n\x0cretry_policy\x18\x03 \x01(\x0b\x32!.modal.client.FunctionRetryPolicy\x12\x19\n\x11\x66unction_call_jwt\x18\x04 \x01(\t\x12#\n\x1bsync_client_retries_enabled\x18\x05 \x01(\x08\x12\x1e\n\x16max_inputs_outstanding\x18\x06 \x01(\r\"\x9f\x06\n\x0f\x46unctionOptions\x12\x12\n\nsecret_ids\x18\x01 \x03(\t\x12\x11\n\tmount_ids\x18\x02 \x03(\t\x12/\n\tresources\x18\x03 \x01(\x0b\x32\x17.modal.client.ResourcesH\x00\x88\x01\x01\x12<\n\x0cretry_policy\x18\x04 \x01(\x0b\x32!.modal.client.FunctionRetryPolicyH\x01\x88\x01\x01\x12\x1e\n\x11\x63oncurrency_limit\x18\x05 \x01(\rH\x02\x88\x01\x01\x12\x19\n\x0ctimeout_secs\x18\x06 \x01(\rH\x03\x88\x01\x01\x12#\n\x16task_idle_timeout_secs\x18\x07 \x01(\rH\x04\x88\x01\x01\x12\x1b\n\x0ewarm_pool_size\x18\x08 \x01(\rH\x05\x88\x01\x01\x12\x30\n\rvolume_mounts\x18\t \x03(\x0b\x32\x19.modal.client.VolumeMount\x12%\n\x18target_concurrent_inputs\x18\n \x01(\rH\x06\x88\x01\x01\x12\x1d\n\x15replace_volume_mounts\x18\x0b \x01(\x08\x12\x1a\n\x12replace_secret_ids\x18\x0c \x01(\x08\x12\x1e\n\x11\x62uffer_containers\x18\r \x01(\rH\x07\x88\x01\x01\x12\"\n\x15max_concurrent_inputs\x18\x0e \x01(\rH\x08\x88\x01\x01\x12\x1b\n\x0e\x62\x61tch_max_size\x18\x0f \x01(\rH\t\x88\x01\x01\x12\x1c\n\x0f\x62\x61tch_linger_ms\x18\x10 \x01(\x04H\n\x88\x01\x01\x42\x0c\n\n_resourcesB\x0f\n\r_retry_policyB\x14\n\x12_concurrency_limitB\x0f\n\r_timeout_secsB\x19\n\x17_task_idle_timeout_secsB\x11\n\x0f_warm_pool_sizeB\x1b\n\x19_target_concurrent_inputsB\x14\n\x12_buffer_containersB\x18\n\x16_max_concurrent_inputsB\x11\n\x0f_batch_max_sizeB\x12\n\x10_batch_linger_ms\"\xf4\x03\n\x18\x46unctionPrecreateRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x1b\n\rfunction_name\x18\x02 \x01(\tB\x04\x80\xb5\x18\x01\x12\x1c\n\x14\x65xisting_function_id\x18\x03 \x01(\t\x12:\n\rfunction_type\x18\x04 \x01(\x0e\x32#.modal.client.Function.FunctionType\x12\x33\n\x0ewebhook_config\x18\x05 \x01(\x0b\x32\x1b.modal.client.WebhookConfig\x12\x17\n\x0fuse_function_id\x18\x06 \x01(\t\x12\x17\n\x0fuse_method_name\x18\x07 \x01(\t\x12Y\n\x12method_definitions\x18\x08 \x03(\x0b\x32=.modal.client.FunctionPrecreateRequest.MethodDefinitionsEntry\x12\x35\n\x0f\x66unction_schema\x18\t \x01(\x0b\x32\x1c.modal.client.FunctionSchema\x1aX\n\x16MethodDefinitionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.modal.client.MethodDefinition:\x02\x38\x01\"o\n\x19\x46unctionPrecreateResponse\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12=\n\x0fhandle_metadata\x18\x02 \x01(\x0b\x32$.modal.client.FunctionHandleMetadata\"P\n\x15\x46unctionPutInputsItem\x12\x0b\n\x03idx\x18\x01 \x01(\x05\x12*\n\x05input\x18\x02 \x01(\x0b\x32\x1b.modal.client.FunctionInput\"~\n\x18\x46unctionPutInputsRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x18\n\x10\x66unction_call_id\x18\x03 \x01(\t\x12\x33\n\x06inputs\x18\x04 \x03(\x0b\x32#.modal.client.FunctionPutInputsItem\"X\n\x19\x46unctionPutInputsResponse\x12;\n\x06inputs\x18\x01 \x03(\x0b\x32+.modal.client.FunctionPutInputsResponseItem\"Q\n\x1d\x46unctionPutInputsResponseItem\x12\x0b\n\x03idx\x18\x01 \x01(\x05\x12\x10\n\x08input_id\x18\x02 \x01(\t\x12\x11\n\tinput_jwt\x18\x03 \x01(\t\"\xd0\x01\n\x16\x46unctionPutOutputsItem\x12\x10\n\x08input_id\x18\x01 \x01(\t\x12+\n\x06result\x18\x02 \x01(\x0b\x32\x1b.modal.client.GenericResult\x12\x18\n\x10input_started_at\x18\x03 \x01(\x01\x12\x19\n\x11output_created_at\x18\x04 \x01(\x01\x12-\n\x0b\x64\x61ta_format\x18\x07 \x01(\x0e\x32\x18.modal.client.DataFormat\x12\x13\n\x0bretry_count\x18\x08 \x01(\r\"h\n\x19\x46unctionPutOutputsRequest\x12\x35\n\x07outputs\x18\x04 \x03(\x0b\x32$.modal.client.FunctionPutOutputsItem\x12\x14\n\x0crequested_at\x18\x05 \x01(\x01\"m\n\x17\x46unctionRetryInputsItem\x12\x11\n\tinput_jwt\x18\x01 \x01(\t\x12*\n\x05input\x18\x02 \x01(\x0b\x32\x1b.modal.client.FunctionInput\x12\x13\n\x0bretry_count\x18\x03 \x01(\r\"n\n\x1a\x46unctionRetryInputsRequest\x12\x19\n\x11\x66unction_call_jwt\x18\x01 \x01(\t\x12\x35\n\x06inputs\x18\x02 \x03(\x0b\x32%.modal.client.FunctionRetryInputsItem\"1\n\x1b\x46unctionRetryInputsResponse\x12\x12\n\ninput_jwts\x18\x01 \x03(\t\"s\n\x13\x46unctionRetryPolicy\x12\x1b\n\x13\x62\x61\x63koff_coefficient\x18\x01 \x01(\x02\x12\x18\n\x10initial_delay_ms\x18\x02 \x01(\r\x12\x14\n\x0cmax_delay_ms\x18\x03 \x01(\r\x12\x0f\n\x07retries\x18\x12 \x01(\r\"\x91\x02\n\x0e\x46unctionSchema\x12\x44\n\x0bschema_type\x18\x01 \x01(\x0e\x32/.modal.client.FunctionSchema.FunctionSchemaType\x12\x33\n\targuments\x18\x02 \x03(\x0b\x32 .modal.client.ClassParameterSpec\x12\x35\n\x0breturn_type\x18\x03 \x01(\x0b\x32 .modal.client.GenericPayloadType\"M\n\x12\x46unctionSchemaType\x12\x1f\n\x1b\x46UNCTION_SCHEMA_UNSPECIFIED\x10\x00\x12\x16\n\x12\x46UNCTION_SCHEMA_V1\x10\x01\"9\n\rFunctionStats\x12\x0f\n\x07\x62\x61\x63klog\x18\x01 \x01(\r\x12\x17\n\x0fnum_total_tasks\x18\x03 \x01(\r\"\x91\x01\n%FunctionUpdateSchedulingParamsRequest\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\t\x12\x1f\n\x17warm_pool_size_override\x18\x02 \x01(\r\x12\x32\n\x08settings\x18\x03 \x01(\x0b\x32 .modal.client.AutoscalerSettings\"(\n&FunctionUpdateSchedulingParamsResponse\"Q\n\tGPUConfig\x12#\n\x04type\x18\x01 \x01(\x0e\x32\x15.modal.client.GPUType\x12\r\n\x05\x63ount\x18\x02 \x01(\r\x12\x10\n\x08gpu_type\x18\x04 \x01(\t\"$\n\rGeneratorDone\x12\x13\n\x0bitems_total\x18\x01 \x01(\x04\"y\n\x12GenericPayloadType\x12.\n\tbase_type\x18\x01 \x01(\x0e\x32\x1b.modal.client.ParameterType\x12\x33\n\tsub_types\x18\x02 \x03(\x0b\x32 .modal.client.GenericPayloadType\"\xed\x03\n\rGenericResult\x12\x39\n\x06status\x18\x01 \x01(\x0e\x32).modal.client.GenericResult.GenericStatus\x12\x11\n\texception\x18\x02 \x01(\t\x12\x10\n\x08\x65xitcode\x18\x03 \x01(\x05\x12\x11\n\ttraceback\x18\x04 \x01(\t\x12\x15\n\rserialized_tb\x18\x0b \x01(\x0c\x12\x15\n\rtb_line_cache\x18\x0c \x01(\x0c\x12\x0e\n\x04\x64\x61ta\x18\x05 \x01(\x0cH\x00\x12\x16\n\x0c\x64\x61ta_blob_id\x18\n \x01(\tH\x00\x12\x1a\n\x12propagation_reason\x18\r \x01(\t\"\xe8\x01\n\rGenericStatus\x12\x1e\n\x1aGENERIC_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n\x16GENERIC_STATUS_SUCCESS\x10\x01\x12\x1a\n\x16GENERIC_STATUS_FAILURE\x10\x02\x12\x1d\n\x19GENERIC_STATUS_TERMINATED\x10\x03\x12\x1a\n\x16GENERIC_STATUS_TIMEOUT\x10\x04\x12\x1f\n\x1bGENERIC_STATUS_INIT_FAILURE\x10\x05\x12#\n\x1fGENERIC_STATUS_INTERNAL_FAILURE\x10\x06\x42\x0c\n\ndata_oneof\"\xd0\x03\n\x05Image\x12,\n\x0b\x62\x61se_images\x18\x05 \x03(\x0b\x32\x17.modal.client.BaseImage\x12\x1b\n\x13\x64ockerfile_commands\x18\x06 \x03(\t\x12\x35\n\rcontext_files\x18\x07 \x03(\x0b\x32\x1e.modal.client.ImageContextFile\x12\x0f\n\x07version\x18\x0b \x01(\t\x12\x12\n\nsecret_ids\x18\x0c \x03(\t\x12\x18\n\x10\x63ontext_mount_id\x18\x0f \x01(\t\x12+\n\ngpu_config\x18\x10 \x01(\x0b\x32\x17.modal.client.GPUConfig\x12@\n\x15image_registry_config\x18\x11 \x01(\x0b\x32!.modal.client.ImageRegistryConfig\x12\x1a\n\x12\x62uild_function_def\x18\x0e \x01(\t\x12\x1e\n\x16\x62uild_function_globals\x18\x12 \x01(\x0c\x12\x0f\n\x07runtime\x18\x13 \x01(\t\x12\x15\n\rruntime_debug\x18\x14 \x01(\x08\x12\x33\n\x0e\x62uild_function\x18\x15 \x01(\x0b\x32\x1b.modal.client.BuildFunction\"2\n\x10ImageContextFile\x12\x10\n\x08\x66ilename\x18\x01 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\"&\n\x12ImageFromIdRequest\x12\x10\n\x08image_id\x18\x01 \x01(\t\"V\n\x13ImageFromIdResponse\x12\x10\n\x08image_id\x18\x01 \x01(\t\x12-\n\x08metadata\x18\x02 \x01(\x0b\x32\x1b.modal.client.ImageMetadata\"\xa4\x02\n\x17ImageGetOrCreateRequest\x12\"\n\x05image\x18\x02 \x01(\x0b\x32\x13.modal.client.Image\x12\x14\n\x06\x61pp_id\x18\x04 \x01(\tB\x04\x80\xb5\x18\x01\x12\x19\n\x11\x65xisting_image_id\x18\x05 \x01(\t\x12\x19\n\x11\x62uild_function_id\x18\x06 \x01(\t\x12\x13\n\x0b\x66orce_build\x18\x07 \x01(\x08\x12\x34\n\tnamespace\x18\x08 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x17\n\x0f\x62uilder_version\x18\t \x01(\t\x12\x1f\n\x17\x61llow_global_deployment\x18\n \x01(\x08\x12\x14\n\x0cignore_cache\x18\x0b \x01(\x08\"\x88\x01\n\x18ImageGetOrCreateResponse\x12\x10\n\x08image_id\x18\x01 \x01(\t\x12+\n\x06result\x18\x02 \x01(\x0b\x32\x1b.modal.client.GenericResult\x12-\n\x08metadata\x18\x03 \x01(\x0b\x32\x1b.modal.client.ImageMetadata\"x\n\x19ImageJoinStreamingRequest\x12\x10\n\x08image_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\x12\x15\n\rlast_entry_id\x18\x03 \x01(\t\x12!\n\x19include_logs_for_finished\x18\x04 \x01(\x08\"\xc2\x01\n\x1aImageJoinStreamingResponse\x12+\n\x06result\x18\x01 \x01(\x0b\x32\x1b.modal.client.GenericResult\x12)\n\ttask_logs\x18\x02 \x03(\x0b\x32\x16.modal.client.TaskLogs\x12\x10\n\x08\x65ntry_id\x18\x03 \x01(\t\x12\x0b\n\x03\x65of\x18\x04 \x01(\x08\x12-\n\x08metadata\x18\x05 \x01(\x0b\x32\x1b.modal.client.ImageMetadata\"\xe0\x02\n\rImageMetadata\x12 \n\x13python_version_info\x18\x01 \x01(\tH\x00\x88\x01\x01\x12H\n\x0fpython_packages\x18\x02 \x03(\x0b\x32/.modal.client.ImageMetadata.PythonPackagesEntry\x12\x14\n\x07workdir\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x1e\n\x11libc_version_info\x18\x04 \x01(\tH\x02\x88\x01\x01\x12\"\n\x15image_builder_version\x18\x05 \x01(\tH\x03\x88\x01\x01\x1a\x35\n\x13PythonPackagesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x16\n\x14_python_version_infoB\n\n\x08_workdirB\x14\n\x12_libc_version_infoB\x18\n\x16_image_builder_version\"d\n\x13ImageRegistryConfig\x12:\n\x12registry_auth_type\x18\x01 \x01(\x0e\x32\x1e.modal.client.RegistryAuthType\x12\x11\n\tsecret_id\x18\x02 \x01(\t\"\x8c\x01\n\x12InputCallGraphInfo\x12\x10\n\x08input_id\x18\x01 \x01(\t\x12\x39\n\x06status\x18\x02 \x01(\x0e\x32).modal.client.GenericResult.GenericStatus\x12\x18\n\x10\x66unction_call_id\x18\x03 \x01(\t\x12\x0f\n\x07task_id\x18\x04 \x01(\t\"K\n\x11InputCategoryInfo\x12\r\n\x05total\x18\x01 \x01(\x05\x12\'\n\x06latest\x18\x02 \x03(\x0b\x32\x17.modal.client.InputInfo\"\x99\x01\n\tInputInfo\x12\x10\n\x08input_id\x18\x01 \x01(\t\x12\x0b\n\x03idx\x18\x02 \x01(\x05\x12\x0f\n\x07task_id\x18\x03 \x01(\t\x12\x12\n\nstarted_at\x18\x04 \x01(\x01\x12\x13\n\x0b\x66inished_at\x18\x05 \x01(\x01\x12\x19\n\x11task_startup_time\x18\x06 \x01(\x01\x12\x18\n\x10task_first_input\x18\x07 \x01(\x08\"\xce\x02\n\x10MethodDefinition\x12\x15\n\rfunction_name\x18\x01 \x01(\t\x12:\n\rfunction_type\x18\x02 \x01(\x0e\x32#.modal.client.Function.FunctionType\x12\x33\n\x0ewebhook_config\x18\x03 \x01(\x0b\x32\x1b.modal.client.WebhookConfig\x12\x0f\n\x07web_url\x18\x04 \x01(\t\x12.\n\x0cweb_url_info\x18\x05 \x01(\x0b\x32\x18.modal.client.WebUrlInfo\x12:\n\x12\x63ustom_domain_info\x18\x06 \x03(\x0b\x32\x1e.modal.client.CustomDomainInfo\x12\x35\n\x0f\x66unction_schema\x18\x07 \x01(\x0b\x32\x1c.modal.client.FunctionSchema\"i\n\tMountFile\x12\x10\n\x08\x66ilename\x18\x01 \x01(\t\x12\x12\n\nsha256_hex\x18\x03 \x01(\t\x12\x11\n\x04size\x18\x04 \x01(\x04H\x00\x88\x01\x01\x12\x11\n\x04mode\x18\x05 \x01(\rH\x01\x88\x01\x01\x42\x07\n\x05_sizeB\x07\n\x05_mode\"\xfa\x01\n\x17MountGetOrCreateRequest\x12\x17\n\x0f\x64\x65ployment_name\x18\x01 \x01(\t\x12\x34\n\tnamespace\x18\x02 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12>\n\x14object_creation_type\x18\x04 \x01(\x0e\x32 .modal.client.ObjectCreationType\x12&\n\x05\x66iles\x18\x05 \x03(\x0b\x32\x17.modal.client.MountFile\x12\x0e\n\x06\x61pp_id\x18\x06 \x01(\t\"h\n\x18MountGetOrCreateResponse\x12\x10\n\x08mount_id\x18\x01 \x01(\t\x12:\n\x0fhandle_metadata\x18\x02 \x01(\x0b\x32!.modal.client.MountHandleMetadata\":\n\x13MountHandleMetadata\x12#\n\x1b\x63ontent_checksum_sha256_hex\x18\x01 \x01(\t\"_\n\x13MountPutFileRequest\x12\x12\n\nsha256_hex\x18\x02 \x01(\t\x12\x0e\n\x04\x64\x61ta\x18\x03 \x01(\x0cH\x00\x12\x16\n\x0c\x64\x61ta_blob_id\x18\x05 \x01(\tH\x00\x42\x0c\n\ndata_oneof\"&\n\x14MountPutFileResponse\x12\x0e\n\x06\x65xists\x18\x02 \x01(\x08\"S\n\x0fMultiPartUpload\x12\x13\n\x0bpart_length\x18\x01 \x01(\x03\x12\x13\n\x0bupload_urls\x18\x02 \x03(\t\x12\x16\n\x0e\x63ompletion_url\x18\x03 \x01(\t\"\xbe\x01\n\rNetworkAccess\x12J\n\x13network_access_type\x18\x01 \x01(\x0e\x32-.modal.client.NetworkAccess.NetworkAccessType\x12\x15\n\rallowed_cidrs\x18\x02 \x03(\t\"J\n\x11NetworkAccessType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x08\n\x04OPEN\x10\x01\x12\x0b\n\x07\x42LOCKED\x10\x02\x12\r\n\tALLOWLIST\x10\x03\"\xa4\x03\n#NotebookKernelPublishResultsRequest\x12\x13\n\x0bnotebook_id\x18\x01 \x01(\t\x12M\n\x07results\x18\x02 \x03(\x0b\x32<.modal.client.NotebookKernelPublishResultsRequest.CellResult\x1aI\n\x0c\x45xecuteReply\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x17\n\x0f\x65xecution_count\x18\x02 \x01(\r\x12\x10\n\x08\x64uration\x18\x03 \x01(\x01\x1a\xcd\x01\n\nCellResult\x12\x0f\n\x07\x63\x65ll_id\x18\x01 \x01(\t\x12.\n\x06output\x18\x02 \x01(\x0b\x32\x1c.modal.client.NotebookOutputH\x00\x12\x16\n\x0c\x63lear_output\x18\x03 \x01(\x08H\x00\x12W\n\rexecute_reply\x18\x04 \x01(\x0b\x32>.modal.client.NotebookKernelPublishResultsRequest.ExecuteReplyH\x00\x42\r\n\x0bresult_type\"\x8e\x05\n\x0eNotebookOutput\x12\x44\n\x0e\x65xecute_result\x18\x01 \x01(\x0b\x32*.modal.client.NotebookOutput.ExecuteResultH\x00\x12@\n\x0c\x64isplay_data\x18\x02 \x01(\x0b\x32(.modal.client.NotebookOutput.DisplayDataH\x00\x12\x35\n\x06stream\x18\x03 \x01(\x0b\x32#.modal.client.NotebookOutput.StreamH\x00\x12\x33\n\x05\x65rror\x18\x04 \x01(\x0b\x32\".modal.client.NotebookOutput.ErrorH\x00\x1az\n\rExecuteResult\x12\x17\n\x0f\x65xecution_count\x18\x01 \x01(\r\x12%\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\x12)\n\x08metadata\x18\x03 \x01(\x0b\x32\x17.google.protobuf.Struct\x1a\x9b\x01\n\x0b\x44isplayData\x12%\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\x17.google.protobuf.Struct\x12)\n\x08metadata\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\x12!\n\x14transient_display_id\x18\x03 \x01(\tH\x00\x88\x01\x01\x42\x17\n\x15_transient_display_id\x1a$\n\x06Stream\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04text\x18\x02 \x01(\t\x1a\x39\n\x05\x45rror\x12\r\n\x05\x65name\x18\x01 \x01(\t\x12\x0e\n\x06\x65value\x18\x02 \x01(\t\x12\x11\n\ttraceback\x18\x03 \x03(\tB\r\n\x0boutput_type\"\x87\x03\n\x06Object\x12\x11\n\tobject_id\x18\x01 \x01(\t\x12H\n\x18\x66unction_handle_metadata\x18\x03 \x01(\x0b\x32$.modal.client.FunctionHandleMetadataH\x00\x12\x42\n\x15mount_handle_metadata\x18\x04 \x01(\x0b\x32!.modal.client.MountHandleMetadataH\x00\x12\x42\n\x15\x63lass_handle_metadata\x18\x05 \x01(\x0b\x32!.modal.client.ClassHandleMetadataH\x00\x12\x46\n\x17sandbox_handle_metadata\x18\x06 \x01(\x0b\x32#.modal.client.SandboxHandleMetadataH\x00\x12\x37\n\x0fvolume_metadata\x18\x07 \x01(\x0b\x32\x1c.modal.client.VolumeMetadataH\x00\x42\x17\n\x15handle_metadata_oneof\"%\n\x10ObjectDependency\x12\x11\n\tobject_id\x18\x01 \x01(\t\"\x86\x02\n\x07PTYInfo\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x12\n\nwinsz_rows\x18\x02 \x01(\r\x12\x12\n\nwinsz_cols\x18\x03 \x01(\r\x12\x10\n\x08\x65nv_term\x18\x04 \x01(\t\x12\x15\n\renv_colorterm\x18\x05 \x01(\t\x12\x18\n\x10\x65nv_term_program\x18\x06 \x01(\t\x12/\n\x08pty_type\x18\x07 \x01(\x0e\x32\x1d.modal.client.PTYInfo.PTYType\"N\n\x07PTYType\x12\x18\n\x14PTY_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11PTY_TYPE_FUNCTION\x10\x01\x12\x12\n\x0ePTY_TYPE_SHELL\x10\x02\"q\n\x08PortSpec\x12\x0c\n\x04port\x18\x01 \x01(\r\x12\x13\n\x0bunencrypted\x18\x02 \x01(\x08\x12\x32\n\x0btunnel_type\x18\x03 \x01(\x0e\x32\x18.modal.client.TunnelTypeH\x00\x88\x01\x01\x42\x0e\n\x0c_tunnel_type\"2\n\tPortSpecs\x12%\n\x05ports\x18\x01 \x03(\x0b\x32\x16.modal.client.PortSpec\"\x7f\n\x05Proxy\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ncreated_at\x18\x02 \x01(\x01\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12\x10\n\x08proxy_id\x18\x05 \x01(\t\x12(\n\tproxy_ips\x18\x04 \x03(\x0b\x32\x15.modal.client.ProxyIp\"+\n\x11ProxyAddIpRequest\x12\x16\n\x08proxy_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\"=\n\x12ProxyAddIpResponse\x12\'\n\x08proxy_ip\x18\x01 \x01(\x0b\x32\x15.modal.client.ProxyIp\"B\n\x12ProxyCreateRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x18\n\x10\x65nvironment_name\x18\x02 \x01(\t\"9\n\x13ProxyCreateResponse\x12\"\n\x05proxy\x18\x01 \x01(\x0b\x32\x13.modal.client.Proxy\",\n\x12ProxyDeleteRequest\x12\x16\n\x08proxy_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\"\xc8\x01\n\x17ProxyGetOrCreateRequest\x12\x1d\n\x0f\x64\x65ployment_name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x34\n\tnamespace\x18\x02 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12>\n\x14object_creation_type\x18\x04 \x01(\x0e\x32 .modal.client.ObjectCreationType\",\n\x18ProxyGetOrCreateResponse\x12\x10\n\x08proxy_id\x18\x01 \x01(\t\"?\n\x0fProxyGetRequest\x12\x12\n\x04name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x18\n\x10\x65nvironment_name\x18\x02 \x01(\t\"6\n\x10ProxyGetResponse\x12\"\n\x05proxy\x18\x01 \x01(\x0b\x32\x13.modal.client.Proxy\"\x89\x01\n\tProxyInfo\x12\x12\n\nelastic_ip\x18\x01 \x01(\t\x12\x11\n\tproxy_key\x18\x02 \x01(\t\x12\x13\n\x0bremote_addr\x18\x03 \x01(\t\x12\x13\n\x0bremote_port\x18\x04 \x01(\x05\x12+\n\nproxy_type\x18\x05 \x01(\x0e\x32\x17.modal.client.ProxyType\"v\n\x07ProxyIp\x12\x10\n\x08proxy_ip\x18\x01 \x01(\t\x12+\n\x06status\x18\x02 \x01(\x0e\x32\x1b.modal.client.ProxyIpStatus\x12\x12\n\ncreated_at\x18\x03 \x01(\x01\x12\x18\n\x10\x65nvironment_name\x18\x04 \x01(\t\"9\n\x11ProxyListResponse\x12$\n\x07proxies\x18\x01 \x03(\x0b\x32\x13.modal.client.Proxy\".\n\x14ProxyRemoveIpRequest\x12\x16\n\x08proxy_ip\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\"T\n\x11QueueClearRequest\x12\x10\n\x08queue_id\x18\x01 \x01(\t\x12\x15\n\rpartition_key\x18\x02 \x01(\x0c\x12\x16\n\x0e\x61ll_partitions\x18\x03 \x01(\x08\",\n\x12QueueDeleteRequest\x12\x16\n\x08queue_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\"\xc2\x01\n\x17QueueGetOrCreateRequest\x12\x17\n\x0f\x64\x65ployment_name\x18\x01 \x01(\t\x12\x34\n\tnamespace\x18\x02 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12>\n\x14object_creation_type\x18\x04 \x01(\x0e\x32 .modal.client.ObjectCreationType\",\n\x18QueueGetOrCreateResponse\x12\x10\n\x08queue_id\x18\x01 \x01(\t\"]\n\x0fQueueGetRequest\x12\x10\n\x08queue_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x03 \x01(\x02\x12\x10\n\x08n_values\x18\x04 \x01(\x05\x12\x15\n\rpartition_key\x18\x05 \x01(\x0c\"\"\n\x10QueueGetResponse\x12\x0e\n\x06values\x18\x02 \x03(\x0c\")\n\x15QueueHeartbeatRequest\x12\x10\n\x08queue_id\x18\x01 \x01(\t\",\n\tQueueItem\x12\r\n\x05value\x18\x01 \x01(\x0c\x12\x10\n\x08\x65ntry_id\x18\x02 \x01(\t\"I\n\x0fQueueLenRequest\x12\x10\n\x08queue_id\x18\x01 \x01(\t\x12\x15\n\rpartition_key\x18\x02 \x01(\x0c\x12\r\n\x05total\x18\x03 \x01(\x08\"\x1f\n\x10QueueLenResponse\x12\x0b\n\x03len\x18\x01 \x01(\x05\"F\n\x10QueueListRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\x12\x18\n\x10total_size_limit\x18\x02 \x01(\x05\"\xa9\x01\n\x11QueueListResponse\x12\x39\n\x06queues\x18\x01 \x03(\x0b\x32).modal.client.QueueListResponse.QueueInfo\x1aY\n\tQueueInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ncreated_at\x18\x02 \x01(\x01\x12\x16\n\x0enum_partitions\x18\x03 \x01(\x05\x12\x12\n\ntotal_size\x18\x04 \x01(\x05\"r\n\x15QueueNextItemsRequest\x12\x10\n\x08queue_id\x18\x01 \x01(\t\x12\x15\n\rpartition_key\x18\x02 \x01(\x0c\x12\x15\n\rlast_entry_id\x18\x03 \x01(\t\x12\x19\n\x11item_poll_timeout\x18\x04 \x01(\x02\"@\n\x16QueueNextItemsResponse\x12&\n\x05items\x18\x01 \x03(\x0b\x32\x17.modal.client.QueueItem\"i\n\x0fQueuePutRequest\x12\x10\n\x08queue_id\x18\x01 \x01(\t\x12\x0e\n\x06values\x18\x04 \x03(\x0c\x12\x15\n\rpartition_key\x18\x05 \x01(\x0c\x12\x1d\n\x15partition_ttl_seconds\x18\x06 \x01(\x05\"M\n\tRateLimit\x12\r\n\x05limit\x18\x01 \x01(\x05\x12\x31\n\x08interval\x18\x02 \x01(\x0e\x32\x1f.modal.client.RateLimitInterval\"\xb5\x01\n\tResources\x12\x11\n\tmemory_mb\x18\x02 \x01(\r\x12\x11\n\tmilli_cpu\x18\x03 \x01(\r\x12+\n\ngpu_config\x18\x04 \x01(\x0b\x32\x17.modal.client.GPUConfig\x12\x15\n\rmemory_mb_max\x18\x05 \x01(\r\x12\x19\n\x11\x65phemeral_disk_mb\x18\x06 \x01(\r\x12\x15\n\rmilli_cpu_max\x18\x07 \x01(\r\x12\x0c\n\x04rdma\x18\x08 \x01(\x08\"J\n\x13RuntimeInputMessage\x12\x0f\n\x07message\x18\x01 \x01(\x0c\x12\x15\n\rmessage_index\x18\x02 \x01(\x04\x12\x0b\n\x03\x65of\x18\x03 \x01(\x08\"\x9c\x02\n\x12RuntimeOutputBatch\x12\x31\n\x05items\x18\x01 \x03(\x0b\x32\".modal.client.RuntimeOutputMessage\x12\x13\n\x0b\x62\x61tch_index\x18\x02 \x01(\x04\x12\x16\n\texit_code\x18\x03 \x01(\x05H\x00\x88\x01\x01\x12\x32\n\x06stdout\x18\x04 \x03(\x0b\x32\".modal.client.RuntimeOutputMessage\x12\x32\n\x06stderr\x18\x05 \x03(\x0b\x32\".modal.client.RuntimeOutputMessage\x12\x30\n\x04info\x18\x06 \x03(\x0b\x32\".modal.client.RuntimeOutputMessageB\x0c\n\n_exit_code\"u\n\x14RuntimeOutputMessage\x12\x35\n\x0f\x66ile_descriptor\x18\x01 \x01(\x0e\x32\x1c.modal.client.FileDescriptor\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x15\n\rmessage_bytes\x18\x03 \x01(\x0c\"d\n\x07S3Mount\x12\x13\n\x0b\x62ucket_name\x18\x01 \x01(\t\x12\x12\n\nmount_path\x18\x02 \x01(\t\x12\x1d\n\x15\x63redentials_secret_id\x18\x03 \x01(\t\x12\x11\n\tread_only\x18\x04 \x01(\x08\"\xa0\x08\n\x07Sandbox\x12\x17\n\x0f\x65ntrypoint_args\x18\x01 \x03(\t\x12\x11\n\tmount_ids\x18\x02 \x03(\t\x12\x10\n\x08image_id\x18\x03 \x01(\t\x12\x12\n\nsecret_ids\x18\x04 \x03(\t\x12*\n\tresources\x18\x05 \x01(\x0b\x32\x17.modal.client.Resources\x12\x33\n\x0e\x63loud_provider\x18\x06 \x01(\x0e\x32\x1b.modal.client.CloudProvider\x12\x14\n\x0ctimeout_secs\x18\x07 \x01(\r\x12\x14\n\x07workdir\x18\x08 \x01(\tH\x01\x88\x01\x01\x12\x33\n\nnfs_mounts\x18\t \x03(\x0b\x32\x1f.modal.client.SharedVolumeMount\x12\x15\n\rruntime_debug\x18\n \x01(\x08\x12\x15\n\rblock_network\x18\x0b \x01(\x08\x12(\n\ts3_mounts\x18\x0c \x03(\x0b\x32\x15.modal.client.S3Mount\x12;\n\x13\x63loud_bucket_mounts\x18\x0e \x03(\x0b\x32\x1e.modal.client.CloudBucketMount\x12\x30\n\rvolume_mounts\x18\r \x03(\x0b\x32\x19.modal.client.VolumeMount\x12\'\n\x08pty_info\x18\x0f \x01(\x0b\x32\x15.modal.client.PTYInfo\x12\x42\n\x13scheduler_placement\x18\x11 \x01(\x0b\x32 .modal.client.SchedulerPlacementH\x02\x88\x01\x01\x12\x11\n\tworker_id\x18\x13 \x01(\t\x12-\n\nopen_ports\x18\x14 \x01(\x0b\x32\x17.modal.client.PortSpecsH\x00\x12\x14\n\x0ci6pn_enabled\x18\x15 \x01(\x08\x12\x33\n\x0enetwork_access\x18\x16 \x01(\x0b\x32\x1b.modal.client.NetworkAccess\x12\x15\n\x08proxy_id\x18\x17 \x01(\tH\x03\x88\x01\x01\x12\x17\n\x0f\x65nable_snapshot\x18\x18 \x01(\x08\x12\x1d\n\x10snapshot_version\x18\x19 \x01(\rH\x04\x88\x01\x01\x12\x1a\n\x12\x63loud_provider_str\x18\x1a \x01(\t\x12\"\n\x15runsc_runtime_version\x18\x1b \x01(\tH\x05\x88\x01\x01\x12\x14\n\x07runtime\x18\x1c \x01(\tH\x06\x88\x01\x01\x12\x0f\n\x07verbose\x18\x1d \x01(\x08\x42\x12\n\x10open_ports_oneofB\n\n\x08_workdirB\x16\n\x14_scheduler_placementB\x0b\n\t_proxy_idB\x13\n\x11_snapshot_versionB\x18\n\x16_runsc_runtime_versionB\n\n\x08_runtimeJ\x04\x08\x10\x10\x11J\x04\x08\x12\x10\x13\"q\n\x14SandboxCreateRequest\x12\x14\n\x06\x61pp_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12)\n\ndefinition\x18\x02 \x01(\x0b\x32\x15.modal.client.Sandbox\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\"+\n\x15SandboxCreateResponse\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\"\x8a\x01\n\x15SandboxGetLogsRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\x12\x35\n\x0f\x66ile_descriptor\x18\x02 \x01(\x0e\x32\x1c.modal.client.FileDescriptor\x12\x0f\n\x07timeout\x18\x03 \x01(\x02\x12\x15\n\rlast_entry_id\x18\x04 \x01(\t\"4\n\x1eSandboxGetResourceUsageRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\"\x90\x01\n\x1fSandboxGetResourceUsageResponse\x12\x19\n\x11\x63pu_core_nanosecs\x18\x01 \x01(\x04\x12\x18\n\x10mem_gib_nanosecs\x18\x02 \x01(\x04\x12\x14\n\x0cgpu_nanosecs\x18\x03 \x01(\x04\x12\x15\n\x08gpu_type\x18\x04 \x01(\tH\x00\x88\x01\x01\x42\x0b\n\t_gpu_type\"i\n\x17SandboxGetTaskIdRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\x12\x14\n\x07timeout\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x18\n\x10wait_until_ready\x18\x03 \x01(\x08\x42\n\n\x08_timeout\"\x83\x01\n\x18SandboxGetTaskIdResponse\x12\x14\n\x07task_id\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x35\n\x0btask_result\x18\x02 \x01(\x0b\x32\x1b.modal.client.GenericResultH\x01\x88\x01\x01\x42\n\n\x08_task_idB\x0e\n\x0c_task_result\"?\n\x18SandboxGetTunnelsRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\"s\n\x19SandboxGetTunnelsResponse\x12+\n\x06result\x18\x01 \x01(\x0b\x32\x1b.modal.client.GenericResult\x12)\n\x07tunnels\x18\x02 \x03(\x0b\x32\x18.modal.client.TunnelData\"D\n\x15SandboxHandleMetadata\x12+\n\x06result\x18\x01 \x01(\x0b\x32\x1b.modal.client.GenericResult\"n\n\x0bSandboxInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\ncreated_at\x18\x03 \x01(\x01\x12)\n\ttask_info\x18\x04 \x01(\x0b\x32\x16.modal.client.TaskInfo\x12\x0e\n\x06\x61pp_id\x18\x05 \x01(\tJ\x04\x08\x02\x10\x03\"\x9a\x01\n\x12SandboxListRequest\x12\x0e\n\x06\x61pp_id\x18\x01 \x01(\t\x12\x18\n\x10\x62\x65\x66ore_timestamp\x18\x02 \x01(\x01\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12\x18\n\x10include_finished\x18\x04 \x01(\x08\x12&\n\x04tags\x18\x05 \x03(\x0b\x32\x18.modal.client.SandboxTag\"C\n\x13SandboxListResponse\x12,\n\tsandboxes\x18\x01 \x03(\x0b\x32\x19.modal.client.SandboxInfo\",\n\x15SandboxRestoreRequest\x12\x13\n\x0bsnapshot_id\x18\x01 \x01(\t\",\n\x16SandboxRestoreResponse\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\"?\n\x18SandboxSnapshotFsRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\"\x8f\x01\n\x19SandboxSnapshotFsResponse\x12\x10\n\x08image_id\x18\x01 \x01(\t\x12+\n\x06result\x18\x02 \x01(\x0b\x32\x1b.modal.client.GenericResult\x12\x33\n\x0eimage_metadata\x18\x03 \x01(\x0b\x32\x1b.modal.client.ImageMetadata\"0\n\x19SandboxSnapshotGetRequest\x12\x13\n\x0bsnapshot_id\x18\x01 \x01(\t\"1\n\x1aSandboxSnapshotGetResponse\x12\x13\n\x0bsnapshot_id\x18\x01 \x01(\t\",\n\x16SandboxSnapshotRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\".\n\x17SandboxSnapshotResponse\x12\x13\n\x0bsnapshot_id\x18\x01 \x01(\t\"B\n\x1aSandboxSnapshotWaitRequest\x12\x13\n\x0bsnapshot_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\"J\n\x1bSandboxSnapshotWaitResponse\x12+\n\x06result\x18\x01 \x01(\x0b\x32\x1b.modal.client.GenericResult\"Y\n\x18SandboxStdinWriteRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\x12\r\n\x05input\x18\x02 \x01(\x0c\x12\r\n\x05index\x18\x03 \x01(\r\x12\x0b\n\x03\x65of\x18\x04 \x01(\x08\"\x1b\n\x19SandboxStdinWriteResponse\"1\n\nSandboxTag\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x11\n\ttag_value\x18\x02 \x01(\t\"m\n\x15SandboxTagsSetRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\x12\x12\n\nsandbox_id\x18\x02 \x01(\t\x12&\n\x04tags\x18\x03 \x03(\x0b\x32\x18.modal.client.SandboxTag\"-\n\x17SandboxTerminateRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\"P\n\x18SandboxTerminateResponse\x12\x34\n\x0f\x65xisting_result\x18\x01 \x01(\x0b\x32\x1b.modal.client.GenericResult\"9\n\x12SandboxWaitRequest\x12\x12\n\nsandbox_id\x18\x01 \x01(\t\x12\x0f\n\x07timeout\x18\x02 \x01(\x02\"B\n\x13SandboxWaitResponse\x12+\n\x06result\x18\x01 \x01(\x0b\x32\x1b.modal.client.GenericResult\"\xa0\x02\n\x08Schedule\x12+\n\x04\x63ron\x18\x01 \x01(\x0b\x32\x1b.modal.client.Schedule.CronH\x00\x12/\n\x06period\x18\x02 \x01(\x0b\x32\x1d.modal.client.Schedule.PeriodH\x00\x1a-\n\x04\x43ron\x12\x13\n\x0b\x63ron_string\x18\x01 \x01(\t\x12\x10\n\x08timezone\x18\x02 \x01(\t\x1au\n\x06Period\x12\r\n\x05years\x18\x01 \x01(\x05\x12\x0e\n\x06months\x18\x02 \x01(\x05\x12\r\n\x05weeks\x18\x03 \x01(\x05\x12\x0c\n\x04\x64\x61ys\x18\x04 \x01(\x05\x12\r\n\x05hours\x18\x05 \x01(\x05\x12\x0f\n\x07minutes\x18\x06 \x01(\x05\x12\x0f\n\x07seconds\x18\x07 \x01(\x02\x42\x10\n\x0eschedule_oneof\"\x8a\x01\n\x12SchedulerPlacement\x12\x0f\n\x07regions\x18\x04 \x03(\t\x12\x12\n\x05_zone\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x17\n\n_lifecycle\x18\x03 \x01(\tH\x01\x88\x01\x01\x12\x17\n\x0f_instance_types\x18\x05 \x03(\tB\x08\n\x06X_zoneB\r\n\x0bX_lifecycleJ\x04\x08\x01\x10\x02\"\xd0\x01\n\x13SecretCreateRequest\x12@\n\x08\x65nv_dict\x18\x01 \x03(\x0b\x32..modal.client.SecretCreateRequest.EnvDictEntry\x12\x14\n\x06\x61pp_id\x18\x02 \x01(\tB\x04\x80\xb5\x18\x01\x12\x15\n\rtemplate_type\x18\x03 \x01(\t\x12\x1a\n\x12\x65xisting_secret_id\x18\x04 \x01(\t\x1a.\n\x0c\x45nvDictEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\")\n\x14SecretCreateResponse\x12\x11\n\tsecret_id\x18\x01 \x01(\t\"(\n\x13SecretDeleteRequest\x12\x11\n\tsecret_id\x18\x01 \x01(\t\"\xe7\x02\n\x18SecretGetOrCreateRequest\x12\x1d\n\x0f\x64\x65ployment_name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x34\n\tnamespace\x18\x02 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12>\n\x14object_creation_type\x18\x04 \x01(\x0e\x32 .modal.client.ObjectCreationType\x12\x45\n\x08\x65nv_dict\x18\x05 \x03(\x0b\x32\x33.modal.client.SecretGetOrCreateRequest.EnvDictEntry\x12\x0e\n\x06\x61pp_id\x18\x06 \x01(\t\x12\x15\n\rrequired_keys\x18\x07 \x03(\t\x1a.\n\x0c\x45nvDictEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\".\n\x19SecretGetOrCreateResponse\x12\x11\n\tsecret_id\x18\x01 \x01(\t\"v\n\x0eSecretListItem\x12\r\n\x05label\x18\x01 \x01(\t\x12\x12\n\ncreated_at\x18\x02 \x01(\x01\x12\x14\n\x0clast_used_at\x18\x03 \x01(\x01\x12\x18\n\x10\x65nvironment_name\x18\x04 \x01(\t\x12\x11\n\tsecret_id\x18\x05 \x01(\t\"-\n\x11SecretListRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\"[\n\x12SecretListResponse\x12+\n\x05items\x18\x01 \x03(\x0b\x32\x1c.modal.client.SecretListItem\x12\x18\n\x10\x65nvironment_name\x18\x02 \x01(\t\"5\n\x19SharedVolumeDeleteRequest\x12\x18\n\x10shared_volume_id\x18\x01 \x01(\t\"D\n\x1aSharedVolumeGetFileRequest\x12\x18\n\x10shared_volume_id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\"S\n\x1bSharedVolumeGetFileResponse\x12\x0e\n\x04\x64\x61ta\x18\x01 \x01(\x0cH\x00\x12\x16\n\x0c\x64\x61ta_blob_id\x18\x02 \x01(\tH\x00\x42\x0c\n\ndata_oneof\"\xdf\x01\n\x1eSharedVolumeGetOrCreateRequest\x12\x1d\n\x0f\x64\x65ployment_name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x34\n\tnamespace\x18\x02 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12>\n\x14object_creation_type\x18\x04 \x01(\x0e\x32 .modal.client.ObjectCreationType\x12\x0e\n\x06\x61pp_id\x18\x05 \x01(\t\";\n\x1fSharedVolumeGetOrCreateResponse\x12\x18\n\x10shared_volume_id\x18\x01 \x01(\t\"8\n\x1cSharedVolumeHeartbeatRequest\x12\x18\n\x10shared_volume_id\x18\x01 \x01(\t\"F\n\x1cSharedVolumeListFilesRequest\x12\x18\n\x10shared_volume_id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\"I\n\x1dSharedVolumeListFilesResponse\x12(\n\x07\x65ntries\x18\x01 \x03(\x0b\x32\x17.modal.client.FileEntry\"\x88\x01\n\x14SharedVolumeListItem\x12\r\n\x05label\x18\x01 \x01(\t\x12\x18\n\x10shared_volume_id\x18\x02 \x01(\t\x12\x12\n\ncreated_at\x18\x03 \x01(\x01\x12\x33\n\x0e\x63loud_provider\x18\x04 \x01(\x0e\x32\x1b.modal.client.CloudProvider\"3\n\x17SharedVolumeListRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\"g\n\x18SharedVolumeListResponse\x12\x31\n\x05items\x18\x01 \x03(\x0b\x32\".modal.client.SharedVolumeListItem\x12\x18\n\x10\x65nvironment_name\x18\x02 \x01(\t\"\x92\x01\n\x11SharedVolumeMount\x12\x12\n\nmount_path\x18\x01 \x01(\t\x12\x18\n\x10shared_volume_id\x18\x02 \x01(\t\x12\x33\n\x0e\x63loud_provider\x18\x03 \x01(\x0e\x32\x1b.modal.client.CloudProvider\x12\x1a\n\x12\x61llow_cross_region\x18\x04 \x01(\x08\"\xa7\x01\n\x1aSharedVolumePutFileRequest\x12\x1e\n\x10shared_volume_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x12\n\nsha256_hex\x18\x03 \x01(\t\x12\x0e\n\x04\x64\x61ta\x18\x04 \x01(\x0cH\x00\x12\x16\n\x0c\x64\x61ta_blob_id\x18\x05 \x01(\tH\x00\x12\x11\n\tresumable\x18\x06 \x01(\x08\x42\x0c\n\ndata_oneof\"-\n\x1bSharedVolumePutFileResponse\x12\x0e\n\x06\x65xists\x18\x01 \x01(\x08\"`\n\x1dSharedVolumeRemoveFileRequest\x12\x1e\n\x10shared_volume_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x11\n\trecursive\x18\x03 \x01(\x08\"^\n\x12SystemErrorMessage\x12\x31\n\nerror_code\x18\x01 \x01(\x0e\x32\x1d.modal.client.SystemErrorCode\x12\x15\n\rerror_message\x18\x02 \x01(\t\"@\n\x17TaskClusterHelloRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x14\n\x0c\x63ontainer_ip\x18\x02 \x01(\t\"[\n\x18TaskClusterHelloResponse\x12\x12\n\ncluster_id\x18\x01 \x01(\t\x12\x14\n\x0c\x63luster_rank\x18\x02 \x01(\r\x12\x15\n\rcontainer_ips\x18\x03 \x03(\t\".\n\x19TaskCurrentInputsResponse\x12\x11\n\tinput_ids\x18\x01 \x03(\t\"\xa7\x01\n\x08TaskInfo\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\nstarted_at\x18\x02 \x01(\x01\x12\x13\n\x0b\x66inished_at\x18\x03 \x01(\x01\x12+\n\x06result\x18\x04 \x01(\x0b\x32\x1b.modal.client.GenericResult\x12\x13\n\x0b\x65nqueued_at\x18\x05 \x01(\x01\x12\x10\n\x08gpu_type\x18\x06 \x01(\t\x12\x12\n\nsandbox_id\x18\x07 \x01(\t\"+\n\x0fTaskListRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\":\n\x10TaskListResponse\x12&\n\x05tasks\x18\x01 \x03(\x0b\x32\x17.modal.client.TaskStats\"\x84\x02\n\x08TaskLogs\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\t\x12+\n\ntask_state\x18\x06 \x01(\x0e\x32\x17.modal.client.TaskState\x12\x11\n\ttimestamp\x18\x07 \x01(\x01\x12\x35\n\x0f\x66ile_descriptor\x18\x08 \x01(\x0e\x32\x1c.modal.client.FileDescriptor\x12\x31\n\rtask_progress\x18\t \x01(\x0b\x32\x1a.modal.client.TaskProgress\x12\x18\n\x10\x66unction_call_id\x18\n \x01(\t\x12\x10\n\x08input_id\x18\x0b \x01(\t\x12\x14\n\x0ctimestamp_ns\x18\x0c \x01(\x04\"\xe0\x01\n\rTaskLogsBatch\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12%\n\x05items\x18\x02 \x03(\x0b\x32\x16.modal.client.TaskLogs\x12\x10\n\x08\x65ntry_id\x18\x05 \x01(\t\x12\x10\n\x08\x61pp_done\x18\n \x01(\x08\x12\x13\n\x0b\x66unction_id\x18\x0b \x01(\t\x12\x10\n\x08input_id\x18\x0c \x01(\t\x12\x10\n\x08image_id\x18\r \x01(\t\x12\x0b\n\x03\x65of\x18\x0e \x01(\x08\x12\x13\n\x0bpty_exec_id\x18\x0f \x01(\t\x12\x18\n\x10root_function_id\x18\x10 \x01(\t\"p\n\x0cTaskProgress\x12\x0b\n\x03len\x18\x01 \x01(\x04\x12\x0b\n\x03pos\x18\x02 \x01(\x04\x12\x31\n\rprogress_type\x18\x03 \x01(\x0e\x32\x1a.modal.client.ProgressType\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\"@\n\x11TaskResultRequest\x12+\n\x06result\x18\x02 \x01(\x0b\x32\x1b.modal.client.GenericResult\"Y\n\tTaskStats\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0e\n\x06\x61pp_id\x18\x02 \x01(\t\x12\x17\n\x0f\x61pp_description\x18\x03 \x01(\t\x12\x12\n\nstarted_at\x18\x04 \x01(\x01\"\x98\x01\n\x0cTaskTemplate\x12\x0c\n\x04rank\x18\x01 \x01(\r\x12*\n\tresources\x18\x02 \x01(\x0b\x32\x17.modal.client.Resources\x12 \n\x18target_concurrent_inputs\x18\x03 \x01(\r\x12\x1d\n\x15max_concurrent_inputs\x18\x04 \x01(\r\x12\r\n\x05index\x18\x05 \x01(\r\"V\n\x16TokenFlowCreateRequest\x12\x12\n\nutm_source\x18\x03 \x01(\t\x12\x16\n\x0elocalhost_port\x18\x04 \x01(\x05\x12\x10\n\x08next_url\x18\x05 \x01(\t\"d\n\x17TokenFlowCreateResponse\x12\x15\n\rtoken_flow_id\x18\x01 \x01(\t\x12\x0f\n\x07web_url\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x13\n\x0bwait_secret\x18\x04 \x01(\t\"S\n\x14TokenFlowWaitRequest\x12\x0f\n\x07timeout\x18\x01 \x01(\x02\x12\x15\n\rtoken_flow_id\x18\x02 \x01(\t\x12\x13\n\x0bwait_secret\x18\x03 \x01(\t\"l\n\x15TokenFlowWaitResponse\x12\x10\n\x08token_id\x18\x01 \x01(\t\x12\x14\n\x0ctoken_secret\x18\x02 \x01(\t\x12\x0f\n\x07timeout\x18\x03 \x01(\x08\x12\x1a\n\x12workspace_username\x18\x04 \x01(\t\"\xa8\x01\n\nTunnelData\x12\x0c\n\x04host\x18\x01 \x01(\t\x12\x0c\n\x04port\x18\x02 \x01(\r\x12\x1d\n\x10unencrypted_host\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x1d\n\x10unencrypted_port\x18\x04 \x01(\rH\x01\x88\x01\x01\x12\x16\n\x0e\x63ontainer_port\x18\x05 \x01(\rB\x13\n\x11_unencrypted_hostB\x13\n\x11_unencrypted_port\"{\n\x12TunnelStartRequest\x12\x0c\n\x04port\x18\x01 \x01(\r\x12\x13\n\x0bunencrypted\x18\x02 \x01(\x08\x12\x32\n\x0btunnel_type\x18\x03 \x01(\x0e\x32\x18.modal.client.TunnelTypeH\x00\x88\x01\x01\x42\x0e\n\x0c_tunnel_type\"\x99\x01\n\x13TunnelStartResponse\x12\x0c\n\x04host\x18\x01 \x01(\t\x12\x0c\n\x04port\x18\x02 \x01(\r\x12\x1d\n\x10unencrypted_host\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x1d\n\x10unencrypted_port\x18\x04 \x01(\rH\x01\x88\x01\x01\x42\x13\n\x11_unencrypted_hostB\x13\n\x11_unencrypted_port\"!\n\x11TunnelStopRequest\x12\x0c\n\x04port\x18\x01 \x01(\r\"$\n\x12TunnelStopResponse\x12\x0e\n\x06\x65xists\x18\x01 \x01(\x08\".\n\x13VolumeCommitRequest\x12\x17\n\tvolume_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\"+\n\x14VolumeCommitResponse\x12\x13\n\x0bskip_reload\x18\x01 \x01(\x08\"d\n\x17VolumeCopyFiles2Request\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x11\n\tsrc_paths\x18\x02 \x03(\t\x12\x10\n\x08\x64st_path\x18\x03 \x01(\t\x12\x11\n\trecursive\x18\x04 \x01(\x08\"c\n\x16VolumeCopyFilesRequest\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x11\n\tsrc_paths\x18\x02 \x03(\t\x12\x10\n\x08\x64st_path\x18\x03 \x01(\t\x12\x11\n\trecursive\x18\x04 \x01(\x08\"F\n\x13VolumeDeleteRequest\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x1c\n\x10\x65nvironment_name\x18\x02 \x01(\tB\x02\x18\x01\"T\n\x15VolumeGetFile2Request\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\r\n\x05start\x18\x03 \x01(\x04\x12\x0b\n\x03len\x18\x04 \x01(\x04\"T\n\x16VolumeGetFile2Response\x12\x10\n\x08get_urls\x18\x01 \x03(\t\x12\x0c\n\x04size\x18\x02 \x01(\x04\x12\r\n\x05start\x18\x03 \x01(\x04\x12\x0b\n\x03len\x18\x04 \x01(\x04\"S\n\x14VolumeGetFileRequest\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\r\n\x05start\x18\x03 \x01(\x04\x12\x0b\n\x03len\x18\x04 \x01(\x04\"w\n\x15VolumeGetFileResponse\x12\x0e\n\x04\x64\x61ta\x18\x01 \x01(\x0cH\x00\x12\x16\n\x0c\x64\x61ta_blob_id\x18\x02 \x01(\tH\x00\x12\x0c\n\x04size\x18\x03 \x01(\x04\x12\r\n\x05start\x18\x04 \x01(\x04\x12\x0b\n\x03len\x18\x05 \x01(\x04\x42\x0c\n\ndata_oneof\"\x89\x02\n\x18VolumeGetOrCreateRequest\x12\x1d\n\x0f\x64\x65ployment_name\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x34\n\tnamespace\x18\x02 \x01(\x0e\x32!.modal.client.DeploymentNamespace\x12\x18\n\x10\x65nvironment_name\x18\x03 \x01(\t\x12>\n\x14object_creation_type\x18\x04 \x01(\x0e\x32 .modal.client.ObjectCreationType\x12\x0e\n\x06\x61pp_id\x18\x05 \x01(\t\x12.\n\x07version\x18\x06 \x01(\x0e\x32\x1d.modal.client.VolumeFsVersion\"\x8e\x01\n\x19VolumeGetOrCreateResponse\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12.\n\x07version\x18\x02 \x01(\x0e\x32\x1d.modal.client.VolumeFsVersion\x12.\n\x08metadata\x18\x03 \x01(\x0b\x32\x1c.modal.client.VolumeMetadata\"+\n\x16VolumeHeartbeatRequest\x12\x11\n\tvolume_id\x18\x01 \x01(\t\"w\n\x17VolumeListFiles2Request\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x11\n\trecursive\x18\x04 \x01(\x08\x12\x18\n\x0bmax_entries\x18\x03 \x01(\rH\x00\x88\x01\x01\x42\x0e\n\x0c_max_entries\"D\n\x18VolumeListFiles2Response\x12(\n\x07\x65ntries\x18\x01 \x03(\x0b\x32\x17.modal.client.FileEntry\"v\n\x16VolumeListFilesRequest\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x11\n\trecursive\x18\x04 \x01(\x08\x12\x18\n\x0bmax_entries\x18\x03 \x01(\rH\x00\x88\x01\x01\x42\x0e\n\x0c_max_entries\"C\n\x17VolumeListFilesResponse\x12(\n\x07\x65ntries\x18\x01 \x03(\x0b\x32\x17.modal.client.FileEntry\"F\n\x0eVolumeListItem\x12\r\n\x05label\x18\x01 \x01(\t\x12\x11\n\tvolume_id\x18\x02 \x01(\t\x12\x12\n\ncreated_at\x18\x03 \x01(\x01\"-\n\x11VolumeListRequest\x12\x18\n\x10\x65nvironment_name\x18\x01 \x01(\t\"[\n\x12VolumeListResponse\x12+\n\x05items\x18\x01 \x03(\x0b\x32\x1c.modal.client.VolumeListItem\x12\x18\n\x10\x65nvironment_name\x18\x02 \x01(\t\"@\n\x0eVolumeMetadata\x12.\n\x07version\x18\x01 \x01(\x0e\x32\x1d.modal.client.VolumeFsVersion\"i\n\x0bVolumeMount\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x12\n\nmount_path\x18\x02 \x01(\t\x12 \n\x18\x61llow_background_commits\x18\x03 \x01(\x08\x12\x11\n\tread_only\x18\x04 \x01(\x08\"\xda\x02\n\x16VolumePutFiles2Request\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12\x38\n\x05\x66iles\x18\x02 \x03(\x0b\x32).modal.client.VolumePutFiles2Request.File\x12)\n!disallow_overwrite_existing_files\x18\x03 \x01(\x08\x1az\n\x04\x46ile\x12\x0c\n\x04path\x18\x01 \x01(\t\x12\x0c\n\x04size\x18\x02 \x01(\x04\x12:\n\x06\x62locks\x18\x03 \x03(\x0b\x32*.modal.client.VolumePutFiles2Request.Block\x12\x11\n\x04mode\x18\x04 \x01(\rH\x00\x88\x01\x01\x42\x07\n\x05_mode\x1aL\n\x05\x42lock\x12\x17\n\x0f\x63ontents_sha256\x18\x01 \x01(\x0c\x12\x19\n\x0cput_response\x18\x02 \x01(\x0cH\x00\x88\x01\x01\x42\x0f\n\r_put_response\"\xaf\x01\n\x17VolumePutFiles2Response\x12J\n\x0emissing_blocks\x18\x01 \x03(\x0b\x32\x32.modal.client.VolumePutFiles2Response.MissingBlock\x1aH\n\x0cMissingBlock\x12\x12\n\nfile_index\x18\x01 \x01(\x04\x12\x13\n\x0b\x62lock_index\x18\x02 \x01(\x04\x12\x0f\n\x07put_url\x18\x03 \x01(\t\"}\n\x15VolumePutFilesRequest\x12\x11\n\tvolume_id\x18\x01 \x01(\t\x12&\n\x05\x66iles\x18\x02 \x03(\x0b\x32\x17.modal.client.MountFile\x12)\n!disallow_overwrite_existing_files\x18\x03 \x01(\x08\"(\n\x13VolumeReloadRequest\x12\x11\n\tvolume_id\x18\x01 \x01(\t\"T\n\x18VolumeRemoveFile2Request\x12\x17\n\tvolume_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x11\n\trecursive\x18\x03 \x01(\x08\"S\n\x17VolumeRemoveFileRequest\x12\x17\n\tvolume_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x11\n\trecursive\x18\x03 \x01(\x08\"<\n\x13VolumeRenameRequest\x12\x17\n\tvolume_id\x18\x01 \x01(\tB\x04\x80\xb5\x18\x01\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xe8\x01\n\x07Warning\x12/\n\x04type\x18\x01 \x01(\x0e\x32!.modal.client.Warning.WarningType\x12\x0f\n\x07message\x18\x02 \x01(\t\"\x9a\x01\n\x0bWarningType\x12\x1c\n\x18WARNING_TYPE_UNSPECIFIED\x10\x00\x12#\n\x1fWARNING_TYPE_CLIENT_DEPRECATION\x10\x01\x12\x1f\n\x1bWARNING_TYPE_RESOURCE_LIMIT\x10\x02\x12\'\n#WARNING_TYPE_FUNCTION_CONFIGURATION\x10\x03\"R\n\nWebUrlInfo\x12\x11\n\ttruncated\x18\x01 \x01(\x08\x12\x1b\n\x0fhas_unique_hash\x18\x02 \x01(\x08\x42\x02\x18\x01\x12\x14\n\x0clabel_stolen\x18\x03 \x01(\x08\"\xc5\x02\n\rWebhookConfig\x12\'\n\x04type\x18\x01 \x01(\x0e\x32\x19.modal.client.WebhookType\x12\x0e\n\x06method\x18\x02 \x01(\t\x12\x18\n\x10requested_suffix\x18\x04 \x01(\t\x12\x32\n\nasync_mode\x18\x05 \x01(\x0e\x32\x1e.modal.client.WebhookAsyncMode\x12\x38\n\x0e\x63ustom_domains\x18\x06 \x03(\x0b\x32 .modal.client.CustomDomainConfig\x12\x17\n\x0fweb_server_port\x18\x07 \x01(\r\x12\"\n\x1aweb_server_startup_timeout\x18\x08 \x01(\x02\x12\x19\n\x11web_endpoint_docs\x18\t \x01(\x08\x12\x1b\n\x13requires_proxy_auth\x18\n \x01(\x08\"K\n\x1bWorkspaceNameLookupResponse\x12\x1a\n\x0eworkspace_name\x18\x01 \x01(\tB\x02\x18\x01\x12\x10\n\x08username\x18\x02 \x01(\t*\x83\x01\n\x13\x41ppDeployVisibility\x12%\n!APP_DEPLOY_VISIBILITY_UNSPECIFIED\x10\x00\x12#\n\x1f\x41PP_DEPLOY_VISIBILITY_WORKSPACE\x10\x01\x12 \n\x1c\x41PP_DEPLOY_VISIBILITY_PUBLIC\x10\x02*\xa1\x02\n\x13\x41ppDisconnectReason\x12%\n!APP_DISCONNECT_REASON_UNSPECIFIED\x10\x00\x12)\n%APP_DISCONNECT_REASON_LOCAL_EXCEPTION\x10\x01\x12,\n(APP_DISCONNECT_REASON_KEYBOARD_INTERRUPT\x10\x02\x12.\n*APP_DISCONNECT_REASON_ENTRYPOINT_COMPLETED\x10\x03\x12.\n*APP_DISCONNECT_REASON_DEPLOYMENT_EXCEPTION\x10\x04\x12*\n&APP_DISCONNECT_REASON_REMOTE_EXCEPTION\x10\x05*\x91\x02\n\x08\x41ppState\x12\x19\n\x15\x41PP_STATE_UNSPECIFIED\x10\x00\x12\x17\n\x13\x41PP_STATE_EPHEMERAL\x10\x01\x12\x16\n\x12\x41PP_STATE_DETACHED\x10\x02\x12\x16\n\x12\x41PP_STATE_DEPLOYED\x10\x03\x12\x16\n\x12\x41PP_STATE_STOPPING\x10\x04\x12\x15\n\x11\x41PP_STATE_STOPPED\x10\x05\x12\x1a\n\x16\x41PP_STATE_INITIALIZING\x10\x06\x12\x16\n\x12\x41PP_STATE_DISABLED\x10\x07\x12#\n\x1f\x41PP_STATE_DETACHED_DISCONNECTED\x10\x08\x12\x19\n\x11\x41PP_STATE_DERIVED\x10\t\x1a\x02\x08\x01*\x85\x01\n\rAppStopSource\x12\x1f\n\x1b\x41PP_STOP_SOURCE_UNSPECIFIED\x10\x00\x12\x17\n\x13\x41PP_STOP_SOURCE_CLI\x10\x01\x12!\n\x1d\x41PP_STOP_SOURCE_PYTHON_CLIENT\x10\x02\x12\x17\n\x13\x41PP_STOP_SOURCE_WEB\x10\x03*\x91\x01\n\x11\x43\x65rtificateStatus\x12\x1e\n\x1a\x43\x45RTIFICATE_STATUS_PENDING\x10\x00\x12\x1d\n\x19\x43\x45RTIFICATE_STATUS_ISSUED\x10\x01\x12\x1d\n\x19\x43\x45RTIFICATE_STATUS_FAILED\x10\x02\x12\x1e\n\x1a\x43\x45RTIFICATE_STATUS_REVOKED\x10\x03*\xb1\x01\n\x10\x43heckpointStatus\x12!\n\x1d\x43HECKPOINT_STATUS_UNSPECIFIED\x10\x00\x12\x1d\n\x19\x43HECKPOINT_STATUS_PENDING\x10\x01\x12 \n\x1c\x43HECKPOINT_STATUS_PROCESSING\x10\x02\x12\x1b\n\x17\x43HECKPOINT_STATUS_READY\x10\x03\x12\x1c\n\x18\x43HECKPOINT_STATUS_FAILED\x10\x04*\xcb\x01\n\nClientType\x12\x1b\n\x17\x43LIENT_TYPE_UNSPECIFIED\x10\x00\x12\x16\n\x12\x43LIENT_TYPE_CLIENT\x10\x01\x12\x16\n\x12\x43LIENT_TYPE_WORKER\x10\x02\x12\x19\n\x15\x43LIENT_TYPE_CONTAINER\x10\x03\x12\x1a\n\x16\x43LIENT_TYPE_WEB_SERVER\x10\x05\x12\x1f\n\x1b\x43LIENT_TYPE_NOTEBOOK_KERNEL\x10\x06\x12\x18\n\x14\x43LIENT_TYPE_LIBMODAL\x10\x07*\xa8\x01\n\rCloudProvider\x12\x1e\n\x1a\x43LOUD_PROVIDER_UNSPECIFIED\x10\x00\x12\x16\n\x12\x43LOUD_PROVIDER_AWS\x10\x01\x12\x16\n\x12\x43LOUD_PROVIDER_GCP\x10\x02\x12\x17\n\x13\x43LOUD_PROVIDER_AUTO\x10\x03\x12\x16\n\x12\x43LOUD_PROVIDER_OCI\x10\x04\"\x04\x08\x05\x10\x05\"\x04\x08\x06\x10\x06\"\x04\x08\x07\x10\x07\"\x04\x08\x08\x10\x08*Z\n\rDNSRecordType\x12\x15\n\x11\x44NS_RECORD_TYPE_A\x10\x00\x12\x17\n\x13\x44NS_RECORD_TYPE_TXT\x10\x01\x12\x19\n\x15\x44NS_RECORD_TYPE_CNAME\x10\x02*w\n\nDataFormat\x12\x1b\n\x17\x44\x41TA_FORMAT_UNSPECIFIED\x10\x00\x12\x16\n\x12\x44\x41TA_FORMAT_PICKLE\x10\x01\x12\x14\n\x10\x44\x41TA_FORMAT_ASGI\x10\x02\x12\x1e\n\x1a\x44\x41TA_FORMAT_GENERATOR_DONE\x10\x03*\x80\x01\n\x13\x44\x65ploymentNamespace\x12$\n DEPLOYMENT_NAMESPACE_UNSPECIFIED\x10\x00\x12\"\n\x1e\x44\x45PLOYMENT_NAMESPACE_WORKSPACE\x10\x01\x12\x1f\n\x1b\x44\x45PLOYMENT_NAMESPACE_GLOBAL\x10\x03*\x92\x01\n\x10\x45xecOutputOption\x12\"\n\x1e\x45XEC_OUTPUT_OPTION_UNSPECIFIED\x10\x00\x12\x1e\n\x1a\x45XEC_OUTPUT_OPTION_DEVNULL\x10\x01\x12\x1b\n\x17\x45XEC_OUTPUT_OPTION_PIPE\x10\x02\x12\x1d\n\x19\x45XEC_OUTPUT_OPTION_STDOUT\x10\x03*\x83\x01\n\x0e\x46ileDescriptor\x12\x1f\n\x1b\x46ILE_DESCRIPTOR_UNSPECIFIED\x10\x00\x12\x1a\n\x16\x46ILE_DESCRIPTOR_STDOUT\x10\x01\x12\x1a\n\x16\x46ILE_DESCRIPTOR_STDERR\x10\x02\x12\x18\n\x14\x46ILE_DESCRIPTOR_INFO\x10\x03*\xfb\x01\n\x1a\x46unctionCallInvocationType\x12-\n)FUNCTION_CALL_INVOCATION_TYPE_UNSPECIFIED\x10\x00\x12-\n)FUNCTION_CALL_INVOCATION_TYPE_SYNC_LEGACY\x10\x01\x12.\n*FUNCTION_CALL_INVOCATION_TYPE_ASYNC_LEGACY\x10\x02\x12\'\n#FUNCTION_CALL_INVOCATION_TYPE_ASYNC\x10\x03\x12&\n\"FUNCTION_CALL_INVOCATION_TYPE_SYNC\x10\x04*p\n\x10\x46unctionCallType\x12\"\n\x1e\x46UNCTION_CALL_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n\x18\x46UNCTION_CALL_TYPE_UNARY\x10\x01\x12\x1a\n\x16\x46UNCTION_CALL_TYPE_MAP\x10\x02*\xce\x01\n\x07GPUType\x12\x18\n\x14GPU_TYPE_UNSPECIFIED\x10\x00\x12\x0f\n\x0bGPU_TYPE_T4\x10\x01\x12\x11\n\rGPU_TYPE_A100\x10\x02\x12\x11\n\rGPU_TYPE_A10G\x10\x03\x12\x10\n\x0cGPU_TYPE_ANY\x10\x04\x12\x16\n\x12GPU_TYPE_A100_80GB\x10\x08\x12\x0f\n\x0bGPU_TYPE_L4\x10\t\x12\x11\n\rGPU_TYPE_H100\x10\n\x12\x11\n\rGPU_TYPE_L40S\x10\x0b\x12\x11\n\rGPU_TYPE_H200\x10\x0c*\xa0\x02\n\x12ObjectCreationType\x12$\n OBJECT_CREATION_TYPE_UNSPECIFIED\x10\x00\x12*\n&OBJECT_CREATION_TYPE_CREATE_IF_MISSING\x10\x01\x12.\n*OBJECT_CREATION_TYPE_CREATE_FAIL_IF_EXISTS\x10\x02\x12\x33\n/OBJECT_CREATION_TYPE_CREATE_OVERWRITE_IF_EXISTS\x10\x03\x12/\n+OBJECT_CREATION_TYPE_ANONYMOUS_OWNED_BY_APP\x10\x04\x12\"\n\x1eOBJECT_CREATION_TYPE_EPHEMERAL\x10\x05*\xef\x01\n\rParameterType\x12\x1a\n\x16PARAM_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11PARAM_TYPE_STRING\x10\x01\x12\x12\n\x0ePARAM_TYPE_INT\x10\x02\x12\x15\n\x11PARAM_TYPE_PICKLE\x10\x03\x12\x14\n\x10PARAM_TYPE_BYTES\x10\x04\x12\x16\n\x12PARAM_TYPE_UNKNOWN\x10\x05\x12\x13\n\x0fPARAM_TYPE_LIST\x10\x06\x12\x13\n\x0fPARAM_TYPE_DICT\x10\x07\x12\x13\n\x0fPARAM_TYPE_NONE\x10\x08\x12\x13\n\x0fPARAM_TYPE_BOOL\x10\t*>\n\x0cProgressType\x12\x19\n\x15IMAGE_SNAPSHOT_UPLOAD\x10\x00\x12\x13\n\x0f\x46UNCTION_QUEUED\x10\x01*\xa9\x01\n\rProxyIpStatus\x12\x1f\n\x1bPROXY_IP_STATUS_UNSPECIFIED\x10\x00\x12\x1c\n\x18PROXY_IP_STATUS_CREATING\x10\x01\x12\x1a\n\x16PROXY_IP_STATUS_ONLINE\x10\x02\x12\x1e\n\x1aPROXY_IP_STATUS_TERMINATED\x10\x03\x12\x1d\n\x19PROXY_IP_STATUS_UNHEALTHY\x10\x04*T\n\tProxyType\x12\x1a\n\x16PROXY_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11PROXY_TYPE_LEGACY\x10\x01\x12\x14\n\x10PROXY_TYPE_VPROX\x10\x02*x\n\x11RateLimitInterval\x12#\n\x1fRATE_LIMIT_INTERVAL_UNSPECIFIED\x10\x00\x12\x1e\n\x1aRATE_LIMIT_INTERVAL_SECOND\x10\x01\x12\x1e\n\x1aRATE_LIMIT_INTERVAL_MINUTE\x10\x02*\xb2\x01\n\x10RegistryAuthType\x12\"\n\x1eREGISTRY_AUTH_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n\x16REGISTRY_AUTH_TYPE_AWS\x10\x01\x12\x1a\n\x16REGISTRY_AUTH_TYPE_GCP\x10\x02\x12\x1d\n\x19REGISTRY_AUTH_TYPE_PUBLIC\x10\x03\x12#\n\x1fREGISTRY_AUTH_TYPE_STATIC_CREDS\x10\x04*6\n\nSeekWhence\x12\x0c\n\x08SEEK_SET\x10\x00\x12\x0c\n\x08SEEK_CUR\x10\x01\x12\x0c\n\x08SEEK_END\x10\x02*\xa8\x03\n\x0fSystemErrorCode\x12!\n\x1dSYSTEM_ERROR_CODE_UNSPECIFIED\x10\x00\x12\x1a\n\x16SYSTEM_ERROR_CODE_PERM\x10\x01\x12\x1b\n\x17SYSTEM_ERROR_CODE_NOENT\x10\x02\x12\x18\n\x14SYSTEM_ERROR_CODE_IO\x10\x05\x12\x1a\n\x16SYSTEM_ERROR_CODE_NXIO\x10\x06\x12\x1b\n\x17SYSTEM_ERROR_CODE_NOMEM\x10\x0c\x12\x1b\n\x17SYSTEM_ERROR_CODE_ACCES\x10\r\x12\x1b\n\x17SYSTEM_ERROR_CODE_EXIST\x10\x11\x12\x1c\n\x18SYSTEM_ERROR_CODE_NOTDIR\x10\x14\x12\x1b\n\x17SYSTEM_ERROR_CODE_ISDIR\x10\x15\x12\x1b\n\x17SYSTEM_ERROR_CODE_INVAL\x10\x16\x12\x1b\n\x17SYSTEM_ERROR_CODE_MFILE\x10\x18\x12\x1a\n\x16SYSTEM_ERROR_CODE_FBIG\x10\x1b\x12\x1b\n\x17SYSTEM_ERROR_CODE_NOSPC\x10\x1c*\xdc\x02\n\tTaskState\x12\x1a\n\x16TASK_STATE_UNSPECIFIED\x10\x00\x12\x16\n\x12TASK_STATE_CREATED\x10\x06\x12\x15\n\x11TASK_STATE_QUEUED\x10\x01\x12\x1e\n\x1aTASK_STATE_WORKER_ASSIGNED\x10\x02\x12\x1c\n\x18TASK_STATE_LOADING_IMAGE\x10\x03\x12\x15\n\x11TASK_STATE_ACTIVE\x10\x04\x12\x18\n\x14TASK_STATE_COMPLETED\x10\x05\x12!\n\x1dTASK_STATE_CREATING_CONTAINER\x10\x07\x12\x13\n\x0fTASK_STATE_IDLE\x10\x08\x12\x1a\n\x16TASK_STATE_PREEMPTIBLE\x10\t\x12\x18\n\x14TASK_STATE_PREEMPTED\x10\n\x12\'\n#TASK_STATE_LOADING_CHECKPOINT_IMAGE\x10\x0b*=\n\nTunnelType\x12\x1b\n\x17TUNNEL_TYPE_UNSPECIFIED\x10\x00\x12\x12\n\x0eTUNNEL_TYPE_H2\x10\x01*h\n\x0fVolumeFsVersion\x12!\n\x1dVOLUME_FS_VERSION_UNSPECIFIED\x10\x00\x12\x18\n\x14VOLUME_FS_VERSION_V1\x10\x01\x12\x18\n\x14VOLUME_FS_VERSION_V2\x10\x02*\x9a\x01\n\x10WebhookAsyncMode\x12\"\n\x1eWEBHOOK_ASYNC_MODE_UNSPECIFIED\x10\x00\x12\x1f\n\x1bWEBHOOK_ASYNC_MODE_DISABLED\x10\x02\x12\x1e\n\x1aWEBHOOK_ASYNC_MODE_TRIGGER\x10\x03\x12\x1b\n\x17WEBHOOK_ASYNC_MODE_AUTO\x10\x04\"\x04\x08\x01\x10\x01*\x99\x01\n\x0bWebhookType\x12\x1c\n\x18WEBHOOK_TYPE_UNSPECIFIED\x10\x00\x12\x19\n\x15WEBHOOK_TYPE_ASGI_APP\x10\x01\x12\x19\n\x15WEBHOOK_TYPE_FUNCTION\x10\x02\x12\x19\n\x15WEBHOOK_TYPE_WSGI_APP\x10\x03\x12\x1b\n\x17WEBHOOK_TYPE_WEB_SERVER\x10\x04\x32\xf7k\n\x0bModalClient\x12W\n\x13\x41ppClientDisconnect\x12(.modal.client.AppClientDisconnectRequest\x1a\x16.google.protobuf.Empty\x12L\n\tAppCreate\x12\x1e.modal.client.AppCreateRequest\x1a\x1f.modal.client.AppCreateResponse\x12L\n\tAppDeploy\x12\x1e.modal.client.AppDeployRequest\x1a\x1f.modal.client.AppDeployResponse\x12m\n\x14\x41ppDeploymentHistory\x12).modal.client.AppDeploymentHistoryRequest\x1a*.modal.client.AppDeploymentHistoryResponse\x12s\n\x16\x41ppGetByDeploymentName\x12+.modal.client.AppGetByDeploymentNameRequest\x1a,.modal.client.AppGetByDeploymentNameResponse\x12U\n\x0c\x41ppGetLayout\x12!.modal.client.AppGetLayoutRequest\x1a\".modal.client.AppGetLayoutResponse\x12L\n\nAppGetLogs\x12\x1f.modal.client.AppGetLogsRequest\x1a\x1b.modal.client.TaskLogsBatch0\x01\x12X\n\rAppGetObjects\x12\".modal.client.AppGetObjectsRequest\x1a#.modal.client.AppGetObjectsResponse\x12[\n\x0e\x41ppGetOrCreate\x12#.modal.client.AppGetOrCreateRequest\x1a$.modal.client.AppGetOrCreateResponse\x12I\n\x0c\x41ppHeartbeat\x12!.modal.client.AppHeartbeatRequest\x1a\x16.google.protobuf.Empty\x12\x46\n\x07\x41ppList\x12\x1c.modal.client.AppListRequest\x1a\x1d.modal.client.AppListResponse\x12L\n\tAppLookup\x12\x1e.modal.client.AppLookupRequest\x1a\x1f.modal.client.AppLookupResponse\x12O\n\nAppPublish\x12\x1f.modal.client.AppPublishRequest\x1a .modal.client.AppPublishResponse\x12G\n\x0b\x41ppRollback\x12 .modal.client.AppRollbackRequest\x1a\x16.google.protobuf.Empty\x12K\n\rAppSetObjects\x12\".modal.client.AppSetObjectsRequest\x1a\x16.google.protobuf.Empty\x12?\n\x07\x41ppStop\x12\x1c.modal.client.AppStopRequest\x1a\x16.google.protobuf.Empty\x12U\n\x0c\x41ttemptAwait\x12!.modal.client.AttemptAwaitRequest\x1a\".modal.client.AttemptAwaitResponse\x12U\n\x0c\x41ttemptRetry\x12!.modal.client.AttemptRetryRequest\x1a\".modal.client.AttemptRetryResponse\x12U\n\x0c\x41ttemptStart\x12!.modal.client.AttemptStartRequest\x1a\".modal.client.AttemptStartResponse\x12O\n\nBlobCreate\x12\x1f.modal.client.BlobCreateRequest\x1a .modal.client.BlobCreateResponse\x12\x46\n\x07\x42lobGet\x12\x1c.modal.client.BlobGetRequest\x1a\x1d.modal.client.BlobGetResponse\x12R\n\x0b\x43lassCreate\x12 .modal.client.ClassCreateRequest\x1a!.modal.client.ClassCreateResponse\x12I\n\x08\x43lassGet\x12\x1d.modal.client.ClassGetRequest\x1a\x1e.modal.client.ClassGetResponse\x12H\n\x0b\x43lientHello\x12\x16.google.protobuf.Empty\x1a!.modal.client.ClientHelloResponse\x12O\n\nClusterGet\x12\x1f.modal.client.ClusterGetRequest\x1a .modal.client.ClusterGetResponse\x12R\n\x0b\x43lusterList\x12 .modal.client.ClusterListRequest\x1a!.modal.client.ClusterListResponse\x12W\n\x13\x43ontainerCheckpoint\x12(.modal.client.ContainerCheckpointRequest\x1a\x16.google.protobuf.Empty\x12X\n\rContainerExec\x12\".modal.client.ContainerExecRequest\x1a#.modal.client.ContainerExecResponse\x12i\n\x16\x43ontainerExecGetOutput\x12+.modal.client.ContainerExecGetOutputRequest\x1a .modal.client.RuntimeOutputBatch0\x01\x12[\n\x15\x43ontainerExecPutInput\x12*.modal.client.ContainerExecPutInputRequest\x1a\x16.google.protobuf.Empty\x12\x64\n\x11\x43ontainerExecWait\x12&.modal.client.ContainerExecWaitRequest\x1a\'.modal.client.ContainerExecWaitResponse\x12v\n\x17\x43ontainerFilesystemExec\x12,.modal.client.ContainerFilesystemExecRequest\x1a-.modal.client.ContainerFilesystemExecResponse\x12\x87\x01\n ContainerFilesystemExecGetOutput\x12\x35.modal.client.ContainerFilesystemExecGetOutputRequest\x1a*.modal.client.FilesystemRuntimeOutputBatch0\x01\x12g\n\x12\x43ontainerHeartbeat\x12\'.modal.client.ContainerHeartbeatRequest\x1a(.modal.client.ContainerHeartbeatResponse\x12@\n\x0e\x43ontainerHello\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\x12I\n\x0c\x43ontainerLog\x12!.modal.client.ContainerLogRequest\x1a\x16.google.protobuf.Empty\x12X\n\rContainerStop\x12\".modal.client.ContainerStopRequest\x1a#.modal.client.ContainerStopResponse\x12\x43\n\tDictClear\x12\x1e.modal.client.DictClearRequest\x1a\x16.google.protobuf.Empty\x12U\n\x0c\x44ictContains\x12!.modal.client.DictContainsRequest\x1a\".modal.client.DictContainsResponse\x12L\n\x0c\x44ictContents\x12!.modal.client.DictContentsRequest\x1a\x17.modal.client.DictEntry0\x01\x12\x45\n\nDictDelete\x12\x1f.modal.client.DictDeleteRequest\x1a\x16.google.protobuf.Empty\x12\x46\n\x07\x44ictGet\x12\x1c.modal.client.DictGetRequest\x1a\x1d.modal.client.DictGetResponse\x12^\n\x0f\x44ictGetOrCreate\x12$.modal.client.DictGetOrCreateRequest\x1a%.modal.client.DictGetOrCreateResponse\x12K\n\rDictHeartbeat\x12\".modal.client.DictHeartbeatRequest\x1a\x16.google.protobuf.Empty\x12\x46\n\x07\x44ictLen\x12\x1c.modal.client.DictLenRequest\x1a\x1d.modal.client.DictLenResponse\x12I\n\x08\x44ictList\x12\x1d.modal.client.DictListRequest\x1a\x1e.modal.client.DictListResponse\x12\x46\n\x07\x44ictPop\x12\x1c.modal.client.DictPopRequest\x1a\x1d.modal.client.DictPopResponse\x12O\n\nDictUpdate\x12\x1f.modal.client.DictUpdateRequest\x1a .modal.client.DictUpdateResponse\x12v\n\x17\x44omainCertificateVerify\x12,.modal.client.DomainCertificateVerifyRequest\x1a-.modal.client.DomainCertificateVerifyResponse\x12U\n\x0c\x44omainCreate\x12!.modal.client.DomainCreateRequest\x1a\".modal.client.DomainCreateResponse\x12O\n\nDomainList\x12\x1f.modal.client.DomainListRequest\x1a .modal.client.DomainListResponse\x12S\n\x11\x45nvironmentCreate\x12&.modal.client.EnvironmentCreateRequest\x1a\x16.google.protobuf.Empty\x12S\n\x11\x45nvironmentDelete\x12&.modal.client.EnvironmentDeleteRequest\x1a\x16.google.protobuf.Empty\x12s\n\x16\x45nvironmentGetOrCreate\x12+.modal.client.EnvironmentGetOrCreateRequest\x1a,.modal.client.EnvironmentGetOrCreateResponse\x12P\n\x0f\x45nvironmentList\x12\x16.google.protobuf.Empty\x1a%.modal.client.EnvironmentListResponse\x12^\n\x11\x45nvironmentUpdate\x12&.modal.client.EnvironmentUpdateRequest\x1a!.modal.client.EnvironmentListItem\x12j\n\x13\x46unctionAsyncInvoke\x12(.modal.client.FunctionAsyncInvokeRequest\x1a).modal.client.FunctionAsyncInvokeResponse\x12g\n\x12\x46unctionBindParams\x12\'.modal.client.FunctionBindParamsRequest\x1a(.modal.client.FunctionBindParamsResponse\x12U\n\x12\x46unctionCallCancel\x12\'.modal.client.FunctionCallCancelRequest\x1a\x16.google.protobuf.Empty\x12\\\n\x15\x46unctionCallGetDataIn\x12(.modal.client.FunctionCallGetDataRequest\x1a\x17.modal.client.DataChunk0\x01\x12]\n\x16\x46unctionCallGetDataOut\x12(.modal.client.FunctionCallGetDataRequest\x1a\x17.modal.client.DataChunk0\x01\x12\x61\n\x10\x46unctionCallList\x12%.modal.client.FunctionCallListRequest\x1a&.modal.client.FunctionCallListResponse\x12Z\n\x16\x46unctionCallPutDataOut\x12(.modal.client.FunctionCallPutDataRequest\x1a\x16.google.protobuf.Empty\x12[\n\x0e\x46unctionCreate\x12#.modal.client.FunctionCreateRequest\x1a$.modal.client.FunctionCreateResponse\x12R\n\x0b\x46unctionGet\x12 .modal.client.FunctionGetRequest\x1a!.modal.client.FunctionGetResponse\x12m\n\x14\x46unctionGetCallGraph\x12).modal.client.FunctionGetCallGraphRequest\x1a*.modal.client.FunctionGetCallGraphResponse\x12\x64\n\x17\x46unctionGetCurrentStats\x12,.modal.client.FunctionGetCurrentStatsRequest\x1a\x1b.modal.client.FunctionStats\x12\x88\x01\n\x1d\x46unctionGetDynamicConcurrency\x12\x32.modal.client.FunctionGetDynamicConcurrencyRequest\x1a\x33.modal.client.FunctionGetDynamicConcurrencyResponse\x12\x64\n\x11\x46unctionGetInputs\x12&.modal.client.FunctionGetInputsRequest\x1a\'.modal.client.FunctionGetInputsResponse\x12g\n\x12\x46unctionGetOutputs\x12\'.modal.client.FunctionGetOutputsRequest\x1a(.modal.client.FunctionGetOutputsResponse\x12p\n\x15\x46unctionGetSerialized\x12*.modal.client.FunctionGetSerializedRequest\x1a+.modal.client.FunctionGetSerializedResponse\x12R\n\x0b\x46unctionMap\x12 .modal.client.FunctionMapRequest\x1a!.modal.client.FunctionMapResponse\x12\x64\n\x11\x46unctionPrecreate\x12&.modal.client.FunctionPrecreateRequest\x1a\'.modal.client.FunctionPrecreateResponse\x12\x64\n\x11\x46unctionPutInputs\x12&.modal.client.FunctionPutInputsRequest\x1a\'.modal.client.FunctionPutInputsResponse\x12U\n\x12\x46unctionPutOutputs\x12\'.modal.client.FunctionPutOutputsRequest\x1a\x16.google.protobuf.Empty\x12j\n\x13\x46unctionRetryInputs\x12(.modal.client.FunctionRetryInputsRequest\x1a).modal.client.FunctionRetryInputsResponse\x12G\n\x15\x46unctionStartPtyShell\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\x12\x8b\x01\n\x1e\x46unctionUpdateSchedulingParams\x12\x33.modal.client.FunctionUpdateSchedulingParamsRequest\x1a\x34.modal.client.FunctionUpdateSchedulingParamsResponse\x12R\n\x0bImageFromId\x12 .modal.client.ImageFromIdRequest\x1a!.modal.client.ImageFromIdResponse\x12\x61\n\x10ImageGetOrCreate\x12%.modal.client.ImageGetOrCreateRequest\x1a&.modal.client.ImageGetOrCreateResponse\x12i\n\x12ImageJoinStreaming\x12\'.modal.client.ImageJoinStreamingRequest\x1a(.modal.client.ImageJoinStreamingResponse0\x01\x12\x61\n\x10MountGetOrCreate\x12%.modal.client.MountGetOrCreateRequest\x1a&.modal.client.MountGetOrCreateResponse\x12U\n\x0cMountPutFile\x12!.modal.client.MountPutFileRequest\x1a\".modal.client.MountPutFileResponse\x12i\n\x1cNotebookKernelPublishResults\x12\x31.modal.client.NotebookKernelPublishResultsRequest\x1a\x16.google.protobuf.Empty\x12O\n\nProxyAddIp\x12\x1f.modal.client.ProxyAddIpRequest\x1a .modal.client.ProxyAddIpResponse\x12R\n\x0bProxyCreate\x12 .modal.client.ProxyCreateRequest\x1a!.modal.client.ProxyCreateResponse\x12G\n\x0bProxyDelete\x12 .modal.client.ProxyDeleteRequest\x1a\x16.google.protobuf.Empty\x12I\n\x08ProxyGet\x12\x1d.modal.client.ProxyGetRequest\x1a\x1e.modal.client.ProxyGetResponse\x12\x61\n\x10ProxyGetOrCreate\x12%.modal.client.ProxyGetOrCreateRequest\x1a&.modal.client.ProxyGetOrCreateResponse\x12\x44\n\tProxyList\x12\x16.google.protobuf.Empty\x1a\x1f.modal.client.ProxyListResponse\x12K\n\rProxyRemoveIp\x12\".modal.client.ProxyRemoveIpRequest\x1a\x16.google.protobuf.Empty\x12\x45\n\nQueueClear\x12\x1f.modal.client.QueueClearRequest\x1a\x16.google.protobuf.Empty\x12G\n\x0bQueueDelete\x12 .modal.client.QueueDeleteRequest\x1a\x16.google.protobuf.Empty\x12I\n\x08QueueGet\x12\x1d.modal.client.QueueGetRequest\x1a\x1e.modal.client.QueueGetResponse\x12\x61\n\x10QueueGetOrCreate\x12%.modal.client.QueueGetOrCreateRequest\x1a&.modal.client.QueueGetOrCreateResponse\x12M\n\x0eQueueHeartbeat\x12#.modal.client.QueueHeartbeatRequest\x1a\x16.google.protobuf.Empty\x12I\n\x08QueueLen\x12\x1d.modal.client.QueueLenRequest\x1a\x1e.modal.client.QueueLenResponse\x12L\n\tQueueList\x12\x1e.modal.client.QueueListRequest\x1a\x1f.modal.client.QueueListResponse\x12[\n\x0eQueueNextItems\x12#.modal.client.QueueNextItemsRequest\x1a$.modal.client.QueueNextItemsResponse\x12\x41\n\x08QueuePut\x12\x1d.modal.client.QueuePutRequest\x1a\x16.google.protobuf.Empty\x12X\n\rSandboxCreate\x12\".modal.client.SandboxCreateRequest\x1a#.modal.client.SandboxCreateResponse\x12T\n\x0eSandboxGetLogs\x12#.modal.client.SandboxGetLogsRequest\x1a\x1b.modal.client.TaskLogsBatch0\x01\x12v\n\x17SandboxGetResourceUsage\x12,.modal.client.SandboxGetResourceUsageRequest\x1a-.modal.client.SandboxGetResourceUsageResponse\x12\x61\n\x10SandboxGetTaskId\x12%.modal.client.SandboxGetTaskIdRequest\x1a&.modal.client.SandboxGetTaskIdResponse\x12\x64\n\x11SandboxGetTunnels\x12&.modal.client.SandboxGetTunnelsRequest\x1a\'.modal.client.SandboxGetTunnelsResponse\x12R\n\x0bSandboxList\x12 .modal.client.SandboxListRequest\x1a!.modal.client.SandboxListResponse\x12[\n\x0eSandboxRestore\x12#.modal.client.SandboxRestoreRequest\x1a$.modal.client.SandboxRestoreResponse\x12^\n\x0fSandboxSnapshot\x12$.modal.client.SandboxSnapshotRequest\x1a%.modal.client.SandboxSnapshotResponse\x12\x64\n\x11SandboxSnapshotFs\x12&.modal.client.SandboxSnapshotFsRequest\x1a\'.modal.client.SandboxSnapshotFsResponse\x12g\n\x12SandboxSnapshotGet\x12\'.modal.client.SandboxSnapshotGetRequest\x1a(.modal.client.SandboxSnapshotGetResponse\x12j\n\x13SandboxSnapshotWait\x12(.modal.client.SandboxSnapshotWaitRequest\x1a).modal.client.SandboxSnapshotWaitResponse\x12\x64\n\x11SandboxStdinWrite\x12&.modal.client.SandboxStdinWriteRequest\x1a\'.modal.client.SandboxStdinWriteResponse\x12M\n\x0eSandboxTagsSet\x12#.modal.client.SandboxTagsSetRequest\x1a\x16.google.protobuf.Empty\x12\x61\n\x10SandboxTerminate\x12%.modal.client.SandboxTerminateRequest\x1a&.modal.client.SandboxTerminateResponse\x12R\n\x0bSandboxWait\x12 .modal.client.SandboxWaitRequest\x1a!.modal.client.SandboxWaitResponse\x12I\n\x0cSecretDelete\x12!.modal.client.SecretDeleteRequest\x1a\x16.google.protobuf.Empty\x12\x64\n\x11SecretGetOrCreate\x12&.modal.client.SecretGetOrCreateRequest\x1a\'.modal.client.SecretGetOrCreateResponse\x12O\n\nSecretList\x12\x1f.modal.client.SecretListRequest\x1a .modal.client.SecretListResponse\x12U\n\x12SharedVolumeDelete\x12\'.modal.client.SharedVolumeDeleteRequest\x1a\x16.google.protobuf.Empty\x12j\n\x13SharedVolumeGetFile\x12(.modal.client.SharedVolumeGetFileRequest\x1a).modal.client.SharedVolumeGetFileResponse\x12v\n\x17SharedVolumeGetOrCreate\x12,.modal.client.SharedVolumeGetOrCreateRequest\x1a-.modal.client.SharedVolumeGetOrCreateResponse\x12[\n\x15SharedVolumeHeartbeat\x12*.modal.client.SharedVolumeHeartbeatRequest\x1a\x16.google.protobuf.Empty\x12\x61\n\x10SharedVolumeList\x12%.modal.client.SharedVolumeListRequest\x1a&.modal.client.SharedVolumeListResponse\x12p\n\x15SharedVolumeListFiles\x12*.modal.client.SharedVolumeListFilesRequest\x1a+.modal.client.SharedVolumeListFilesResponse\x12x\n\x1bSharedVolumeListFilesStream\x12*.modal.client.SharedVolumeListFilesRequest\x1a+.modal.client.SharedVolumeListFilesResponse0\x01\x12j\n\x13SharedVolumePutFile\x12(.modal.client.SharedVolumePutFileRequest\x1a).modal.client.SharedVolumePutFileResponse\x12]\n\x16SharedVolumeRemoveFile\x12+.modal.client.SharedVolumeRemoveFileRequest\x1a\x16.google.protobuf.Empty\x12\x61\n\x10TaskClusterHello\x12%.modal.client.TaskClusterHelloRequest\x1a&.modal.client.TaskClusterHelloResponse\x12T\n\x11TaskCurrentInputs\x12\x16.google.protobuf.Empty\x1a\'.modal.client.TaskCurrentInputsResponse\x12I\n\x08TaskList\x12\x1d.modal.client.TaskListRequest\x1a\x1e.modal.client.TaskListResponse\x12\x45\n\nTaskResult\x12\x1f.modal.client.TaskResultRequest\x1a\x16.google.protobuf.Empty\x12^\n\x0fTokenFlowCreate\x12$.modal.client.TokenFlowCreateRequest\x1a%.modal.client.TokenFlowCreateResponse\x12X\n\rTokenFlowWait\x12\".modal.client.TokenFlowWaitRequest\x1a#.modal.client.TokenFlowWaitResponse\x12R\n\x0bTunnelStart\x12 .modal.client.TunnelStartRequest\x1a!.modal.client.TunnelStartResponse\x12O\n\nTunnelStop\x12\x1f.modal.client.TunnelStopRequest\x1a .modal.client.TunnelStopResponse\x12U\n\x0cVolumeCommit\x12!.modal.client.VolumeCommitRequest\x1a\".modal.client.VolumeCommitResponse\x12O\n\x0fVolumeCopyFiles\x12$.modal.client.VolumeCopyFilesRequest\x1a\x16.google.protobuf.Empty\x12Q\n\x10VolumeCopyFiles2\x12%.modal.client.VolumeCopyFiles2Request\x1a\x16.google.protobuf.Empty\x12I\n\x0cVolumeDelete\x12!.modal.client.VolumeDeleteRequest\x1a\x16.google.protobuf.Empty\x12X\n\rVolumeGetFile\x12\".modal.client.VolumeGetFileRequest\x1a#.modal.client.VolumeGetFileResponse\x12[\n\x0eVolumeGetFile2\x12#.modal.client.VolumeGetFile2Request\x1a$.modal.client.VolumeGetFile2Response\x12\x64\n\x11VolumeGetOrCreate\x12&.modal.client.VolumeGetOrCreateRequest\x1a\'.modal.client.VolumeGetOrCreateResponse\x12O\n\x0fVolumeHeartbeat\x12$.modal.client.VolumeHeartbeatRequest\x1a\x16.google.protobuf.Empty\x12O\n\nVolumeList\x12\x1f.modal.client.VolumeListRequest\x1a .modal.client.VolumeListResponse\x12`\n\x0fVolumeListFiles\x12$.modal.client.VolumeListFilesRequest\x1a%.modal.client.VolumeListFilesResponse0\x01\x12\x63\n\x10VolumeListFiles2\x12%.modal.client.VolumeListFiles2Request\x1a&.modal.client.VolumeListFiles2Response0\x01\x12M\n\x0eVolumePutFiles\x12#.modal.client.VolumePutFilesRequest\x1a\x16.google.protobuf.Empty\x12^\n\x0fVolumePutFiles2\x12$.modal.client.VolumePutFiles2Request\x1a%.modal.client.VolumePutFiles2Response\x12I\n\x0cVolumeReload\x12!.modal.client.VolumeReloadRequest\x1a\x16.google.protobuf.Empty\x12Q\n\x10VolumeRemoveFile\x12%.modal.client.VolumeRemoveFileRequest\x1a\x16.google.protobuf.Empty\x12S\n\x11VolumeRemoveFile2\x12&.modal.client.VolumeRemoveFile2Request\x1a\x16.google.protobuf.Empty\x12I\n\x0cVolumeRename\x12!.modal.client.VolumeRenameRequest\x1a\x16.google.protobuf.Empty\x12X\n\x13WorkspaceNameLookup\x12\x16.google.protobuf.Empty\x1a).modal.client.WorkspaceNameLookupResponseB&Z$github.com/modal-labs/modal/go/protob\x06proto3')

_APPDEPLOYVISIBILITY = DESCRIPTOR.enum_types_by_name['AppDeployVisibility']
AppDeployVisibility = enum_type_wrapper.EnumTypeWrapper(_APPDEPLOYVISIBILITY)
_APPDISCONNECTREASON = DESCRIPTOR.enum_types_by_name['AppDisconnectReason']
AppDisconnectReason = enum_type_wrapper.EnumTypeWrapper(_APPDISCONNECTREASON)
_APPSTATE = DESCRIPTOR.enum_types_by_name['AppState']
AppState = enum_type_wrapper.EnumTypeWrapper(_APPSTATE)
_APPSTOPSOURCE = DESCRIPTOR.enum_types_by_name['AppStopSource']
AppStopSource = enum_type_wrapper.EnumTypeWrapper(_APPSTOPSOURCE)
_CERTIFICATESTATUS = DESCRIPTOR.enum_types_by_name['CertificateStatus']
CertificateStatus = enum_type_wrapper.EnumTypeWrapper(_CERTIFICATESTATUS)
_CHECKPOINTSTATUS = DESCRIPTOR.enum_types_by_name['CheckpointStatus']
CheckpointStatus = enum_type_wrapper.EnumTypeWrapper(_CHECKPOINTSTATUS)
_CLIENTTYPE = DESCRIPTOR.enum_types_by_name['ClientType']
ClientType = enum_type_wrapper.EnumTypeWrapper(_CLIENTTYPE)
_CLOUDPROVIDER = DESCRIPTOR.enum_types_by_name['CloudProvider']
CloudProvider = enum_type_wrapper.EnumTypeWrapper(_CLOUDPROVIDER)
_DNSRECORDTYPE = DESCRIPTOR.enum_types_by_name['DNSRecordType']
DNSRecordType = enum_type_wrapper.EnumTypeWrapper(_DNSRECORDTYPE)
_DATAFORMAT = DESCRIPTOR.enum_types_by_name['DataFormat']
DataFormat = enum_type_wrapper.EnumTypeWrapper(_DATAFORMAT)
_DEPLOYMENTNAMESPACE = DESCRIPTOR.enum_types_by_name['DeploymentNamespace']
DeploymentNamespace = enum_type_wrapper.EnumTypeWrapper(_DEPLOYMENTNAMESPACE)
_EXECOUTPUTOPTION = DESCRIPTOR.enum_types_by_name['ExecOutputOption']
ExecOutputOption = enum_type_wrapper.EnumTypeWrapper(_EXECOUTPUTOPTION)
_FILEDESCRIPTOR = DESCRIPTOR.enum_types_by_name['FileDescriptor']
FileDescriptor = enum_type_wrapper.EnumTypeWrapper(_FILEDESCRIPTOR)
_FUNCTIONCALLINVOCATIONTYPE = DESCRIPTOR.enum_types_by_name['FunctionCallInvocationType']
FunctionCallInvocationType = enum_type_wrapper.EnumTypeWrapper(_FUNCTIONCALLINVOCATIONTYPE)
_FUNCTIONCALLTYPE = DESCRIPTOR.enum_types_by_name['FunctionCallType']
FunctionCallType = enum_type_wrapper.EnumTypeWrapper(_FUNCTIONCALLTYPE)
_GPUTYPE = DESCRIPTOR.enum_types_by_name['GPUType']
GPUType = enum_type_wrapper.EnumTypeWrapper(_GPUTYPE)
_OBJECTCREATIONTYPE = DESCRIPTOR.enum_types_by_name['ObjectCreationType']
ObjectCreationType = enum_type_wrapper.EnumTypeWrapper(_OBJECTCREATIONTYPE)
_PARAMETERTYPE = DESCRIPTOR.enum_types_by_name['ParameterType']
ParameterType = enum_type_wrapper.EnumTypeWrapper(_PARAMETERTYPE)
_PROGRESSTYPE = DESCRIPTOR.enum_types_by_name['ProgressType']
ProgressType = enum_type_wrapper.EnumTypeWrapper(_PROGRESSTYPE)
_PROXYIPSTATUS = DESCRIPTOR.enum_types_by_name['ProxyIpStatus']
ProxyIpStatus = enum_type_wrapper.EnumTypeWrapper(_PROXYIPSTATUS)
_PROXYTYPE = DESCRIPTOR.enum_types_by_name['ProxyType']
ProxyType = enum_type_wrapper.EnumTypeWrapper(_PROXYTYPE)
_RATELIMITINTERVAL = DESCRIPTOR.enum_types_by_name['RateLimitInterval']
RateLimitInterval = enum_type_wrapper.EnumTypeWrapper(_RATELIMITINTERVAL)
_REGISTRYAUTHTYPE = DESCRIPTOR.enum_types_by_name['RegistryAuthType']
RegistryAuthType = enum_type_wrapper.EnumTypeWrapper(_REGISTRYAUTHTYPE)
_SEEKWHENCE = DESCRIPTOR.enum_types_by_name['SeekWhence']
SeekWhence = enum_type_wrapper.EnumTypeWrapper(_SEEKWHENCE)
_SYSTEMERRORCODE = DESCRIPTOR.enum_types_by_name['SystemErrorCode']
SystemErrorCode = enum_type_wrapper.EnumTypeWrapper(_SYSTEMERRORCODE)
_TASKSTATE = DESCRIPTOR.enum_types_by_name['TaskState']
TaskState = enum_type_wrapper.EnumTypeWrapper(_TASKSTATE)
_TUNNELTYPE = DESCRIPTOR.enum_types_by_name['TunnelType']
TunnelType = enum_type_wrapper.EnumTypeWrapper(_TUNNELTYPE)
_VOLUMEFSVERSION = DESCRIPTOR.enum_types_by_name['VolumeFsVersion']
VolumeFsVersion = enum_type_wrapper.EnumTypeWrapper(_VOLUMEFSVERSION)
_WEBHOOKASYNCMODE = DESCRIPTOR.enum_types_by_name['WebhookAsyncMode']
WebhookAsyncMode = enum_type_wrapper.EnumTypeWrapper(_WEBHOOKASYNCMODE)
_WEBHOOKTYPE = DESCRIPTOR.enum_types_by_name['WebhookType']
WebhookType = enum_type_wrapper.EnumTypeWrapper(_WEBHOOKTYPE)
APP_DEPLOY_VISIBILITY_UNSPECIFIED = 0
APP_DEPLOY_VISIBILITY_WORKSPACE = 1
APP_DEPLOY_VISIBILITY_PUBLIC = 2
APP_DISCONNECT_REASON_UNSPECIFIED = 0
APP_DISCONNECT_REASON_LOCAL_EXCEPTION = 1
APP_DISCONNECT_REASON_KEYBOARD_INTERRUPT = 2
APP_DISCONNECT_REASON_ENTRYPOINT_COMPLETED = 3
APP_DISCONNECT_REASON_DEPLOYMENT_EXCEPTION = 4
APP_DISCONNECT_REASON_REMOTE_EXCEPTION = 5
APP_STATE_UNSPECIFIED = 0
APP_STATE_EPHEMERAL = 1
APP_STATE_DETACHED = 2
APP_STATE_DEPLOYED = 3
APP_STATE_STOPPING = 4
APP_STATE_STOPPED = 5
APP_STATE_INITIALIZING = 6
APP_STATE_DISABLED = 7
APP_STATE_DETACHED_DISCONNECTED = 8
APP_STATE_DERIVED = 9
APP_STOP_SOURCE_UNSPECIFIED = 0
APP_STOP_SOURCE_CLI = 1
APP_STOP_SOURCE_PYTHON_CLIENT = 2
APP_STOP_SOURCE_WEB = 3
CERTIFICATE_STATUS_PENDING = 0
CERTIFICATE_STATUS_ISSUED = 1
CERTIFICATE_STATUS_FAILED = 2
CERTIFICATE_STATUS_REVOKED = 3
CHECKPOINT_STATUS_UNSPECIFIED = 0
CHECKPOINT_STATUS_PENDING = 1
CHECKPOINT_STATUS_PROCESSING = 2
CHECKPOINT_STATUS_READY = 3
CHECKPOINT_STATUS_FAILED = 4
CLIENT_TYPE_UNSPECIFIED = 0
CLIENT_TYPE_CLIENT = 1
CLIENT_TYPE_WORKER = 2
CLIENT_TYPE_CONTAINER = 3
CLIENT_TYPE_WEB_SERVER = 5
CLIENT_TYPE_NOTEBOOK_KERNEL = 6
CLIENT_TYPE_LIBMODAL = 7
CLOUD_PROVIDER_UNSPECIFIED = 0
CLOUD_PROVIDER_AWS = 1
CLOUD_PROVIDER_GCP = 2
CLOUD_PROVIDER_AUTO = 3
CLOUD_PROVIDER_OCI = 4
DNS_RECORD_TYPE_A = 0
DNS_RECORD_TYPE_TXT = 1
DNS_RECORD_TYPE_CNAME = 2
DATA_FORMAT_UNSPECIFIED = 0
DATA_FORMAT_PICKLE = 1
DATA_FORMAT_ASGI = 2
DATA_FORMAT_GENERATOR_DONE = 3
DEPLOYMENT_NAMESPACE_UNSPECIFIED = 0
DEPLOYMENT_NAMESPACE_WORKSPACE = 1
DEPLOYMENT_NAMESPACE_GLOBAL = 3
EXEC_OUTPUT_OPTION_UNSPECIFIED = 0
EXEC_OUTPUT_OPTION_DEVNULL = 1
EXEC_OUTPUT_OPTION_PIPE = 2
EXEC_OUTPUT_OPTION_STDOUT = 3
FILE_DESCRIPTOR_UNSPECIFIED = 0
FILE_DESCRIPTOR_STDOUT = 1
FILE_DESCRIPTOR_STDERR = 2
FILE_DESCRIPTOR_INFO = 3
FUNCTION_CALL_INVOCATION_TYPE_UNSPECIFIED = 0
FUNCTION_CALL_INVOCATION_TYPE_SYNC_LEGACY = 1
FUNCTION_CALL_INVOCATION_TYPE_ASYNC_LEGACY = 2
FUNCTION_CALL_INVOCATION_TYPE_ASYNC = 3
FUNCTION_CALL_INVOCATION_TYPE_SYNC = 4
FUNCTION_CALL_TYPE_UNSPECIFIED = 0
FUNCTION_CALL_TYPE_UNARY = 1
FUNCTION_CALL_TYPE_MAP = 2
GPU_TYPE_UNSPECIFIED = 0
GPU_TYPE_T4 = 1
GPU_TYPE_A100 = 2
GPU_TYPE_A10G = 3
GPU_TYPE_ANY = 4
GPU_TYPE_A100_80GB = 8
GPU_TYPE_L4 = 9
GPU_TYPE_H100 = 10
GPU_TYPE_L40S = 11
GPU_TYPE_H200 = 12
OBJECT_CREATION_TYPE_UNSPECIFIED = 0
OBJECT_CREATION_TYPE_CREATE_IF_MISSING = 1
OBJECT_CREATION_TYPE_CREATE_FAIL_IF_EXISTS = 2
OBJECT_CREATION_TYPE_CREATE_OVERWRITE_IF_EXISTS = 3
OBJECT_CREATION_TYPE_ANONYMOUS_OWNED_BY_APP = 4
OBJECT_CREATION_TYPE_EPHEMERAL = 5
PARAM_TYPE_UNSPECIFIED = 0
PARAM_TYPE_STRING = 1
PARAM_TYPE_INT = 2
PARAM_TYPE_PICKLE = 3
PARAM_TYPE_BYTES = 4
PARAM_TYPE_UNKNOWN = 5
PARAM_TYPE_LIST = 6
PARAM_TYPE_DICT = 7
PARAM_TYPE_NONE = 8
PARAM_TYPE_BOOL = 9
IMAGE_SNAPSHOT_UPLOAD = 0
FUNCTION_QUEUED = 1
PROXY_IP_STATUS_UNSPECIFIED = 0
PROXY_IP_STATUS_CREATING = 1
PROXY_IP_STATUS_ONLINE = 2
PROXY_IP_STATUS_TERMINATED = 3
PROXY_IP_STATUS_UNHEALTHY = 4
PROXY_TYPE_UNSPECIFIED = 0
PROXY_TYPE_LEGACY = 1
PROXY_TYPE_VPROX = 2
RATE_LIMIT_INTERVAL_UNSPECIFIED = 0
RATE_LIMIT_INTERVAL_SECOND = 1
RATE_LIMIT_INTERVAL_MINUTE = 2
REGISTRY_AUTH_TYPE_UNSPECIFIED = 0
REGISTRY_AUTH_TYPE_AWS = 1
REGISTRY_AUTH_TYPE_GCP = 2
REGISTRY_AUTH_TYPE_PUBLIC = 3
REGISTRY_AUTH_TYPE_STATIC_CREDS = 4
SEEK_SET = 0
SEEK_CUR = 1
SEEK_END = 2
SYSTEM_ERROR_CODE_UNSPECIFIED = 0
SYSTEM_ERROR_CODE_PERM = 1
SYSTEM_ERROR_CODE_NOENT = 2
SYSTEM_ERROR_CODE_IO = 5
SYSTEM_ERROR_CODE_NXIO = 6
SYSTEM_ERROR_CODE_NOMEM = 12
SYSTEM_ERROR_CODE_ACCES = 13
SYSTEM_ERROR_CODE_EXIST = 17
SYSTEM_ERROR_CODE_NOTDIR = 20
SYSTEM_ERROR_CODE_ISDIR = 21
SYSTEM_ERROR_CODE_INVAL = 22
SYSTEM_ERROR_CODE_MFILE = 24
SYSTEM_ERROR_CODE_FBIG = 27
SYSTEM_ERROR_CODE_NOSPC = 28
TASK_STATE_UNSPECIFIED = 0
TASK_STATE_CREATED = 6
TASK_STATE_QUEUED = 1
TASK_STATE_WORKER_ASSIGNED = 2
TASK_STATE_LOADING_IMAGE = 3
TASK_STATE_ACTIVE = 4
TASK_STATE_COMPLETED = 5
TASK_STATE_CREATING_CONTAINER = 7
TASK_STATE_IDLE = 8
TASK_STATE_PREEMPTIBLE = 9
TASK_STATE_PREEMPTED = 10
TASK_STATE_LOADING_CHECKPOINT_IMAGE = 11
TUNNEL_TYPE_UNSPECIFIED = 0
TUNNEL_TYPE_H2 = 1
VOLUME_FS_VERSION_UNSPECIFIED = 0
VOLUME_FS_VERSION_V1 = 1
VOLUME_FS_VERSION_V2 = 2
WEBHOOK_ASYNC_MODE_UNSPECIFIED = 0
WEBHOOK_ASYNC_MODE_DISABLED = 2
WEBHOOK_ASYNC_MODE_TRIGGER = 3
WEBHOOK_ASYNC_MODE_AUTO = 4
WEBHOOK_TYPE_UNSPECIFIED = 0
WEBHOOK_TYPE_ASGI_APP = 1
WEBHOOK_TYPE_FUNCTION = 2
WEBHOOK_TYPE_WSGI_APP = 3
WEBHOOK_TYPE_WEB_SERVER = 4


_APPCLIENTDISCONNECTREQUEST = DESCRIPTOR.message_types_by_name['AppClientDisconnectRequest']
_APPCREATEREQUEST = DESCRIPTOR.message_types_by_name['AppCreateRequest']
_APPCREATERESPONSE = DESCRIPTOR.message_types_by_name['AppCreateResponse']
_APPDEPLOYREQUEST = DESCRIPTOR.message_types_by_name['AppDeployRequest']
_APPDEPLOYRESPONSE = DESCRIPTOR.message_types_by_name['AppDeployResponse']
_APPDEPLOYMENTHISTORY = DESCRIPTOR.message_types_by_name['AppDeploymentHistory']
_APPDEPLOYMENTHISTORYREQUEST = DESCRIPTOR.message_types_by_name['AppDeploymentHistoryRequest']
_APPDEPLOYMENTHISTORYRESPONSE = DESCRIPTOR.message_types_by_name['AppDeploymentHistoryResponse']
_APPGETBYDEPLOYMENTNAMEREQUEST = DESCRIPTOR.message_types_by_name['AppGetByDeploymentNameRequest']
_APPGETBYDEPLOYMENTNAMERESPONSE = DESCRIPTOR.message_types_by_name['AppGetByDeploymentNameResponse']
_APPGETLAYOUTREQUEST = DESCRIPTOR.message_types_by_name['AppGetLayoutRequest']
_APPGETLAYOUTRESPONSE = DESCRIPTOR.message_types_by_name['AppGetLayoutResponse']
_APPGETLOGSREQUEST = DESCRIPTOR.message_types_by_name['AppGetLogsRequest']
_APPGETOBJECTSITEM = DESCRIPTOR.message_types_by_name['AppGetObjectsItem']
_APPGETOBJECTSREQUEST = DESCRIPTOR.message_types_by_name['AppGetObjectsRequest']
_APPGETOBJECTSRESPONSE = DESCRIPTOR.message_types_by_name['AppGetObjectsResponse']
_APPGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['AppGetOrCreateRequest']
_APPGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['AppGetOrCreateResponse']
_APPHEARTBEATREQUEST = DESCRIPTOR.message_types_by_name['AppHeartbeatRequest']
_APPLAYOUT = DESCRIPTOR.message_types_by_name['AppLayout']
_APPLAYOUT_FUNCTIONIDSENTRY = _APPLAYOUT.nested_types_by_name['FunctionIdsEntry']
_APPLAYOUT_CLASSIDSENTRY = _APPLAYOUT.nested_types_by_name['ClassIdsEntry']
_APPLISTREQUEST = DESCRIPTOR.message_types_by_name['AppListRequest']
_APPLISTRESPONSE = DESCRIPTOR.message_types_by_name['AppListResponse']
_APPLISTRESPONSE_APPLISTITEM = _APPLISTRESPONSE.nested_types_by_name['AppListItem']
_APPLOOKUPREQUEST = DESCRIPTOR.message_types_by_name['AppLookupRequest']
_APPLOOKUPRESPONSE = DESCRIPTOR.message_types_by_name['AppLookupResponse']
_APPPUBLISHREQUEST = DESCRIPTOR.message_types_by_name['AppPublishRequest']
_APPPUBLISHREQUEST_FUNCTIONIDSENTRY = _APPPUBLISHREQUEST.nested_types_by_name['FunctionIdsEntry']
_APPPUBLISHREQUEST_CLASSIDSENTRY = _APPPUBLISHREQUEST.nested_types_by_name['ClassIdsEntry']
_APPPUBLISHREQUEST_DEFINITIONIDSENTRY = _APPPUBLISHREQUEST.nested_types_by_name['DefinitionIdsEntry']
_APPPUBLISHRESPONSE = DESCRIPTOR.message_types_by_name['AppPublishResponse']
_APPROLLBACKREQUEST = DESCRIPTOR.message_types_by_name['AppRollbackRequest']
_APPSETOBJECTSREQUEST = DESCRIPTOR.message_types_by_name['AppSetObjectsRequest']
_APPSETOBJECTSREQUEST_INDEXEDOBJECTIDSENTRY = _APPSETOBJECTSREQUEST.nested_types_by_name['IndexedObjectIdsEntry']
_APPSTOPREQUEST = DESCRIPTOR.message_types_by_name['AppStopRequest']
_ASGI = DESCRIPTOR.message_types_by_name['Asgi']
_ASGI_HTTP = _ASGI.nested_types_by_name['Http']
_ASGI_HTTPREQUEST = _ASGI.nested_types_by_name['HttpRequest']
_ASGI_HTTPRESPONSESTART = _ASGI.nested_types_by_name['HttpResponseStart']
_ASGI_HTTPRESPONSEBODY = _ASGI.nested_types_by_name['HttpResponseBody']
_ASGI_HTTPRESPONSETRAILERS = _ASGI.nested_types_by_name['HttpResponseTrailers']
_ASGI_HTTPDISCONNECT = _ASGI.nested_types_by_name['HttpDisconnect']
_ASGI_WEBSOCKET = _ASGI.nested_types_by_name['Websocket']
_ASGI_WEBSOCKETCONNECT = _ASGI.nested_types_by_name['WebsocketConnect']
_ASGI_WEBSOCKETACCEPT = _ASGI.nested_types_by_name['WebsocketAccept']
_ASGI_WEBSOCKETRECEIVE = _ASGI.nested_types_by_name['WebsocketReceive']
_ASGI_WEBSOCKETSEND = _ASGI.nested_types_by_name['WebsocketSend']
_ASGI_WEBSOCKETDISCONNECT = _ASGI.nested_types_by_name['WebsocketDisconnect']
_ASGI_WEBSOCKETCLOSE = _ASGI.nested_types_by_name['WebsocketClose']
_ATTEMPTAWAITREQUEST = DESCRIPTOR.message_types_by_name['AttemptAwaitRequest']
_ATTEMPTAWAITRESPONSE = DESCRIPTOR.message_types_by_name['AttemptAwaitResponse']
_ATTEMPTRETRYREQUEST = DESCRIPTOR.message_types_by_name['AttemptRetryRequest']
_ATTEMPTRETRYRESPONSE = DESCRIPTOR.message_types_by_name['AttemptRetryResponse']
_ATTEMPTSTARTREQUEST = DESCRIPTOR.message_types_by_name['AttemptStartRequest']
_ATTEMPTSTARTRESPONSE = DESCRIPTOR.message_types_by_name['AttemptStartResponse']
_AUTOSCALERSETTINGS = DESCRIPTOR.message_types_by_name['AutoscalerSettings']
_BASEIMAGE = DESCRIPTOR.message_types_by_name['BaseImage']
_BLOBCREATEREQUEST = DESCRIPTOR.message_types_by_name['BlobCreateRequest']
_BLOBCREATERESPONSE = DESCRIPTOR.message_types_by_name['BlobCreateResponse']
_BLOBGETREQUEST = DESCRIPTOR.message_types_by_name['BlobGetRequest']
_BLOBGETRESPONSE = DESCRIPTOR.message_types_by_name['BlobGetResponse']
_BUILDFUNCTION = DESCRIPTOR.message_types_by_name['BuildFunction']
_CANCELINPUTEVENT = DESCRIPTOR.message_types_by_name['CancelInputEvent']
_CHECKPOINTINFO = DESCRIPTOR.message_types_by_name['CheckpointInfo']
_CLASSCREATEREQUEST = DESCRIPTOR.message_types_by_name['ClassCreateRequest']
_CLASSCREATERESPONSE = DESCRIPTOR.message_types_by_name['ClassCreateResponse']
_CLASSGETREQUEST = DESCRIPTOR.message_types_by_name['ClassGetRequest']
_CLASSGETRESPONSE = DESCRIPTOR.message_types_by_name['ClassGetResponse']
_CLASSHANDLEMETADATA = DESCRIPTOR.message_types_by_name['ClassHandleMetadata']
_CLASSMETHOD = DESCRIPTOR.message_types_by_name['ClassMethod']
_CLASSPARAMETERINFO = DESCRIPTOR.message_types_by_name['ClassParameterInfo']
_CLASSPARAMETERSET = DESCRIPTOR.message_types_by_name['ClassParameterSet']
_CLASSPARAMETERSPEC = DESCRIPTOR.message_types_by_name['ClassParameterSpec']
_CLASSPARAMETERVALUE = DESCRIPTOR.message_types_by_name['ClassParameterValue']
_CLIENTHELLORESPONSE = DESCRIPTOR.message_types_by_name['ClientHelloResponse']
_CLOUDBUCKETMOUNT = DESCRIPTOR.message_types_by_name['CloudBucketMount']
_CLUSTERGETREQUEST = DESCRIPTOR.message_types_by_name['ClusterGetRequest']
_CLUSTERGETRESPONSE = DESCRIPTOR.message_types_by_name['ClusterGetResponse']
_CLUSTERLISTREQUEST = DESCRIPTOR.message_types_by_name['ClusterListRequest']
_CLUSTERLISTRESPONSE = DESCRIPTOR.message_types_by_name['ClusterListResponse']
_CLUSTERSTATS = DESCRIPTOR.message_types_by_name['ClusterStats']
_COMMITINFO = DESCRIPTOR.message_types_by_name['CommitInfo']
_CONTAINERARGUMENTS = DESCRIPTOR.message_types_by_name['ContainerArguments']
_CONTAINERARGUMENTS_TRACINGCONTEXTENTRY = _CONTAINERARGUMENTS.nested_types_by_name['TracingContextEntry']
_CONTAINERCHECKPOINTREQUEST = DESCRIPTOR.message_types_by_name['ContainerCheckpointRequest']
_CONTAINEREXECGETOUTPUTREQUEST = DESCRIPTOR.message_types_by_name['ContainerExecGetOutputRequest']
_CONTAINEREXECPUTINPUTREQUEST = DESCRIPTOR.message_types_by_name['ContainerExecPutInputRequest']
_CONTAINEREXECREQUEST = DESCRIPTOR.message_types_by_name['ContainerExecRequest']
_CONTAINEREXECRESPONSE = DESCRIPTOR.message_types_by_name['ContainerExecResponse']
_CONTAINEREXECWAITREQUEST = DESCRIPTOR.message_types_by_name['ContainerExecWaitRequest']
_CONTAINEREXECWAITRESPONSE = DESCRIPTOR.message_types_by_name['ContainerExecWaitResponse']
_CONTAINERFILECLOSEREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileCloseRequest']
_CONTAINERFILEDELETEBYTESREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileDeleteBytesRequest']
_CONTAINERFILEFLUSHREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileFlushRequest']
_CONTAINERFILELSREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileLsRequest']
_CONTAINERFILEMKDIRREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileMkdirRequest']
_CONTAINERFILEOPENREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileOpenRequest']
_CONTAINERFILEREADLINEREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileReadLineRequest']
_CONTAINERFILEREADREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileReadRequest']
_CONTAINERFILERMREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileRmRequest']
_CONTAINERFILESEEKREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileSeekRequest']
_CONTAINERFILEWATCHREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileWatchRequest']
_CONTAINERFILEWRITEREPLACEBYTESREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileWriteReplaceBytesRequest']
_CONTAINERFILEWRITEREQUEST = DESCRIPTOR.message_types_by_name['ContainerFileWriteRequest']
_CONTAINERFILESYSTEMEXECGETOUTPUTREQUEST = DESCRIPTOR.message_types_by_name['ContainerFilesystemExecGetOutputRequest']
_CONTAINERFILESYSTEMEXECREQUEST = DESCRIPTOR.message_types_by_name['ContainerFilesystemExecRequest']
_CONTAINERFILESYSTEMEXECRESPONSE = DESCRIPTOR.message_types_by_name['ContainerFilesystemExecResponse']
_CONTAINERHEARTBEATREQUEST = DESCRIPTOR.message_types_by_name['ContainerHeartbeatRequest']
_CONTAINERHEARTBEATRESPONSE = DESCRIPTOR.message_types_by_name['ContainerHeartbeatResponse']
_CONTAINERLOGREQUEST = DESCRIPTOR.message_types_by_name['ContainerLogRequest']
_CONTAINERSTOPREQUEST = DESCRIPTOR.message_types_by_name['ContainerStopRequest']
_CONTAINERSTOPRESPONSE = DESCRIPTOR.message_types_by_name['ContainerStopResponse']
_CUSTOMDOMAINCONFIG = DESCRIPTOR.message_types_by_name['CustomDomainConfig']
_CUSTOMDOMAININFO = DESCRIPTOR.message_types_by_name['CustomDomainInfo']
_DNSRECORD = DESCRIPTOR.message_types_by_name['DNSRecord']
_DATACHUNK = DESCRIPTOR.message_types_by_name['DataChunk']
_DICTCLEARREQUEST = DESCRIPTOR.message_types_by_name['DictClearRequest']
_DICTCONTAINSREQUEST = DESCRIPTOR.message_types_by_name['DictContainsRequest']
_DICTCONTAINSRESPONSE = DESCRIPTOR.message_types_by_name['DictContainsResponse']
_DICTCONTENTSREQUEST = DESCRIPTOR.message_types_by_name['DictContentsRequest']
_DICTDELETEREQUEST = DESCRIPTOR.message_types_by_name['DictDeleteRequest']
_DICTENTRY = DESCRIPTOR.message_types_by_name['DictEntry']
_DICTGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['DictGetOrCreateRequest']
_DICTGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['DictGetOrCreateResponse']
_DICTGETREQUEST = DESCRIPTOR.message_types_by_name['DictGetRequest']
_DICTGETRESPONSE = DESCRIPTOR.message_types_by_name['DictGetResponse']
_DICTHEARTBEATREQUEST = DESCRIPTOR.message_types_by_name['DictHeartbeatRequest']
_DICTLENREQUEST = DESCRIPTOR.message_types_by_name['DictLenRequest']
_DICTLENRESPONSE = DESCRIPTOR.message_types_by_name['DictLenResponse']
_DICTLISTREQUEST = DESCRIPTOR.message_types_by_name['DictListRequest']
_DICTLISTRESPONSE = DESCRIPTOR.message_types_by_name['DictListResponse']
_DICTLISTRESPONSE_DICTINFO = _DICTLISTRESPONSE.nested_types_by_name['DictInfo']
_DICTPOPREQUEST = DESCRIPTOR.message_types_by_name['DictPopRequest']
_DICTPOPRESPONSE = DESCRIPTOR.message_types_by_name['DictPopResponse']
_DICTUPDATEREQUEST = DESCRIPTOR.message_types_by_name['DictUpdateRequest']
_DICTUPDATERESPONSE = DESCRIPTOR.message_types_by_name['DictUpdateResponse']
_DOMAIN = DESCRIPTOR.message_types_by_name['Domain']
_DOMAINCERTIFICATEVERIFYREQUEST = DESCRIPTOR.message_types_by_name['DomainCertificateVerifyRequest']
_DOMAINCERTIFICATEVERIFYRESPONSE = DESCRIPTOR.message_types_by_name['DomainCertificateVerifyResponse']
_DOMAINCREATEREQUEST = DESCRIPTOR.message_types_by_name['DomainCreateRequest']
_DOMAINCREATERESPONSE = DESCRIPTOR.message_types_by_name['DomainCreateResponse']
_DOMAINLISTREQUEST = DESCRIPTOR.message_types_by_name['DomainListRequest']
_DOMAINLISTRESPONSE = DESCRIPTOR.message_types_by_name['DomainListResponse']
_ENVIRONMENTCREATEREQUEST = DESCRIPTOR.message_types_by_name['EnvironmentCreateRequest']
_ENVIRONMENTDELETEREQUEST = DESCRIPTOR.message_types_by_name['EnvironmentDeleteRequest']
_ENVIRONMENTGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['EnvironmentGetOrCreateRequest']
_ENVIRONMENTGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['EnvironmentGetOrCreateResponse']
_ENVIRONMENTLISTITEM = DESCRIPTOR.message_types_by_name['EnvironmentListItem']
_ENVIRONMENTLISTRESPONSE = DESCRIPTOR.message_types_by_name['EnvironmentListResponse']
_ENVIRONMENTMETADATA = DESCRIPTOR.message_types_by_name['EnvironmentMetadata']
_ENVIRONMENTSETTINGS = DESCRIPTOR.message_types_by_name['EnvironmentSettings']
_ENVIRONMENTUPDATEREQUEST = DESCRIPTOR.message_types_by_name['EnvironmentUpdateRequest']
_FILEENTRY = DESCRIPTOR.message_types_by_name['FileEntry']
_FILESYSTEMRUNTIMEOUTPUTBATCH = DESCRIPTOR.message_types_by_name['FilesystemRuntimeOutputBatch']
_FUNCTION = DESCRIPTOR.message_types_by_name['Function']
_FUNCTION_METHODDEFINITIONSENTRY = _FUNCTION.nested_types_by_name['MethodDefinitionsEntry']
_FUNCTION_EXPERIMENTALOPTIONSENTRY = _FUNCTION.nested_types_by_name['ExperimentalOptionsEntry']
_FUNCTIONASYNCINVOKEREQUEST = DESCRIPTOR.message_types_by_name['FunctionAsyncInvokeRequest']
_FUNCTIONASYNCINVOKERESPONSE = DESCRIPTOR.message_types_by_name['FunctionAsyncInvokeResponse']
_FUNCTIONBINDPARAMSREQUEST = DESCRIPTOR.message_types_by_name['FunctionBindParamsRequest']
_FUNCTIONBINDPARAMSRESPONSE = DESCRIPTOR.message_types_by_name['FunctionBindParamsResponse']
_FUNCTIONCALLCALLGRAPHINFO = DESCRIPTOR.message_types_by_name['FunctionCallCallGraphInfo']
_FUNCTIONCALLCANCELREQUEST = DESCRIPTOR.message_types_by_name['FunctionCallCancelRequest']
_FUNCTIONCALLGETDATAREQUEST = DESCRIPTOR.message_types_by_name['FunctionCallGetDataRequest']
_FUNCTIONCALLINFO = DESCRIPTOR.message_types_by_name['FunctionCallInfo']
_FUNCTIONCALLLISTREQUEST = DESCRIPTOR.message_types_by_name['FunctionCallListRequest']
_FUNCTIONCALLLISTRESPONSE = DESCRIPTOR.message_types_by_name['FunctionCallListResponse']
_FUNCTIONCALLPUTDATAREQUEST = DESCRIPTOR.message_types_by_name['FunctionCallPutDataRequest']
_FUNCTIONCREATEREQUEST = DESCRIPTOR.message_types_by_name['FunctionCreateRequest']
_FUNCTIONCREATERESPONSE = DESCRIPTOR.message_types_by_name['FunctionCreateResponse']
_FUNCTIONDATA = DESCRIPTOR.message_types_by_name['FunctionData']
_FUNCTIONDATA_METHODDEFINITIONSENTRY = _FUNCTIONDATA.nested_types_by_name['MethodDefinitionsEntry']
_FUNCTIONDATA_RANKEDFUNCTION = _FUNCTIONDATA.nested_types_by_name['RankedFunction']
_FUNCTIONDATA_EXPERIMENTALOPTIONSENTRY = _FUNCTIONDATA.nested_types_by_name['ExperimentalOptionsEntry']
_FUNCTIONEXTENDED = DESCRIPTOR.message_types_by_name['FunctionExtended']
_FUNCTIONGETCALLGRAPHREQUEST = DESCRIPTOR.message_types_by_name['FunctionGetCallGraphRequest']
_FUNCTIONGETCALLGRAPHRESPONSE = DESCRIPTOR.message_types_by_name['FunctionGetCallGraphResponse']
_FUNCTIONGETCURRENTSTATSREQUEST = DESCRIPTOR.message_types_by_name['FunctionGetCurrentStatsRequest']
_FUNCTIONGETDYNAMICCONCURRENCYREQUEST = DESCRIPTOR.message_types_by_name['FunctionGetDynamicConcurrencyRequest']
_FUNCTIONGETDYNAMICCONCURRENCYRESPONSE = DESCRIPTOR.message_types_by_name['FunctionGetDynamicConcurrencyResponse']
_FUNCTIONGETINPUTSITEM = DESCRIPTOR.message_types_by_name['FunctionGetInputsItem']
_FUNCTIONGETINPUTSREQUEST = DESCRIPTOR.message_types_by_name['FunctionGetInputsRequest']
_FUNCTIONGETINPUTSRESPONSE = DESCRIPTOR.message_types_by_name['FunctionGetInputsResponse']
_FUNCTIONGETOUTPUTSITEM = DESCRIPTOR.message_types_by_name['FunctionGetOutputsItem']
_FUNCTIONGETOUTPUTSREQUEST = DESCRIPTOR.message_types_by_name['FunctionGetOutputsRequest']
_FUNCTIONGETOUTPUTSRESPONSE = DESCRIPTOR.message_types_by_name['FunctionGetOutputsResponse']
_FUNCTIONGETREQUEST = DESCRIPTOR.message_types_by_name['FunctionGetRequest']
_FUNCTIONGETRESPONSE = DESCRIPTOR.message_types_by_name['FunctionGetResponse']
_FUNCTIONGETSERIALIZEDREQUEST = DESCRIPTOR.message_types_by_name['FunctionGetSerializedRequest']
_FUNCTIONGETSERIALIZEDRESPONSE = DESCRIPTOR.message_types_by_name['FunctionGetSerializedResponse']
_FUNCTIONHANDLEMETADATA = DESCRIPTOR.message_types_by_name['FunctionHandleMetadata']
_FUNCTIONHANDLEMETADATA_METHODHANDLEMETADATAENTRY = _FUNCTIONHANDLEMETADATA.nested_types_by_name['MethodHandleMetadataEntry']
_FUNCTIONINPUT = DESCRIPTOR.message_types_by_name['FunctionInput']
_FUNCTIONMAPREQUEST = DESCRIPTOR.message_types_by_name['FunctionMapRequest']
_FUNCTIONMAPRESPONSE = DESCRIPTOR.message_types_by_name['FunctionMapResponse']
_FUNCTIONOPTIONS = DESCRIPTOR.message_types_by_name['FunctionOptions']
_FUNCTIONPRECREATEREQUEST = DESCRIPTOR.message_types_by_name['FunctionPrecreateRequest']
_FUNCTIONPRECREATEREQUEST_METHODDEFINITIONSENTRY = _FUNCTIONPRECREATEREQUEST.nested_types_by_name['MethodDefinitionsEntry']
_FUNCTIONPRECREATERESPONSE = DESCRIPTOR.message_types_by_name['FunctionPrecreateResponse']
_FUNCTIONPUTINPUTSITEM = DESCRIPTOR.message_types_by_name['FunctionPutInputsItem']
_FUNCTIONPUTINPUTSREQUEST = DESCRIPTOR.message_types_by_name['FunctionPutInputsRequest']
_FUNCTIONPUTINPUTSRESPONSE = DESCRIPTOR.message_types_by_name['FunctionPutInputsResponse']
_FUNCTIONPUTINPUTSRESPONSEITEM = DESCRIPTOR.message_types_by_name['FunctionPutInputsResponseItem']
_FUNCTIONPUTOUTPUTSITEM = DESCRIPTOR.message_types_by_name['FunctionPutOutputsItem']
_FUNCTIONPUTOUTPUTSREQUEST = DESCRIPTOR.message_types_by_name['FunctionPutOutputsRequest']
_FUNCTIONRETRYINPUTSITEM = DESCRIPTOR.message_types_by_name['FunctionRetryInputsItem']
_FUNCTIONRETRYINPUTSREQUEST = DESCRIPTOR.message_types_by_name['FunctionRetryInputsRequest']
_FUNCTIONRETRYINPUTSRESPONSE = DESCRIPTOR.message_types_by_name['FunctionRetryInputsResponse']
_FUNCTIONRETRYPOLICY = DESCRIPTOR.message_types_by_name['FunctionRetryPolicy']
_FUNCTIONSCHEMA = DESCRIPTOR.message_types_by_name['FunctionSchema']
_FUNCTIONSTATS = DESCRIPTOR.message_types_by_name['FunctionStats']
_FUNCTIONUPDATESCHEDULINGPARAMSREQUEST = DESCRIPTOR.message_types_by_name['FunctionUpdateSchedulingParamsRequest']
_FUNCTIONUPDATESCHEDULINGPARAMSRESPONSE = DESCRIPTOR.message_types_by_name['FunctionUpdateSchedulingParamsResponse']
_GPUCONFIG = DESCRIPTOR.message_types_by_name['GPUConfig']
_GENERATORDONE = DESCRIPTOR.message_types_by_name['GeneratorDone']
_GENERICPAYLOADTYPE = DESCRIPTOR.message_types_by_name['GenericPayloadType']
_GENERICRESULT = DESCRIPTOR.message_types_by_name['GenericResult']
_IMAGE = DESCRIPTOR.message_types_by_name['Image']
_IMAGECONTEXTFILE = DESCRIPTOR.message_types_by_name['ImageContextFile']
_IMAGEFROMIDREQUEST = DESCRIPTOR.message_types_by_name['ImageFromIdRequest']
_IMAGEFROMIDRESPONSE = DESCRIPTOR.message_types_by_name['ImageFromIdResponse']
_IMAGEGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['ImageGetOrCreateRequest']
_IMAGEGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['ImageGetOrCreateResponse']
_IMAGEJOINSTREAMINGREQUEST = DESCRIPTOR.message_types_by_name['ImageJoinStreamingRequest']
_IMAGEJOINSTREAMINGRESPONSE = DESCRIPTOR.message_types_by_name['ImageJoinStreamingResponse']
_IMAGEMETADATA = DESCRIPTOR.message_types_by_name['ImageMetadata']
_IMAGEMETADATA_PYTHONPACKAGESENTRY = _IMAGEMETADATA.nested_types_by_name['PythonPackagesEntry']
_IMAGEREGISTRYCONFIG = DESCRIPTOR.message_types_by_name['ImageRegistryConfig']
_INPUTCALLGRAPHINFO = DESCRIPTOR.message_types_by_name['InputCallGraphInfo']
_INPUTCATEGORYINFO = DESCRIPTOR.message_types_by_name['InputCategoryInfo']
_INPUTINFO = DESCRIPTOR.message_types_by_name['InputInfo']
_METHODDEFINITION = DESCRIPTOR.message_types_by_name['MethodDefinition']
_MOUNTFILE = DESCRIPTOR.message_types_by_name['MountFile']
_MOUNTGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['MountGetOrCreateRequest']
_MOUNTGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['MountGetOrCreateResponse']
_MOUNTHANDLEMETADATA = DESCRIPTOR.message_types_by_name['MountHandleMetadata']
_MOUNTPUTFILEREQUEST = DESCRIPTOR.message_types_by_name['MountPutFileRequest']
_MOUNTPUTFILERESPONSE = DESCRIPTOR.message_types_by_name['MountPutFileResponse']
_MULTIPARTUPLOAD = DESCRIPTOR.message_types_by_name['MultiPartUpload']
_NETWORKACCESS = DESCRIPTOR.message_types_by_name['NetworkAccess']
_NOTEBOOKKERNELPUBLISHRESULTSREQUEST = DESCRIPTOR.message_types_by_name['NotebookKernelPublishResultsRequest']
_NOTEBOOKKERNELPUBLISHRESULTSREQUEST_EXECUTEREPLY = _NOTEBOOKKERNELPUBLISHRESULTSREQUEST.nested_types_by_name['ExecuteReply']
_NOTEBOOKKERNELPUBLISHRESULTSREQUEST_CELLRESULT = _NOTEBOOKKERNELPUBLISHRESULTSREQUEST.nested_types_by_name['CellResult']
_NOTEBOOKOUTPUT = DESCRIPTOR.message_types_by_name['NotebookOutput']
_NOTEBOOKOUTPUT_EXECUTERESULT = _NOTEBOOKOUTPUT.nested_types_by_name['ExecuteResult']
_NOTEBOOKOUTPUT_DISPLAYDATA = _NOTEBOOKOUTPUT.nested_types_by_name['DisplayData']
_NOTEBOOKOUTPUT_STREAM = _NOTEBOOKOUTPUT.nested_types_by_name['Stream']
_NOTEBOOKOUTPUT_ERROR = _NOTEBOOKOUTPUT.nested_types_by_name['Error']
_OBJECT = DESCRIPTOR.message_types_by_name['Object']
_OBJECTDEPENDENCY = DESCRIPTOR.message_types_by_name['ObjectDependency']
_PTYINFO = DESCRIPTOR.message_types_by_name['PTYInfo']
_PORTSPEC = DESCRIPTOR.message_types_by_name['PortSpec']
_PORTSPECS = DESCRIPTOR.message_types_by_name['PortSpecs']
_PROXY = DESCRIPTOR.message_types_by_name['Proxy']
_PROXYADDIPREQUEST = DESCRIPTOR.message_types_by_name['ProxyAddIpRequest']
_PROXYADDIPRESPONSE = DESCRIPTOR.message_types_by_name['ProxyAddIpResponse']
_PROXYCREATEREQUEST = DESCRIPTOR.message_types_by_name['ProxyCreateRequest']
_PROXYCREATERESPONSE = DESCRIPTOR.message_types_by_name['ProxyCreateResponse']
_PROXYDELETEREQUEST = DESCRIPTOR.message_types_by_name['ProxyDeleteRequest']
_PROXYGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['ProxyGetOrCreateRequest']
_PROXYGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['ProxyGetOrCreateResponse']
_PROXYGETREQUEST = DESCRIPTOR.message_types_by_name['ProxyGetRequest']
_PROXYGETRESPONSE = DESCRIPTOR.message_types_by_name['ProxyGetResponse']
_PROXYINFO = DESCRIPTOR.message_types_by_name['ProxyInfo']
_PROXYIP = DESCRIPTOR.message_types_by_name['ProxyIp']
_PROXYLISTRESPONSE = DESCRIPTOR.message_types_by_name['ProxyListResponse']
_PROXYREMOVEIPREQUEST = DESCRIPTOR.message_types_by_name['ProxyRemoveIpRequest']
_QUEUECLEARREQUEST = DESCRIPTOR.message_types_by_name['QueueClearRequest']
_QUEUEDELETEREQUEST = DESCRIPTOR.message_types_by_name['QueueDeleteRequest']
_QUEUEGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['QueueGetOrCreateRequest']
_QUEUEGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['QueueGetOrCreateResponse']
_QUEUEGETREQUEST = DESCRIPTOR.message_types_by_name['QueueGetRequest']
_QUEUEGETRESPONSE = DESCRIPTOR.message_types_by_name['QueueGetResponse']
_QUEUEHEARTBEATREQUEST = DESCRIPTOR.message_types_by_name['QueueHeartbeatRequest']
_QUEUEITEM = DESCRIPTOR.message_types_by_name['QueueItem']
_QUEUELENREQUEST = DESCRIPTOR.message_types_by_name['QueueLenRequest']
_QUEUELENRESPONSE = DESCRIPTOR.message_types_by_name['QueueLenResponse']
_QUEUELISTREQUEST = DESCRIPTOR.message_types_by_name['QueueListRequest']
_QUEUELISTRESPONSE = DESCRIPTOR.message_types_by_name['QueueListResponse']
_QUEUELISTRESPONSE_QUEUEINFO = _QUEUELISTRESPONSE.nested_types_by_name['QueueInfo']
_QUEUENEXTITEMSREQUEST = DESCRIPTOR.message_types_by_name['QueueNextItemsRequest']
_QUEUENEXTITEMSRESPONSE = DESCRIPTOR.message_types_by_name['QueueNextItemsResponse']
_QUEUEPUTREQUEST = DESCRIPTOR.message_types_by_name['QueuePutRequest']
_RATELIMIT = DESCRIPTOR.message_types_by_name['RateLimit']
_RESOURCES = DESCRIPTOR.message_types_by_name['Resources']
_RUNTIMEINPUTMESSAGE = DESCRIPTOR.message_types_by_name['RuntimeInputMessage']
_RUNTIMEOUTPUTBATCH = DESCRIPTOR.message_types_by_name['RuntimeOutputBatch']
_RUNTIMEOUTPUTMESSAGE = DESCRIPTOR.message_types_by_name['RuntimeOutputMessage']
_S3MOUNT = DESCRIPTOR.message_types_by_name['S3Mount']
_SANDBOX = DESCRIPTOR.message_types_by_name['Sandbox']
_SANDBOXCREATEREQUEST = DESCRIPTOR.message_types_by_name['SandboxCreateRequest']
_SANDBOXCREATERESPONSE = DESCRIPTOR.message_types_by_name['SandboxCreateResponse']
_SANDBOXGETLOGSREQUEST = DESCRIPTOR.message_types_by_name['SandboxGetLogsRequest']
_SANDBOXGETRESOURCEUSAGEREQUEST = DESCRIPTOR.message_types_by_name['SandboxGetResourceUsageRequest']
_SANDBOXGETRESOURCEUSAGERESPONSE = DESCRIPTOR.message_types_by_name['SandboxGetResourceUsageResponse']
_SANDBOXGETTASKIDREQUEST = DESCRIPTOR.message_types_by_name['SandboxGetTaskIdRequest']
_SANDBOXGETTASKIDRESPONSE = DESCRIPTOR.message_types_by_name['SandboxGetTaskIdResponse']
_SANDBOXGETTUNNELSREQUEST = DESCRIPTOR.message_types_by_name['SandboxGetTunnelsRequest']
_SANDBOXGETTUNNELSRESPONSE = DESCRIPTOR.message_types_by_name['SandboxGetTunnelsResponse']
_SANDBOXHANDLEMETADATA = DESCRIPTOR.message_types_by_name['SandboxHandleMetadata']
_SANDBOXINFO = DESCRIPTOR.message_types_by_name['SandboxInfo']
_SANDBOXLISTREQUEST = DESCRIPTOR.message_types_by_name['SandboxListRequest']
_SANDBOXLISTRESPONSE = DESCRIPTOR.message_types_by_name['SandboxListResponse']
_SANDBOXRESTOREREQUEST = DESCRIPTOR.message_types_by_name['SandboxRestoreRequest']
_SANDBOXRESTORERESPONSE = DESCRIPTOR.message_types_by_name['SandboxRestoreResponse']
_SANDBOXSNAPSHOTFSREQUEST = DESCRIPTOR.message_types_by_name['SandboxSnapshotFsRequest']
_SANDBOXSNAPSHOTFSRESPONSE = DESCRIPTOR.message_types_by_name['SandboxSnapshotFsResponse']
_SANDBOXSNAPSHOTGETREQUEST = DESCRIPTOR.message_types_by_name['SandboxSnapshotGetRequest']
_SANDBOXSNAPSHOTGETRESPONSE = DESCRIPTOR.message_types_by_name['SandboxSnapshotGetResponse']
_SANDBOXSNAPSHOTREQUEST = DESCRIPTOR.message_types_by_name['SandboxSnapshotRequest']
_SANDBOXSNAPSHOTRESPONSE = DESCRIPTOR.message_types_by_name['SandboxSnapshotResponse']
_SANDBOXSNAPSHOTWAITREQUEST = DESCRIPTOR.message_types_by_name['SandboxSnapshotWaitRequest']
_SANDBOXSNAPSHOTWAITRESPONSE = DESCRIPTOR.message_types_by_name['SandboxSnapshotWaitResponse']
_SANDBOXSTDINWRITEREQUEST = DESCRIPTOR.message_types_by_name['SandboxStdinWriteRequest']
_SANDBOXSTDINWRITERESPONSE = DESCRIPTOR.message_types_by_name['SandboxStdinWriteResponse']
_SANDBOXTAG = DESCRIPTOR.message_types_by_name['SandboxTag']
_SANDBOXTAGSSETREQUEST = DESCRIPTOR.message_types_by_name['SandboxTagsSetRequest']
_SANDBOXTERMINATEREQUEST = DESCRIPTOR.message_types_by_name['SandboxTerminateRequest']
_SANDBOXTERMINATERESPONSE = DESCRIPTOR.message_types_by_name['SandboxTerminateResponse']
_SANDBOXWAITREQUEST = DESCRIPTOR.message_types_by_name['SandboxWaitRequest']
_SANDBOXWAITRESPONSE = DESCRIPTOR.message_types_by_name['SandboxWaitResponse']
_SCHEDULE = DESCRIPTOR.message_types_by_name['Schedule']
_SCHEDULE_CRON = _SCHEDULE.nested_types_by_name['Cron']
_SCHEDULE_PERIOD = _SCHEDULE.nested_types_by_name['Period']
_SCHEDULERPLACEMENT = DESCRIPTOR.message_types_by_name['SchedulerPlacement']
_SECRETCREATEREQUEST = DESCRIPTOR.message_types_by_name['SecretCreateRequest']
_SECRETCREATEREQUEST_ENVDICTENTRY = _SECRETCREATEREQUEST.nested_types_by_name['EnvDictEntry']
_SECRETCREATERESPONSE = DESCRIPTOR.message_types_by_name['SecretCreateResponse']
_SECRETDELETEREQUEST = DESCRIPTOR.message_types_by_name['SecretDeleteRequest']
_SECRETGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['SecretGetOrCreateRequest']
_SECRETGETORCREATEREQUEST_ENVDICTENTRY = _SECRETGETORCREATEREQUEST.nested_types_by_name['EnvDictEntry']
_SECRETGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['SecretGetOrCreateResponse']
_SECRETLISTITEM = DESCRIPTOR.message_types_by_name['SecretListItem']
_SECRETLISTREQUEST = DESCRIPTOR.message_types_by_name['SecretListRequest']
_SECRETLISTRESPONSE = DESCRIPTOR.message_types_by_name['SecretListResponse']
_SHAREDVOLUMEDELETEREQUEST = DESCRIPTOR.message_types_by_name['SharedVolumeDeleteRequest']
_SHAREDVOLUMEGETFILEREQUEST = DESCRIPTOR.message_types_by_name['SharedVolumeGetFileRequest']
_SHAREDVOLUMEGETFILERESPONSE = DESCRIPTOR.message_types_by_name['SharedVolumeGetFileResponse']
_SHAREDVOLUMEGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['SharedVolumeGetOrCreateRequest']
_SHAREDVOLUMEGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['SharedVolumeGetOrCreateResponse']
_SHAREDVOLUMEHEARTBEATREQUEST = DESCRIPTOR.message_types_by_name['SharedVolumeHeartbeatRequest']
_SHAREDVOLUMELISTFILESREQUEST = DESCRIPTOR.message_types_by_name['SharedVolumeListFilesRequest']
_SHAREDVOLUMELISTFILESRESPONSE = DESCRIPTOR.message_types_by_name['SharedVolumeListFilesResponse']
_SHAREDVOLUMELISTITEM = DESCRIPTOR.message_types_by_name['SharedVolumeListItem']
_SHAREDVOLUMELISTREQUEST = DESCRIPTOR.message_types_by_name['SharedVolumeListRequest']
_SHAREDVOLUMELISTRESPONSE = DESCRIPTOR.message_types_by_name['SharedVolumeListResponse']
_SHAREDVOLUMEMOUNT = DESCRIPTOR.message_types_by_name['SharedVolumeMount']
_SHAREDVOLUMEPUTFILEREQUEST = DESCRIPTOR.message_types_by_name['SharedVolumePutFileRequest']
_SHAREDVOLUMEPUTFILERESPONSE = DESCRIPTOR.message_types_by_name['SharedVolumePutFileResponse']
_SHAREDVOLUMEREMOVEFILEREQUEST = DESCRIPTOR.message_types_by_name['SharedVolumeRemoveFileRequest']
_SYSTEMERRORMESSAGE = DESCRIPTOR.message_types_by_name['SystemErrorMessage']
_TASKCLUSTERHELLOREQUEST = DESCRIPTOR.message_types_by_name['TaskClusterHelloRequest']
_TASKCLUSTERHELLORESPONSE = DESCRIPTOR.message_types_by_name['TaskClusterHelloResponse']
_TASKCURRENTINPUTSRESPONSE = DESCRIPTOR.message_types_by_name['TaskCurrentInputsResponse']
_TASKINFO = DESCRIPTOR.message_types_by_name['TaskInfo']
_TASKLISTREQUEST = DESCRIPTOR.message_types_by_name['TaskListRequest']
_TASKLISTRESPONSE = DESCRIPTOR.message_types_by_name['TaskListResponse']
_TASKLOGS = DESCRIPTOR.message_types_by_name['TaskLogs']
_TASKLOGSBATCH = DESCRIPTOR.message_types_by_name['TaskLogsBatch']
_TASKPROGRESS = DESCRIPTOR.message_types_by_name['TaskProgress']
_TASKRESULTREQUEST = DESCRIPTOR.message_types_by_name['TaskResultRequest']
_TASKSTATS = DESCRIPTOR.message_types_by_name['TaskStats']
_TASKTEMPLATE = DESCRIPTOR.message_types_by_name['TaskTemplate']
_TOKENFLOWCREATEREQUEST = DESCRIPTOR.message_types_by_name['TokenFlowCreateRequest']
_TOKENFLOWCREATERESPONSE = DESCRIPTOR.message_types_by_name['TokenFlowCreateResponse']
_TOKENFLOWWAITREQUEST = DESCRIPTOR.message_types_by_name['TokenFlowWaitRequest']
_TOKENFLOWWAITRESPONSE = DESCRIPTOR.message_types_by_name['TokenFlowWaitResponse']
_TUNNELDATA = DESCRIPTOR.message_types_by_name['TunnelData']
_TUNNELSTARTREQUEST = DESCRIPTOR.message_types_by_name['TunnelStartRequest']
_TUNNELSTARTRESPONSE = DESCRIPTOR.message_types_by_name['TunnelStartResponse']
_TUNNELSTOPREQUEST = DESCRIPTOR.message_types_by_name['TunnelStopRequest']
_TUNNELSTOPRESPONSE = DESCRIPTOR.message_types_by_name['TunnelStopResponse']
_VOLUMECOMMITREQUEST = DESCRIPTOR.message_types_by_name['VolumeCommitRequest']
_VOLUMECOMMITRESPONSE = DESCRIPTOR.message_types_by_name['VolumeCommitResponse']
_VOLUMECOPYFILES2REQUEST = DESCRIPTOR.message_types_by_name['VolumeCopyFiles2Request']
_VOLUMECOPYFILESREQUEST = DESCRIPTOR.message_types_by_name['VolumeCopyFilesRequest']
_VOLUMEDELETEREQUEST = DESCRIPTOR.message_types_by_name['VolumeDeleteRequest']
_VOLUMEGETFILE2REQUEST = DESCRIPTOR.message_types_by_name['VolumeGetFile2Request']
_VOLUMEGETFILE2RESPONSE = DESCRIPTOR.message_types_by_name['VolumeGetFile2Response']
_VOLUMEGETFILEREQUEST = DESCRIPTOR.message_types_by_name['VolumeGetFileRequest']
_VOLUMEGETFILERESPONSE = DESCRIPTOR.message_types_by_name['VolumeGetFileResponse']
_VOLUMEGETORCREATEREQUEST = DESCRIPTOR.message_types_by_name['VolumeGetOrCreateRequest']
_VOLUMEGETORCREATERESPONSE = DESCRIPTOR.message_types_by_name['VolumeGetOrCreateResponse']
_VOLUMEHEARTBEATREQUEST = DESCRIPTOR.message_types_by_name['VolumeHeartbeatRequest']
_VOLUMELISTFILES2REQUEST = DESCRIPTOR.message_types_by_name['VolumeListFiles2Request']
_VOLUMELISTFILES2RESPONSE = DESCRIPTOR.message_types_by_name['VolumeListFiles2Response']
_VOLUMELISTFILESREQUEST = DESCRIPTOR.message_types_by_name['VolumeListFilesRequest']
_VOLUMELISTFILESRESPONSE = DESCRIPTOR.message_types_by_name['VolumeListFilesResponse']
_VOLUMELISTITEM = DESCRIPTOR.message_types_by_name['VolumeListItem']
_VOLUMELISTREQUEST = DESCRIPTOR.message_types_by_name['VolumeListRequest']
_VOLUMELISTRESPONSE = DESCRIPTOR.message_types_by_name['VolumeListResponse']
_VOLUMEMETADATA = DESCRIPTOR.message_types_by_name['VolumeMetadata']
_VOLUMEMOUNT = DESCRIPTOR.message_types_by_name['VolumeMount']
_VOLUMEPUTFILES2REQUEST = DESCRIPTOR.message_types_by_name['VolumePutFiles2Request']
_VOLUMEPUTFILES2REQUEST_FILE = _VOLUMEPUTFILES2REQUEST.nested_types_by_name['File']
_VOLUMEPUTFILES2REQUEST_BLOCK = _VOLUMEPUTFILES2REQUEST.nested_types_by_name['Block']
_VOLUMEPUTFILES2RESPONSE = DESCRIPTOR.message_types_by_name['VolumePutFiles2Response']
_VOLUMEPUTFILES2RESPONSE_MISSINGBLOCK = _VOLUMEPUTFILES2RESPONSE.nested_types_by_name['MissingBlock']
_VOLUMEPUTFILESREQUEST = DESCRIPTOR.message_types_by_name['VolumePutFilesRequest']
_VOLUMERELOADREQUEST = DESCRIPTOR.message_types_by_name['VolumeReloadRequest']
_VOLUMEREMOVEFILE2REQUEST = DESCRIPTOR.message_types_by_name['VolumeRemoveFile2Request']
_VOLUMEREMOVEFILEREQUEST = DESCRIPTOR.message_types_by_name['VolumeRemoveFileRequest']
_VOLUMERENAMEREQUEST = DESCRIPTOR.message_types_by_name['VolumeRenameRequest']
_WARNING = DESCRIPTOR.message_types_by_name['Warning']
_WEBURLINFO = DESCRIPTOR.message_types_by_name['WebUrlInfo']
_WEBHOOKCONFIG = DESCRIPTOR.message_types_by_name['WebhookConfig']
_WORKSPACENAMELOOKUPRESPONSE = DESCRIPTOR.message_types_by_name['WorkspaceNameLookupResponse']
_CLASSPARAMETERINFO_PARAMETERSERIALIZATIONFORMAT = _CLASSPARAMETERINFO.enum_types_by_name['ParameterSerializationFormat']
_CLOUDBUCKETMOUNT_BUCKETTYPE = _CLOUDBUCKETMOUNT.enum_types_by_name['BucketType']
_FILEENTRY_FILETYPE = _FILEENTRY.enum_types_by_name['FileType']
_FUNCTION_DEFINITIONTYPE = _FUNCTION.enum_types_by_name['DefinitionType']
_FUNCTION_FUNCTIONTYPE = _FUNCTION.enum_types_by_name['FunctionType']
_FUNCTIONSCHEMA_FUNCTIONSCHEMATYPE = _FUNCTIONSCHEMA.enum_types_by_name['FunctionSchemaType']
_GENERICRESULT_GENERICSTATUS = _GENERICRESULT.enum_types_by_name['GenericStatus']
_NETWORKACCESS_NETWORKACCESSTYPE = _NETWORKACCESS.enum_types_by_name['NetworkAccessType']
_PTYINFO_PTYTYPE = _PTYINFO.enum_types_by_name['PTYType']
_WARNING_WARNINGTYPE = _WARNING.enum_types_by_name['WarningType']
AppClientDisconnectRequest = _reflection.GeneratedProtocolMessageType('AppClientDisconnectRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPCLIENTDISCONNECTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppClientDisconnectRequest)
  })
_sym_db.RegisterMessage(AppClientDisconnectRequest)

AppCreateRequest = _reflection.GeneratedProtocolMessageType('AppCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppCreateRequest)
  })
_sym_db.RegisterMessage(AppCreateRequest)

AppCreateResponse = _reflection.GeneratedProtocolMessageType('AppCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppCreateResponse)
  })
_sym_db.RegisterMessage(AppCreateResponse)

AppDeployRequest = _reflection.GeneratedProtocolMessageType('AppDeployRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPDEPLOYREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppDeployRequest)
  })
_sym_db.RegisterMessage(AppDeployRequest)

AppDeployResponse = _reflection.GeneratedProtocolMessageType('AppDeployResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPDEPLOYRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppDeployResponse)
  })
_sym_db.RegisterMessage(AppDeployResponse)

AppDeploymentHistory = _reflection.GeneratedProtocolMessageType('AppDeploymentHistory', (_message.Message,), {
  'DESCRIPTOR' : _APPDEPLOYMENTHISTORY,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppDeploymentHistory)
  })
_sym_db.RegisterMessage(AppDeploymentHistory)

AppDeploymentHistoryRequest = _reflection.GeneratedProtocolMessageType('AppDeploymentHistoryRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPDEPLOYMENTHISTORYREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppDeploymentHistoryRequest)
  })
_sym_db.RegisterMessage(AppDeploymentHistoryRequest)

AppDeploymentHistoryResponse = _reflection.GeneratedProtocolMessageType('AppDeploymentHistoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPDEPLOYMENTHISTORYRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppDeploymentHistoryResponse)
  })
_sym_db.RegisterMessage(AppDeploymentHistoryResponse)

AppGetByDeploymentNameRequest = _reflection.GeneratedProtocolMessageType('AppGetByDeploymentNameRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPGETBYDEPLOYMENTNAMEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetByDeploymentNameRequest)
  })
_sym_db.RegisterMessage(AppGetByDeploymentNameRequest)

AppGetByDeploymentNameResponse = _reflection.GeneratedProtocolMessageType('AppGetByDeploymentNameResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPGETBYDEPLOYMENTNAMERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetByDeploymentNameResponse)
  })
_sym_db.RegisterMessage(AppGetByDeploymentNameResponse)

AppGetLayoutRequest = _reflection.GeneratedProtocolMessageType('AppGetLayoutRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPGETLAYOUTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetLayoutRequest)
  })
_sym_db.RegisterMessage(AppGetLayoutRequest)

AppGetLayoutResponse = _reflection.GeneratedProtocolMessageType('AppGetLayoutResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPGETLAYOUTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetLayoutResponse)
  })
_sym_db.RegisterMessage(AppGetLayoutResponse)

AppGetLogsRequest = _reflection.GeneratedProtocolMessageType('AppGetLogsRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPGETLOGSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetLogsRequest)
  })
_sym_db.RegisterMessage(AppGetLogsRequest)

AppGetObjectsItem = _reflection.GeneratedProtocolMessageType('AppGetObjectsItem', (_message.Message,), {
  'DESCRIPTOR' : _APPGETOBJECTSITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetObjectsItem)
  })
_sym_db.RegisterMessage(AppGetObjectsItem)

AppGetObjectsRequest = _reflection.GeneratedProtocolMessageType('AppGetObjectsRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPGETOBJECTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetObjectsRequest)
  })
_sym_db.RegisterMessage(AppGetObjectsRequest)

AppGetObjectsResponse = _reflection.GeneratedProtocolMessageType('AppGetObjectsResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPGETOBJECTSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetObjectsResponse)
  })
_sym_db.RegisterMessage(AppGetObjectsResponse)

AppGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('AppGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetOrCreateRequest)
  })
_sym_db.RegisterMessage(AppGetOrCreateRequest)

AppGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('AppGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppGetOrCreateResponse)
  })
_sym_db.RegisterMessage(AppGetOrCreateResponse)

AppHeartbeatRequest = _reflection.GeneratedProtocolMessageType('AppHeartbeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPHEARTBEATREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppHeartbeatRequest)
  })
_sym_db.RegisterMessage(AppHeartbeatRequest)

AppLayout = _reflection.GeneratedProtocolMessageType('AppLayout', (_message.Message,), {

  'FunctionIdsEntry' : _reflection.GeneratedProtocolMessageType('FunctionIdsEntry', (_message.Message,), {
    'DESCRIPTOR' : _APPLAYOUT_FUNCTIONIDSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.AppLayout.FunctionIdsEntry)
    })
  ,

  'ClassIdsEntry' : _reflection.GeneratedProtocolMessageType('ClassIdsEntry', (_message.Message,), {
    'DESCRIPTOR' : _APPLAYOUT_CLASSIDSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.AppLayout.ClassIdsEntry)
    })
  ,
  'DESCRIPTOR' : _APPLAYOUT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppLayout)
  })
_sym_db.RegisterMessage(AppLayout)
_sym_db.RegisterMessage(AppLayout.FunctionIdsEntry)
_sym_db.RegisterMessage(AppLayout.ClassIdsEntry)

AppListRequest = _reflection.GeneratedProtocolMessageType('AppListRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPLISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppListRequest)
  })
_sym_db.RegisterMessage(AppListRequest)

AppListResponse = _reflection.GeneratedProtocolMessageType('AppListResponse', (_message.Message,), {

  'AppListItem' : _reflection.GeneratedProtocolMessageType('AppListItem', (_message.Message,), {
    'DESCRIPTOR' : _APPLISTRESPONSE_APPLISTITEM,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.AppListResponse.AppListItem)
    })
  ,
  'DESCRIPTOR' : _APPLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppListResponse)
  })
_sym_db.RegisterMessage(AppListResponse)
_sym_db.RegisterMessage(AppListResponse.AppListItem)

AppLookupRequest = _reflection.GeneratedProtocolMessageType('AppLookupRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPLOOKUPREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppLookupRequest)
  })
_sym_db.RegisterMessage(AppLookupRequest)

AppLookupResponse = _reflection.GeneratedProtocolMessageType('AppLookupResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPLOOKUPRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppLookupResponse)
  })
_sym_db.RegisterMessage(AppLookupResponse)

AppPublishRequest = _reflection.GeneratedProtocolMessageType('AppPublishRequest', (_message.Message,), {

  'FunctionIdsEntry' : _reflection.GeneratedProtocolMessageType('FunctionIdsEntry', (_message.Message,), {
    'DESCRIPTOR' : _APPPUBLISHREQUEST_FUNCTIONIDSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.AppPublishRequest.FunctionIdsEntry)
    })
  ,

  'ClassIdsEntry' : _reflection.GeneratedProtocolMessageType('ClassIdsEntry', (_message.Message,), {
    'DESCRIPTOR' : _APPPUBLISHREQUEST_CLASSIDSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.AppPublishRequest.ClassIdsEntry)
    })
  ,

  'DefinitionIdsEntry' : _reflection.GeneratedProtocolMessageType('DefinitionIdsEntry', (_message.Message,), {
    'DESCRIPTOR' : _APPPUBLISHREQUEST_DEFINITIONIDSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.AppPublishRequest.DefinitionIdsEntry)
    })
  ,
  'DESCRIPTOR' : _APPPUBLISHREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppPublishRequest)
  })
_sym_db.RegisterMessage(AppPublishRequest)
_sym_db.RegisterMessage(AppPublishRequest.FunctionIdsEntry)
_sym_db.RegisterMessage(AppPublishRequest.ClassIdsEntry)
_sym_db.RegisterMessage(AppPublishRequest.DefinitionIdsEntry)

AppPublishResponse = _reflection.GeneratedProtocolMessageType('AppPublishResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPPUBLISHRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppPublishResponse)
  })
_sym_db.RegisterMessage(AppPublishResponse)

AppRollbackRequest = _reflection.GeneratedProtocolMessageType('AppRollbackRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPROLLBACKREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppRollbackRequest)
  })
_sym_db.RegisterMessage(AppRollbackRequest)

AppSetObjectsRequest = _reflection.GeneratedProtocolMessageType('AppSetObjectsRequest', (_message.Message,), {

  'IndexedObjectIdsEntry' : _reflection.GeneratedProtocolMessageType('IndexedObjectIdsEntry', (_message.Message,), {
    'DESCRIPTOR' : _APPSETOBJECTSREQUEST_INDEXEDOBJECTIDSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.AppSetObjectsRequest.IndexedObjectIdsEntry)
    })
  ,
  'DESCRIPTOR' : _APPSETOBJECTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppSetObjectsRequest)
  })
_sym_db.RegisterMessage(AppSetObjectsRequest)
_sym_db.RegisterMessage(AppSetObjectsRequest.IndexedObjectIdsEntry)

AppStopRequest = _reflection.GeneratedProtocolMessageType('AppStopRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPSTOPREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AppStopRequest)
  })
_sym_db.RegisterMessage(AppStopRequest)

Asgi = _reflection.GeneratedProtocolMessageType('Asgi', (_message.Message,), {

  'Http' : _reflection.GeneratedProtocolMessageType('Http', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_HTTP,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.Http)
    })
  ,

  'HttpRequest' : _reflection.GeneratedProtocolMessageType('HttpRequest', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_HTTPREQUEST,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.HttpRequest)
    })
  ,

  'HttpResponseStart' : _reflection.GeneratedProtocolMessageType('HttpResponseStart', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_HTTPRESPONSESTART,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.HttpResponseStart)
    })
  ,

  'HttpResponseBody' : _reflection.GeneratedProtocolMessageType('HttpResponseBody', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_HTTPRESPONSEBODY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.HttpResponseBody)
    })
  ,

  'HttpResponseTrailers' : _reflection.GeneratedProtocolMessageType('HttpResponseTrailers', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_HTTPRESPONSETRAILERS,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.HttpResponseTrailers)
    })
  ,

  'HttpDisconnect' : _reflection.GeneratedProtocolMessageType('HttpDisconnect', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_HTTPDISCONNECT,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.HttpDisconnect)
    })
  ,

  'Websocket' : _reflection.GeneratedProtocolMessageType('Websocket', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_WEBSOCKET,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.Websocket)
    })
  ,

  'WebsocketConnect' : _reflection.GeneratedProtocolMessageType('WebsocketConnect', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_WEBSOCKETCONNECT,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.WebsocketConnect)
    })
  ,

  'WebsocketAccept' : _reflection.GeneratedProtocolMessageType('WebsocketAccept', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_WEBSOCKETACCEPT,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.WebsocketAccept)
    })
  ,

  'WebsocketReceive' : _reflection.GeneratedProtocolMessageType('WebsocketReceive', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_WEBSOCKETRECEIVE,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.WebsocketReceive)
    })
  ,

  'WebsocketSend' : _reflection.GeneratedProtocolMessageType('WebsocketSend', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_WEBSOCKETSEND,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.WebsocketSend)
    })
  ,

  'WebsocketDisconnect' : _reflection.GeneratedProtocolMessageType('WebsocketDisconnect', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_WEBSOCKETDISCONNECT,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.WebsocketDisconnect)
    })
  ,

  'WebsocketClose' : _reflection.GeneratedProtocolMessageType('WebsocketClose', (_message.Message,), {
    'DESCRIPTOR' : _ASGI_WEBSOCKETCLOSE,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Asgi.WebsocketClose)
    })
  ,
  'DESCRIPTOR' : _ASGI,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Asgi)
  })
_sym_db.RegisterMessage(Asgi)
_sym_db.RegisterMessage(Asgi.Http)
_sym_db.RegisterMessage(Asgi.HttpRequest)
_sym_db.RegisterMessage(Asgi.HttpResponseStart)
_sym_db.RegisterMessage(Asgi.HttpResponseBody)
_sym_db.RegisterMessage(Asgi.HttpResponseTrailers)
_sym_db.RegisterMessage(Asgi.HttpDisconnect)
_sym_db.RegisterMessage(Asgi.Websocket)
_sym_db.RegisterMessage(Asgi.WebsocketConnect)
_sym_db.RegisterMessage(Asgi.WebsocketAccept)
_sym_db.RegisterMessage(Asgi.WebsocketReceive)
_sym_db.RegisterMessage(Asgi.WebsocketSend)
_sym_db.RegisterMessage(Asgi.WebsocketDisconnect)
_sym_db.RegisterMessage(Asgi.WebsocketClose)

AttemptAwaitRequest = _reflection.GeneratedProtocolMessageType('AttemptAwaitRequest', (_message.Message,), {
  'DESCRIPTOR' : _ATTEMPTAWAITREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AttemptAwaitRequest)
  })
_sym_db.RegisterMessage(AttemptAwaitRequest)

AttemptAwaitResponse = _reflection.GeneratedProtocolMessageType('AttemptAwaitResponse', (_message.Message,), {
  'DESCRIPTOR' : _ATTEMPTAWAITRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AttemptAwaitResponse)
  })
_sym_db.RegisterMessage(AttemptAwaitResponse)

AttemptRetryRequest = _reflection.GeneratedProtocolMessageType('AttemptRetryRequest', (_message.Message,), {
  'DESCRIPTOR' : _ATTEMPTRETRYREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AttemptRetryRequest)
  })
_sym_db.RegisterMessage(AttemptRetryRequest)

AttemptRetryResponse = _reflection.GeneratedProtocolMessageType('AttemptRetryResponse', (_message.Message,), {
  'DESCRIPTOR' : _ATTEMPTRETRYRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AttemptRetryResponse)
  })
_sym_db.RegisterMessage(AttemptRetryResponse)

AttemptStartRequest = _reflection.GeneratedProtocolMessageType('AttemptStartRequest', (_message.Message,), {
  'DESCRIPTOR' : _ATTEMPTSTARTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AttemptStartRequest)
  })
_sym_db.RegisterMessage(AttemptStartRequest)

AttemptStartResponse = _reflection.GeneratedProtocolMessageType('AttemptStartResponse', (_message.Message,), {
  'DESCRIPTOR' : _ATTEMPTSTARTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AttemptStartResponse)
  })
_sym_db.RegisterMessage(AttemptStartResponse)

AutoscalerSettings = _reflection.GeneratedProtocolMessageType('AutoscalerSettings', (_message.Message,), {
  'DESCRIPTOR' : _AUTOSCALERSETTINGS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.AutoscalerSettings)
  })
_sym_db.RegisterMessage(AutoscalerSettings)

BaseImage = _reflection.GeneratedProtocolMessageType('BaseImage', (_message.Message,), {
  'DESCRIPTOR' : _BASEIMAGE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.BaseImage)
  })
_sym_db.RegisterMessage(BaseImage)

BlobCreateRequest = _reflection.GeneratedProtocolMessageType('BlobCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _BLOBCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.BlobCreateRequest)
  })
_sym_db.RegisterMessage(BlobCreateRequest)

BlobCreateResponse = _reflection.GeneratedProtocolMessageType('BlobCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _BLOBCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.BlobCreateResponse)
  })
_sym_db.RegisterMessage(BlobCreateResponse)

BlobGetRequest = _reflection.GeneratedProtocolMessageType('BlobGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _BLOBGETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.BlobGetRequest)
  })
_sym_db.RegisterMessage(BlobGetRequest)

BlobGetResponse = _reflection.GeneratedProtocolMessageType('BlobGetResponse', (_message.Message,), {
  'DESCRIPTOR' : _BLOBGETRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.BlobGetResponse)
  })
_sym_db.RegisterMessage(BlobGetResponse)

BuildFunction = _reflection.GeneratedProtocolMessageType('BuildFunction', (_message.Message,), {
  'DESCRIPTOR' : _BUILDFUNCTION,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.BuildFunction)
  })
_sym_db.RegisterMessage(BuildFunction)

CancelInputEvent = _reflection.GeneratedProtocolMessageType('CancelInputEvent', (_message.Message,), {
  'DESCRIPTOR' : _CANCELINPUTEVENT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.CancelInputEvent)
  })
_sym_db.RegisterMessage(CancelInputEvent)

CheckpointInfo = _reflection.GeneratedProtocolMessageType('CheckpointInfo', (_message.Message,), {
  'DESCRIPTOR' : _CHECKPOINTINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.CheckpointInfo)
  })
_sym_db.RegisterMessage(CheckpointInfo)

ClassCreateRequest = _reflection.GeneratedProtocolMessageType('ClassCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _CLASSCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassCreateRequest)
  })
_sym_db.RegisterMessage(ClassCreateRequest)

ClassCreateResponse = _reflection.GeneratedProtocolMessageType('ClassCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _CLASSCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassCreateResponse)
  })
_sym_db.RegisterMessage(ClassCreateResponse)

ClassGetRequest = _reflection.GeneratedProtocolMessageType('ClassGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _CLASSGETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassGetRequest)
  })
_sym_db.RegisterMessage(ClassGetRequest)

ClassGetResponse = _reflection.GeneratedProtocolMessageType('ClassGetResponse', (_message.Message,), {
  'DESCRIPTOR' : _CLASSGETRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassGetResponse)
  })
_sym_db.RegisterMessage(ClassGetResponse)

ClassHandleMetadata = _reflection.GeneratedProtocolMessageType('ClassHandleMetadata', (_message.Message,), {
  'DESCRIPTOR' : _CLASSHANDLEMETADATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassHandleMetadata)
  })
_sym_db.RegisterMessage(ClassHandleMetadata)

ClassMethod = _reflection.GeneratedProtocolMessageType('ClassMethod', (_message.Message,), {
  'DESCRIPTOR' : _CLASSMETHOD,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassMethod)
  })
_sym_db.RegisterMessage(ClassMethod)

ClassParameterInfo = _reflection.GeneratedProtocolMessageType('ClassParameterInfo', (_message.Message,), {
  'DESCRIPTOR' : _CLASSPARAMETERINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassParameterInfo)
  })
_sym_db.RegisterMessage(ClassParameterInfo)

ClassParameterSet = _reflection.GeneratedProtocolMessageType('ClassParameterSet', (_message.Message,), {
  'DESCRIPTOR' : _CLASSPARAMETERSET,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassParameterSet)
  })
_sym_db.RegisterMessage(ClassParameterSet)

ClassParameterSpec = _reflection.GeneratedProtocolMessageType('ClassParameterSpec', (_message.Message,), {
  'DESCRIPTOR' : _CLASSPARAMETERSPEC,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassParameterSpec)
  })
_sym_db.RegisterMessage(ClassParameterSpec)

ClassParameterValue = _reflection.GeneratedProtocolMessageType('ClassParameterValue', (_message.Message,), {
  'DESCRIPTOR' : _CLASSPARAMETERVALUE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClassParameterValue)
  })
_sym_db.RegisterMessage(ClassParameterValue)

ClientHelloResponse = _reflection.GeneratedProtocolMessageType('ClientHelloResponse', (_message.Message,), {
  'DESCRIPTOR' : _CLIENTHELLORESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClientHelloResponse)
  })
_sym_db.RegisterMessage(ClientHelloResponse)

CloudBucketMount = _reflection.GeneratedProtocolMessageType('CloudBucketMount', (_message.Message,), {
  'DESCRIPTOR' : _CLOUDBUCKETMOUNT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.CloudBucketMount)
  })
_sym_db.RegisterMessage(CloudBucketMount)

ClusterGetRequest = _reflection.GeneratedProtocolMessageType('ClusterGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _CLUSTERGETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClusterGetRequest)
  })
_sym_db.RegisterMessage(ClusterGetRequest)

ClusterGetResponse = _reflection.GeneratedProtocolMessageType('ClusterGetResponse', (_message.Message,), {
  'DESCRIPTOR' : _CLUSTERGETRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClusterGetResponse)
  })
_sym_db.RegisterMessage(ClusterGetResponse)

ClusterListRequest = _reflection.GeneratedProtocolMessageType('ClusterListRequest', (_message.Message,), {
  'DESCRIPTOR' : _CLUSTERLISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClusterListRequest)
  })
_sym_db.RegisterMessage(ClusterListRequest)

ClusterListResponse = _reflection.GeneratedProtocolMessageType('ClusterListResponse', (_message.Message,), {
  'DESCRIPTOR' : _CLUSTERLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClusterListResponse)
  })
_sym_db.RegisterMessage(ClusterListResponse)

ClusterStats = _reflection.GeneratedProtocolMessageType('ClusterStats', (_message.Message,), {
  'DESCRIPTOR' : _CLUSTERSTATS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ClusterStats)
  })
_sym_db.RegisterMessage(ClusterStats)

CommitInfo = _reflection.GeneratedProtocolMessageType('CommitInfo', (_message.Message,), {
  'DESCRIPTOR' : _COMMITINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.CommitInfo)
  })
_sym_db.RegisterMessage(CommitInfo)

ContainerArguments = _reflection.GeneratedProtocolMessageType('ContainerArguments', (_message.Message,), {

  'TracingContextEntry' : _reflection.GeneratedProtocolMessageType('TracingContextEntry', (_message.Message,), {
    'DESCRIPTOR' : _CONTAINERARGUMENTS_TRACINGCONTEXTENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.ContainerArguments.TracingContextEntry)
    })
  ,
  'DESCRIPTOR' : _CONTAINERARGUMENTS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerArguments)
  })
_sym_db.RegisterMessage(ContainerArguments)
_sym_db.RegisterMessage(ContainerArguments.TracingContextEntry)

ContainerCheckpointRequest = _reflection.GeneratedProtocolMessageType('ContainerCheckpointRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERCHECKPOINTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerCheckpointRequest)
  })
_sym_db.RegisterMessage(ContainerCheckpointRequest)

ContainerExecGetOutputRequest = _reflection.GeneratedProtocolMessageType('ContainerExecGetOutputRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINEREXECGETOUTPUTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerExecGetOutputRequest)
  })
_sym_db.RegisterMessage(ContainerExecGetOutputRequest)

ContainerExecPutInputRequest = _reflection.GeneratedProtocolMessageType('ContainerExecPutInputRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINEREXECPUTINPUTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerExecPutInputRequest)
  })
_sym_db.RegisterMessage(ContainerExecPutInputRequest)

ContainerExecRequest = _reflection.GeneratedProtocolMessageType('ContainerExecRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINEREXECREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerExecRequest)
  })
_sym_db.RegisterMessage(ContainerExecRequest)

ContainerExecResponse = _reflection.GeneratedProtocolMessageType('ContainerExecResponse', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINEREXECRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerExecResponse)
  })
_sym_db.RegisterMessage(ContainerExecResponse)

ContainerExecWaitRequest = _reflection.GeneratedProtocolMessageType('ContainerExecWaitRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINEREXECWAITREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerExecWaitRequest)
  })
_sym_db.RegisterMessage(ContainerExecWaitRequest)

ContainerExecWaitResponse = _reflection.GeneratedProtocolMessageType('ContainerExecWaitResponse', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINEREXECWAITRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerExecWaitResponse)
  })
_sym_db.RegisterMessage(ContainerExecWaitResponse)

ContainerFileCloseRequest = _reflection.GeneratedProtocolMessageType('ContainerFileCloseRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILECLOSEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileCloseRequest)
  })
_sym_db.RegisterMessage(ContainerFileCloseRequest)

ContainerFileDeleteBytesRequest = _reflection.GeneratedProtocolMessageType('ContainerFileDeleteBytesRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEDELETEBYTESREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileDeleteBytesRequest)
  })
_sym_db.RegisterMessage(ContainerFileDeleteBytesRequest)

ContainerFileFlushRequest = _reflection.GeneratedProtocolMessageType('ContainerFileFlushRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEFLUSHREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileFlushRequest)
  })
_sym_db.RegisterMessage(ContainerFileFlushRequest)

ContainerFileLsRequest = _reflection.GeneratedProtocolMessageType('ContainerFileLsRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILELSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileLsRequest)
  })
_sym_db.RegisterMessage(ContainerFileLsRequest)

ContainerFileMkdirRequest = _reflection.GeneratedProtocolMessageType('ContainerFileMkdirRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEMKDIRREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileMkdirRequest)
  })
_sym_db.RegisterMessage(ContainerFileMkdirRequest)

ContainerFileOpenRequest = _reflection.GeneratedProtocolMessageType('ContainerFileOpenRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEOPENREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileOpenRequest)
  })
_sym_db.RegisterMessage(ContainerFileOpenRequest)

ContainerFileReadLineRequest = _reflection.GeneratedProtocolMessageType('ContainerFileReadLineRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEREADLINEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileReadLineRequest)
  })
_sym_db.RegisterMessage(ContainerFileReadLineRequest)

ContainerFileReadRequest = _reflection.GeneratedProtocolMessageType('ContainerFileReadRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEREADREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileReadRequest)
  })
_sym_db.RegisterMessage(ContainerFileReadRequest)

ContainerFileRmRequest = _reflection.GeneratedProtocolMessageType('ContainerFileRmRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILERMREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileRmRequest)
  })
_sym_db.RegisterMessage(ContainerFileRmRequest)

ContainerFileSeekRequest = _reflection.GeneratedProtocolMessageType('ContainerFileSeekRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILESEEKREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileSeekRequest)
  })
_sym_db.RegisterMessage(ContainerFileSeekRequest)

ContainerFileWatchRequest = _reflection.GeneratedProtocolMessageType('ContainerFileWatchRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEWATCHREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileWatchRequest)
  })
_sym_db.RegisterMessage(ContainerFileWatchRequest)

ContainerFileWriteReplaceBytesRequest = _reflection.GeneratedProtocolMessageType('ContainerFileWriteReplaceBytesRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEWRITEREPLACEBYTESREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileWriteReplaceBytesRequest)
  })
_sym_db.RegisterMessage(ContainerFileWriteReplaceBytesRequest)

ContainerFileWriteRequest = _reflection.GeneratedProtocolMessageType('ContainerFileWriteRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILEWRITEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFileWriteRequest)
  })
_sym_db.RegisterMessage(ContainerFileWriteRequest)

ContainerFilesystemExecGetOutputRequest = _reflection.GeneratedProtocolMessageType('ContainerFilesystemExecGetOutputRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILESYSTEMEXECGETOUTPUTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFilesystemExecGetOutputRequest)
  })
_sym_db.RegisterMessage(ContainerFilesystemExecGetOutputRequest)

ContainerFilesystemExecRequest = _reflection.GeneratedProtocolMessageType('ContainerFilesystemExecRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILESYSTEMEXECREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFilesystemExecRequest)
  })
_sym_db.RegisterMessage(ContainerFilesystemExecRequest)

ContainerFilesystemExecResponse = _reflection.GeneratedProtocolMessageType('ContainerFilesystemExecResponse', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERFILESYSTEMEXECRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerFilesystemExecResponse)
  })
_sym_db.RegisterMessage(ContainerFilesystemExecResponse)

ContainerHeartbeatRequest = _reflection.GeneratedProtocolMessageType('ContainerHeartbeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERHEARTBEATREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerHeartbeatRequest)
  })
_sym_db.RegisterMessage(ContainerHeartbeatRequest)

ContainerHeartbeatResponse = _reflection.GeneratedProtocolMessageType('ContainerHeartbeatResponse', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERHEARTBEATRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerHeartbeatResponse)
  })
_sym_db.RegisterMessage(ContainerHeartbeatResponse)

ContainerLogRequest = _reflection.GeneratedProtocolMessageType('ContainerLogRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERLOGREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerLogRequest)
  })
_sym_db.RegisterMessage(ContainerLogRequest)

ContainerStopRequest = _reflection.GeneratedProtocolMessageType('ContainerStopRequest', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERSTOPREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerStopRequest)
  })
_sym_db.RegisterMessage(ContainerStopRequest)

ContainerStopResponse = _reflection.GeneratedProtocolMessageType('ContainerStopResponse', (_message.Message,), {
  'DESCRIPTOR' : _CONTAINERSTOPRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ContainerStopResponse)
  })
_sym_db.RegisterMessage(ContainerStopResponse)

CustomDomainConfig = _reflection.GeneratedProtocolMessageType('CustomDomainConfig', (_message.Message,), {
  'DESCRIPTOR' : _CUSTOMDOMAINCONFIG,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.CustomDomainConfig)
  })
_sym_db.RegisterMessage(CustomDomainConfig)

CustomDomainInfo = _reflection.GeneratedProtocolMessageType('CustomDomainInfo', (_message.Message,), {
  'DESCRIPTOR' : _CUSTOMDOMAININFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.CustomDomainInfo)
  })
_sym_db.RegisterMessage(CustomDomainInfo)

DNSRecord = _reflection.GeneratedProtocolMessageType('DNSRecord', (_message.Message,), {
  'DESCRIPTOR' : _DNSRECORD,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DNSRecord)
  })
_sym_db.RegisterMessage(DNSRecord)

DataChunk = _reflection.GeneratedProtocolMessageType('DataChunk', (_message.Message,), {
  'DESCRIPTOR' : _DATACHUNK,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DataChunk)
  })
_sym_db.RegisterMessage(DataChunk)

DictClearRequest = _reflection.GeneratedProtocolMessageType('DictClearRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTCLEARREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictClearRequest)
  })
_sym_db.RegisterMessage(DictClearRequest)

DictContainsRequest = _reflection.GeneratedProtocolMessageType('DictContainsRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTCONTAINSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictContainsRequest)
  })
_sym_db.RegisterMessage(DictContainsRequest)

DictContainsResponse = _reflection.GeneratedProtocolMessageType('DictContainsResponse', (_message.Message,), {
  'DESCRIPTOR' : _DICTCONTAINSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictContainsResponse)
  })
_sym_db.RegisterMessage(DictContainsResponse)

DictContentsRequest = _reflection.GeneratedProtocolMessageType('DictContentsRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTCONTENTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictContentsRequest)
  })
_sym_db.RegisterMessage(DictContentsRequest)

DictDeleteRequest = _reflection.GeneratedProtocolMessageType('DictDeleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTDELETEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictDeleteRequest)
  })
_sym_db.RegisterMessage(DictDeleteRequest)

DictEntry = _reflection.GeneratedProtocolMessageType('DictEntry', (_message.Message,), {
  'DESCRIPTOR' : _DICTENTRY,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictEntry)
  })
_sym_db.RegisterMessage(DictEntry)

DictGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('DictGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictGetOrCreateRequest)
  })
_sym_db.RegisterMessage(DictGetOrCreateRequest)

DictGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('DictGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _DICTGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictGetOrCreateResponse)
  })
_sym_db.RegisterMessage(DictGetOrCreateResponse)

DictGetRequest = _reflection.GeneratedProtocolMessageType('DictGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTGETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictGetRequest)
  })
_sym_db.RegisterMessage(DictGetRequest)

DictGetResponse = _reflection.GeneratedProtocolMessageType('DictGetResponse', (_message.Message,), {
  'DESCRIPTOR' : _DICTGETRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictGetResponse)
  })
_sym_db.RegisterMessage(DictGetResponse)

DictHeartbeatRequest = _reflection.GeneratedProtocolMessageType('DictHeartbeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTHEARTBEATREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictHeartbeatRequest)
  })
_sym_db.RegisterMessage(DictHeartbeatRequest)

DictLenRequest = _reflection.GeneratedProtocolMessageType('DictLenRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTLENREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictLenRequest)
  })
_sym_db.RegisterMessage(DictLenRequest)

DictLenResponse = _reflection.GeneratedProtocolMessageType('DictLenResponse', (_message.Message,), {
  'DESCRIPTOR' : _DICTLENRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictLenResponse)
  })
_sym_db.RegisterMessage(DictLenResponse)

DictListRequest = _reflection.GeneratedProtocolMessageType('DictListRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTLISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictListRequest)
  })
_sym_db.RegisterMessage(DictListRequest)

DictListResponse = _reflection.GeneratedProtocolMessageType('DictListResponse', (_message.Message,), {

  'DictInfo' : _reflection.GeneratedProtocolMessageType('DictInfo', (_message.Message,), {
    'DESCRIPTOR' : _DICTLISTRESPONSE_DICTINFO,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.DictListResponse.DictInfo)
    })
  ,
  'DESCRIPTOR' : _DICTLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictListResponse)
  })
_sym_db.RegisterMessage(DictListResponse)
_sym_db.RegisterMessage(DictListResponse.DictInfo)

DictPopRequest = _reflection.GeneratedProtocolMessageType('DictPopRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTPOPREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictPopRequest)
  })
_sym_db.RegisterMessage(DictPopRequest)

DictPopResponse = _reflection.GeneratedProtocolMessageType('DictPopResponse', (_message.Message,), {
  'DESCRIPTOR' : _DICTPOPRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictPopResponse)
  })
_sym_db.RegisterMessage(DictPopResponse)

DictUpdateRequest = _reflection.GeneratedProtocolMessageType('DictUpdateRequest', (_message.Message,), {
  'DESCRIPTOR' : _DICTUPDATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictUpdateRequest)
  })
_sym_db.RegisterMessage(DictUpdateRequest)

DictUpdateResponse = _reflection.GeneratedProtocolMessageType('DictUpdateResponse', (_message.Message,), {
  'DESCRIPTOR' : _DICTUPDATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DictUpdateResponse)
  })
_sym_db.RegisterMessage(DictUpdateResponse)

Domain = _reflection.GeneratedProtocolMessageType('Domain', (_message.Message,), {
  'DESCRIPTOR' : _DOMAIN,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Domain)
  })
_sym_db.RegisterMessage(Domain)

DomainCertificateVerifyRequest = _reflection.GeneratedProtocolMessageType('DomainCertificateVerifyRequest', (_message.Message,), {
  'DESCRIPTOR' : _DOMAINCERTIFICATEVERIFYREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DomainCertificateVerifyRequest)
  })
_sym_db.RegisterMessage(DomainCertificateVerifyRequest)

DomainCertificateVerifyResponse = _reflection.GeneratedProtocolMessageType('DomainCertificateVerifyResponse', (_message.Message,), {
  'DESCRIPTOR' : _DOMAINCERTIFICATEVERIFYRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DomainCertificateVerifyResponse)
  })
_sym_db.RegisterMessage(DomainCertificateVerifyResponse)

DomainCreateRequest = _reflection.GeneratedProtocolMessageType('DomainCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _DOMAINCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DomainCreateRequest)
  })
_sym_db.RegisterMessage(DomainCreateRequest)

DomainCreateResponse = _reflection.GeneratedProtocolMessageType('DomainCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _DOMAINCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DomainCreateResponse)
  })
_sym_db.RegisterMessage(DomainCreateResponse)

DomainListRequest = _reflection.GeneratedProtocolMessageType('DomainListRequest', (_message.Message,), {
  'DESCRIPTOR' : _DOMAINLISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DomainListRequest)
  })
_sym_db.RegisterMessage(DomainListRequest)

DomainListResponse = _reflection.GeneratedProtocolMessageType('DomainListResponse', (_message.Message,), {
  'DESCRIPTOR' : _DOMAINLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.DomainListResponse)
  })
_sym_db.RegisterMessage(DomainListResponse)

EnvironmentCreateRequest = _reflection.GeneratedProtocolMessageType('EnvironmentCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentCreateRequest)
  })
_sym_db.RegisterMessage(EnvironmentCreateRequest)

EnvironmentDeleteRequest = _reflection.GeneratedProtocolMessageType('EnvironmentDeleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTDELETEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentDeleteRequest)
  })
_sym_db.RegisterMessage(EnvironmentDeleteRequest)

EnvironmentGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('EnvironmentGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentGetOrCreateRequest)
  })
_sym_db.RegisterMessage(EnvironmentGetOrCreateRequest)

EnvironmentGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('EnvironmentGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentGetOrCreateResponse)
  })
_sym_db.RegisterMessage(EnvironmentGetOrCreateResponse)

EnvironmentListItem = _reflection.GeneratedProtocolMessageType('EnvironmentListItem', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTLISTITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentListItem)
  })
_sym_db.RegisterMessage(EnvironmentListItem)

EnvironmentListResponse = _reflection.GeneratedProtocolMessageType('EnvironmentListResponse', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentListResponse)
  })
_sym_db.RegisterMessage(EnvironmentListResponse)

EnvironmentMetadata = _reflection.GeneratedProtocolMessageType('EnvironmentMetadata', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTMETADATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentMetadata)
  })
_sym_db.RegisterMessage(EnvironmentMetadata)

EnvironmentSettings = _reflection.GeneratedProtocolMessageType('EnvironmentSettings', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTSETTINGS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentSettings)
  })
_sym_db.RegisterMessage(EnvironmentSettings)

EnvironmentUpdateRequest = _reflection.GeneratedProtocolMessageType('EnvironmentUpdateRequest', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTUPDATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.EnvironmentUpdateRequest)
  })
_sym_db.RegisterMessage(EnvironmentUpdateRequest)

FileEntry = _reflection.GeneratedProtocolMessageType('FileEntry', (_message.Message,), {
  'DESCRIPTOR' : _FILEENTRY,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FileEntry)
  })
_sym_db.RegisterMessage(FileEntry)

FilesystemRuntimeOutputBatch = _reflection.GeneratedProtocolMessageType('FilesystemRuntimeOutputBatch', (_message.Message,), {
  'DESCRIPTOR' : _FILESYSTEMRUNTIMEOUTPUTBATCH,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FilesystemRuntimeOutputBatch)
  })
_sym_db.RegisterMessage(FilesystemRuntimeOutputBatch)

Function = _reflection.GeneratedProtocolMessageType('Function', (_message.Message,), {

  'MethodDefinitionsEntry' : _reflection.GeneratedProtocolMessageType('MethodDefinitionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _FUNCTION_METHODDEFINITIONSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Function.MethodDefinitionsEntry)
    })
  ,

  'ExperimentalOptionsEntry' : _reflection.GeneratedProtocolMessageType('ExperimentalOptionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _FUNCTION_EXPERIMENTALOPTIONSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Function.ExperimentalOptionsEntry)
    })
  ,
  'DESCRIPTOR' : _FUNCTION,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Function)
  })
_sym_db.RegisterMessage(Function)
_sym_db.RegisterMessage(Function.MethodDefinitionsEntry)
_sym_db.RegisterMessage(Function.ExperimentalOptionsEntry)

FunctionAsyncInvokeRequest = _reflection.GeneratedProtocolMessageType('FunctionAsyncInvokeRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONASYNCINVOKEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionAsyncInvokeRequest)
  })
_sym_db.RegisterMessage(FunctionAsyncInvokeRequest)

FunctionAsyncInvokeResponse = _reflection.GeneratedProtocolMessageType('FunctionAsyncInvokeResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONASYNCINVOKERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionAsyncInvokeResponse)
  })
_sym_db.RegisterMessage(FunctionAsyncInvokeResponse)

FunctionBindParamsRequest = _reflection.GeneratedProtocolMessageType('FunctionBindParamsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONBINDPARAMSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionBindParamsRequest)
  })
_sym_db.RegisterMessage(FunctionBindParamsRequest)

FunctionBindParamsResponse = _reflection.GeneratedProtocolMessageType('FunctionBindParamsResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONBINDPARAMSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionBindParamsResponse)
  })
_sym_db.RegisterMessage(FunctionBindParamsResponse)

FunctionCallCallGraphInfo = _reflection.GeneratedProtocolMessageType('FunctionCallCallGraphInfo', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCALLCALLGRAPHINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCallCallGraphInfo)
  })
_sym_db.RegisterMessage(FunctionCallCallGraphInfo)

FunctionCallCancelRequest = _reflection.GeneratedProtocolMessageType('FunctionCallCancelRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCALLCANCELREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCallCancelRequest)
  })
_sym_db.RegisterMessage(FunctionCallCancelRequest)

FunctionCallGetDataRequest = _reflection.GeneratedProtocolMessageType('FunctionCallGetDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCALLGETDATAREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCallGetDataRequest)
  })
_sym_db.RegisterMessage(FunctionCallGetDataRequest)

FunctionCallInfo = _reflection.GeneratedProtocolMessageType('FunctionCallInfo', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCALLINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCallInfo)
  })
_sym_db.RegisterMessage(FunctionCallInfo)

FunctionCallListRequest = _reflection.GeneratedProtocolMessageType('FunctionCallListRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCALLLISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCallListRequest)
  })
_sym_db.RegisterMessage(FunctionCallListRequest)

FunctionCallListResponse = _reflection.GeneratedProtocolMessageType('FunctionCallListResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCALLLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCallListResponse)
  })
_sym_db.RegisterMessage(FunctionCallListResponse)

FunctionCallPutDataRequest = _reflection.GeneratedProtocolMessageType('FunctionCallPutDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCALLPUTDATAREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCallPutDataRequest)
  })
_sym_db.RegisterMessage(FunctionCallPutDataRequest)

FunctionCreateRequest = _reflection.GeneratedProtocolMessageType('FunctionCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCreateRequest)
  })
_sym_db.RegisterMessage(FunctionCreateRequest)

FunctionCreateResponse = _reflection.GeneratedProtocolMessageType('FunctionCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionCreateResponse)
  })
_sym_db.RegisterMessage(FunctionCreateResponse)

FunctionData = _reflection.GeneratedProtocolMessageType('FunctionData', (_message.Message,), {

  'MethodDefinitionsEntry' : _reflection.GeneratedProtocolMessageType('MethodDefinitionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _FUNCTIONDATA_METHODDEFINITIONSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.FunctionData.MethodDefinitionsEntry)
    })
  ,

  'RankedFunction' : _reflection.GeneratedProtocolMessageType('RankedFunction', (_message.Message,), {
    'DESCRIPTOR' : _FUNCTIONDATA_RANKEDFUNCTION,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.FunctionData.RankedFunction)
    })
  ,

  'ExperimentalOptionsEntry' : _reflection.GeneratedProtocolMessageType('ExperimentalOptionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _FUNCTIONDATA_EXPERIMENTALOPTIONSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.FunctionData.ExperimentalOptionsEntry)
    })
  ,
  'DESCRIPTOR' : _FUNCTIONDATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionData)
  })
_sym_db.RegisterMessage(FunctionData)
_sym_db.RegisterMessage(FunctionData.MethodDefinitionsEntry)
_sym_db.RegisterMessage(FunctionData.RankedFunction)
_sym_db.RegisterMessage(FunctionData.ExperimentalOptionsEntry)

FunctionExtended = _reflection.GeneratedProtocolMessageType('FunctionExtended', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONEXTENDED,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionExtended)
  })
_sym_db.RegisterMessage(FunctionExtended)

FunctionGetCallGraphRequest = _reflection.GeneratedProtocolMessageType('FunctionGetCallGraphRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETCALLGRAPHREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetCallGraphRequest)
  })
_sym_db.RegisterMessage(FunctionGetCallGraphRequest)

FunctionGetCallGraphResponse = _reflection.GeneratedProtocolMessageType('FunctionGetCallGraphResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETCALLGRAPHRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetCallGraphResponse)
  })
_sym_db.RegisterMessage(FunctionGetCallGraphResponse)

FunctionGetCurrentStatsRequest = _reflection.GeneratedProtocolMessageType('FunctionGetCurrentStatsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETCURRENTSTATSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetCurrentStatsRequest)
  })
_sym_db.RegisterMessage(FunctionGetCurrentStatsRequest)

FunctionGetDynamicConcurrencyRequest = _reflection.GeneratedProtocolMessageType('FunctionGetDynamicConcurrencyRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETDYNAMICCONCURRENCYREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetDynamicConcurrencyRequest)
  })
_sym_db.RegisterMessage(FunctionGetDynamicConcurrencyRequest)

FunctionGetDynamicConcurrencyResponse = _reflection.GeneratedProtocolMessageType('FunctionGetDynamicConcurrencyResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETDYNAMICCONCURRENCYRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetDynamicConcurrencyResponse)
  })
_sym_db.RegisterMessage(FunctionGetDynamicConcurrencyResponse)

FunctionGetInputsItem = _reflection.GeneratedProtocolMessageType('FunctionGetInputsItem', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETINPUTSITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetInputsItem)
  })
_sym_db.RegisterMessage(FunctionGetInputsItem)

FunctionGetInputsRequest = _reflection.GeneratedProtocolMessageType('FunctionGetInputsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETINPUTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetInputsRequest)
  })
_sym_db.RegisterMessage(FunctionGetInputsRequest)

FunctionGetInputsResponse = _reflection.GeneratedProtocolMessageType('FunctionGetInputsResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETINPUTSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetInputsResponse)
  })
_sym_db.RegisterMessage(FunctionGetInputsResponse)

FunctionGetOutputsItem = _reflection.GeneratedProtocolMessageType('FunctionGetOutputsItem', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETOUTPUTSITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetOutputsItem)
  })
_sym_db.RegisterMessage(FunctionGetOutputsItem)

FunctionGetOutputsRequest = _reflection.GeneratedProtocolMessageType('FunctionGetOutputsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETOUTPUTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetOutputsRequest)
  })
_sym_db.RegisterMessage(FunctionGetOutputsRequest)

FunctionGetOutputsResponse = _reflection.GeneratedProtocolMessageType('FunctionGetOutputsResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETOUTPUTSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetOutputsResponse)
  })
_sym_db.RegisterMessage(FunctionGetOutputsResponse)

FunctionGetRequest = _reflection.GeneratedProtocolMessageType('FunctionGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetRequest)
  })
_sym_db.RegisterMessage(FunctionGetRequest)

FunctionGetResponse = _reflection.GeneratedProtocolMessageType('FunctionGetResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetResponse)
  })
_sym_db.RegisterMessage(FunctionGetResponse)

FunctionGetSerializedRequest = _reflection.GeneratedProtocolMessageType('FunctionGetSerializedRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETSERIALIZEDREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetSerializedRequest)
  })
_sym_db.RegisterMessage(FunctionGetSerializedRequest)

FunctionGetSerializedResponse = _reflection.GeneratedProtocolMessageType('FunctionGetSerializedResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONGETSERIALIZEDRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionGetSerializedResponse)
  })
_sym_db.RegisterMessage(FunctionGetSerializedResponse)

FunctionHandleMetadata = _reflection.GeneratedProtocolMessageType('FunctionHandleMetadata', (_message.Message,), {

  'MethodHandleMetadataEntry' : _reflection.GeneratedProtocolMessageType('MethodHandleMetadataEntry', (_message.Message,), {
    'DESCRIPTOR' : _FUNCTIONHANDLEMETADATA_METHODHANDLEMETADATAENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.FunctionHandleMetadata.MethodHandleMetadataEntry)
    })
  ,
  'DESCRIPTOR' : _FUNCTIONHANDLEMETADATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionHandleMetadata)
  })
_sym_db.RegisterMessage(FunctionHandleMetadata)
_sym_db.RegisterMessage(FunctionHandleMetadata.MethodHandleMetadataEntry)

FunctionInput = _reflection.GeneratedProtocolMessageType('FunctionInput', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONINPUT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionInput)
  })
_sym_db.RegisterMessage(FunctionInput)

FunctionMapRequest = _reflection.GeneratedProtocolMessageType('FunctionMapRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONMAPREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionMapRequest)
  })
_sym_db.RegisterMessage(FunctionMapRequest)

FunctionMapResponse = _reflection.GeneratedProtocolMessageType('FunctionMapResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONMAPRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionMapResponse)
  })
_sym_db.RegisterMessage(FunctionMapResponse)

FunctionOptions = _reflection.GeneratedProtocolMessageType('FunctionOptions', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONOPTIONS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionOptions)
  })
_sym_db.RegisterMessage(FunctionOptions)

FunctionPrecreateRequest = _reflection.GeneratedProtocolMessageType('FunctionPrecreateRequest', (_message.Message,), {

  'MethodDefinitionsEntry' : _reflection.GeneratedProtocolMessageType('MethodDefinitionsEntry', (_message.Message,), {
    'DESCRIPTOR' : _FUNCTIONPRECREATEREQUEST_METHODDEFINITIONSENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.FunctionPrecreateRequest.MethodDefinitionsEntry)
    })
  ,
  'DESCRIPTOR' : _FUNCTIONPRECREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionPrecreateRequest)
  })
_sym_db.RegisterMessage(FunctionPrecreateRequest)
_sym_db.RegisterMessage(FunctionPrecreateRequest.MethodDefinitionsEntry)

FunctionPrecreateResponse = _reflection.GeneratedProtocolMessageType('FunctionPrecreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONPRECREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionPrecreateResponse)
  })
_sym_db.RegisterMessage(FunctionPrecreateResponse)

FunctionPutInputsItem = _reflection.GeneratedProtocolMessageType('FunctionPutInputsItem', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONPUTINPUTSITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionPutInputsItem)
  })
_sym_db.RegisterMessage(FunctionPutInputsItem)

FunctionPutInputsRequest = _reflection.GeneratedProtocolMessageType('FunctionPutInputsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONPUTINPUTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionPutInputsRequest)
  })
_sym_db.RegisterMessage(FunctionPutInputsRequest)

FunctionPutInputsResponse = _reflection.GeneratedProtocolMessageType('FunctionPutInputsResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONPUTINPUTSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionPutInputsResponse)
  })
_sym_db.RegisterMessage(FunctionPutInputsResponse)

FunctionPutInputsResponseItem = _reflection.GeneratedProtocolMessageType('FunctionPutInputsResponseItem', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONPUTINPUTSRESPONSEITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionPutInputsResponseItem)
  })
_sym_db.RegisterMessage(FunctionPutInputsResponseItem)

FunctionPutOutputsItem = _reflection.GeneratedProtocolMessageType('FunctionPutOutputsItem', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONPUTOUTPUTSITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionPutOutputsItem)
  })
_sym_db.RegisterMessage(FunctionPutOutputsItem)

FunctionPutOutputsRequest = _reflection.GeneratedProtocolMessageType('FunctionPutOutputsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONPUTOUTPUTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionPutOutputsRequest)
  })
_sym_db.RegisterMessage(FunctionPutOutputsRequest)

FunctionRetryInputsItem = _reflection.GeneratedProtocolMessageType('FunctionRetryInputsItem', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONRETRYINPUTSITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionRetryInputsItem)
  })
_sym_db.RegisterMessage(FunctionRetryInputsItem)

FunctionRetryInputsRequest = _reflection.GeneratedProtocolMessageType('FunctionRetryInputsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONRETRYINPUTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionRetryInputsRequest)
  })
_sym_db.RegisterMessage(FunctionRetryInputsRequest)

FunctionRetryInputsResponse = _reflection.GeneratedProtocolMessageType('FunctionRetryInputsResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONRETRYINPUTSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionRetryInputsResponse)
  })
_sym_db.RegisterMessage(FunctionRetryInputsResponse)

FunctionRetryPolicy = _reflection.GeneratedProtocolMessageType('FunctionRetryPolicy', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONRETRYPOLICY,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionRetryPolicy)
  })
_sym_db.RegisterMessage(FunctionRetryPolicy)

FunctionSchema = _reflection.GeneratedProtocolMessageType('FunctionSchema', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONSCHEMA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionSchema)
  })
_sym_db.RegisterMessage(FunctionSchema)

FunctionStats = _reflection.GeneratedProtocolMessageType('FunctionStats', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONSTATS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionStats)
  })
_sym_db.RegisterMessage(FunctionStats)

FunctionUpdateSchedulingParamsRequest = _reflection.GeneratedProtocolMessageType('FunctionUpdateSchedulingParamsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONUPDATESCHEDULINGPARAMSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionUpdateSchedulingParamsRequest)
  })
_sym_db.RegisterMessage(FunctionUpdateSchedulingParamsRequest)

FunctionUpdateSchedulingParamsResponse = _reflection.GeneratedProtocolMessageType('FunctionUpdateSchedulingParamsResponse', (_message.Message,), {
  'DESCRIPTOR' : _FUNCTIONUPDATESCHEDULINGPARAMSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.FunctionUpdateSchedulingParamsResponse)
  })
_sym_db.RegisterMessage(FunctionUpdateSchedulingParamsResponse)

GPUConfig = _reflection.GeneratedProtocolMessageType('GPUConfig', (_message.Message,), {
  'DESCRIPTOR' : _GPUCONFIG,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.GPUConfig)
  })
_sym_db.RegisterMessage(GPUConfig)

GeneratorDone = _reflection.GeneratedProtocolMessageType('GeneratorDone', (_message.Message,), {
  'DESCRIPTOR' : _GENERATORDONE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.GeneratorDone)
  })
_sym_db.RegisterMessage(GeneratorDone)

GenericPayloadType = _reflection.GeneratedProtocolMessageType('GenericPayloadType', (_message.Message,), {
  'DESCRIPTOR' : _GENERICPAYLOADTYPE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.GenericPayloadType)
  })
_sym_db.RegisterMessage(GenericPayloadType)

GenericResult = _reflection.GeneratedProtocolMessageType('GenericResult', (_message.Message,), {
  'DESCRIPTOR' : _GENERICRESULT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.GenericResult)
  })
_sym_db.RegisterMessage(GenericResult)

Image = _reflection.GeneratedProtocolMessageType('Image', (_message.Message,), {
  'DESCRIPTOR' : _IMAGE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Image)
  })
_sym_db.RegisterMessage(Image)

ImageContextFile = _reflection.GeneratedProtocolMessageType('ImageContextFile', (_message.Message,), {
  'DESCRIPTOR' : _IMAGECONTEXTFILE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageContextFile)
  })
_sym_db.RegisterMessage(ImageContextFile)

ImageFromIdRequest = _reflection.GeneratedProtocolMessageType('ImageFromIdRequest', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEFROMIDREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageFromIdRequest)
  })
_sym_db.RegisterMessage(ImageFromIdRequest)

ImageFromIdResponse = _reflection.GeneratedProtocolMessageType('ImageFromIdResponse', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEFROMIDRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageFromIdResponse)
  })
_sym_db.RegisterMessage(ImageFromIdResponse)

ImageGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('ImageGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageGetOrCreateRequest)
  })
_sym_db.RegisterMessage(ImageGetOrCreateRequest)

ImageGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('ImageGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageGetOrCreateResponse)
  })
_sym_db.RegisterMessage(ImageGetOrCreateResponse)

ImageJoinStreamingRequest = _reflection.GeneratedProtocolMessageType('ImageJoinStreamingRequest', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEJOINSTREAMINGREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageJoinStreamingRequest)
  })
_sym_db.RegisterMessage(ImageJoinStreamingRequest)

ImageJoinStreamingResponse = _reflection.GeneratedProtocolMessageType('ImageJoinStreamingResponse', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEJOINSTREAMINGRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageJoinStreamingResponse)
  })
_sym_db.RegisterMessage(ImageJoinStreamingResponse)

ImageMetadata = _reflection.GeneratedProtocolMessageType('ImageMetadata', (_message.Message,), {

  'PythonPackagesEntry' : _reflection.GeneratedProtocolMessageType('PythonPackagesEntry', (_message.Message,), {
    'DESCRIPTOR' : _IMAGEMETADATA_PYTHONPACKAGESENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.ImageMetadata.PythonPackagesEntry)
    })
  ,
  'DESCRIPTOR' : _IMAGEMETADATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageMetadata)
  })
_sym_db.RegisterMessage(ImageMetadata)
_sym_db.RegisterMessage(ImageMetadata.PythonPackagesEntry)

ImageRegistryConfig = _reflection.GeneratedProtocolMessageType('ImageRegistryConfig', (_message.Message,), {
  'DESCRIPTOR' : _IMAGEREGISTRYCONFIG,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ImageRegistryConfig)
  })
_sym_db.RegisterMessage(ImageRegistryConfig)

InputCallGraphInfo = _reflection.GeneratedProtocolMessageType('InputCallGraphInfo', (_message.Message,), {
  'DESCRIPTOR' : _INPUTCALLGRAPHINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.InputCallGraphInfo)
  })
_sym_db.RegisterMessage(InputCallGraphInfo)

InputCategoryInfo = _reflection.GeneratedProtocolMessageType('InputCategoryInfo', (_message.Message,), {
  'DESCRIPTOR' : _INPUTCATEGORYINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.InputCategoryInfo)
  })
_sym_db.RegisterMessage(InputCategoryInfo)

InputInfo = _reflection.GeneratedProtocolMessageType('InputInfo', (_message.Message,), {
  'DESCRIPTOR' : _INPUTINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.InputInfo)
  })
_sym_db.RegisterMessage(InputInfo)

MethodDefinition = _reflection.GeneratedProtocolMessageType('MethodDefinition', (_message.Message,), {
  'DESCRIPTOR' : _METHODDEFINITION,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.MethodDefinition)
  })
_sym_db.RegisterMessage(MethodDefinition)

MountFile = _reflection.GeneratedProtocolMessageType('MountFile', (_message.Message,), {
  'DESCRIPTOR' : _MOUNTFILE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.MountFile)
  })
_sym_db.RegisterMessage(MountFile)

MountGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('MountGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _MOUNTGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.MountGetOrCreateRequest)
  })
_sym_db.RegisterMessage(MountGetOrCreateRequest)

MountGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('MountGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _MOUNTGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.MountGetOrCreateResponse)
  })
_sym_db.RegisterMessage(MountGetOrCreateResponse)

MountHandleMetadata = _reflection.GeneratedProtocolMessageType('MountHandleMetadata', (_message.Message,), {
  'DESCRIPTOR' : _MOUNTHANDLEMETADATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.MountHandleMetadata)
  })
_sym_db.RegisterMessage(MountHandleMetadata)

MountPutFileRequest = _reflection.GeneratedProtocolMessageType('MountPutFileRequest', (_message.Message,), {
  'DESCRIPTOR' : _MOUNTPUTFILEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.MountPutFileRequest)
  })
_sym_db.RegisterMessage(MountPutFileRequest)

MountPutFileResponse = _reflection.GeneratedProtocolMessageType('MountPutFileResponse', (_message.Message,), {
  'DESCRIPTOR' : _MOUNTPUTFILERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.MountPutFileResponse)
  })
_sym_db.RegisterMessage(MountPutFileResponse)

MultiPartUpload = _reflection.GeneratedProtocolMessageType('MultiPartUpload', (_message.Message,), {
  'DESCRIPTOR' : _MULTIPARTUPLOAD,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.MultiPartUpload)
  })
_sym_db.RegisterMessage(MultiPartUpload)

NetworkAccess = _reflection.GeneratedProtocolMessageType('NetworkAccess', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKACCESS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.NetworkAccess)
  })
_sym_db.RegisterMessage(NetworkAccess)

NotebookKernelPublishResultsRequest = _reflection.GeneratedProtocolMessageType('NotebookKernelPublishResultsRequest', (_message.Message,), {

  'ExecuteReply' : _reflection.GeneratedProtocolMessageType('ExecuteReply', (_message.Message,), {
    'DESCRIPTOR' : _NOTEBOOKKERNELPUBLISHRESULTSREQUEST_EXECUTEREPLY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.NotebookKernelPublishResultsRequest.ExecuteReply)
    })
  ,

  'CellResult' : _reflection.GeneratedProtocolMessageType('CellResult', (_message.Message,), {
    'DESCRIPTOR' : _NOTEBOOKKERNELPUBLISHRESULTSREQUEST_CELLRESULT,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.NotebookKernelPublishResultsRequest.CellResult)
    })
  ,
  'DESCRIPTOR' : _NOTEBOOKKERNELPUBLISHRESULTSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.NotebookKernelPublishResultsRequest)
  })
_sym_db.RegisterMessage(NotebookKernelPublishResultsRequest)
_sym_db.RegisterMessage(NotebookKernelPublishResultsRequest.ExecuteReply)
_sym_db.RegisterMessage(NotebookKernelPublishResultsRequest.CellResult)

NotebookOutput = _reflection.GeneratedProtocolMessageType('NotebookOutput', (_message.Message,), {

  'ExecuteResult' : _reflection.GeneratedProtocolMessageType('ExecuteResult', (_message.Message,), {
    'DESCRIPTOR' : _NOTEBOOKOUTPUT_EXECUTERESULT,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.NotebookOutput.ExecuteResult)
    })
  ,

  'DisplayData' : _reflection.GeneratedProtocolMessageType('DisplayData', (_message.Message,), {
    'DESCRIPTOR' : _NOTEBOOKOUTPUT_DISPLAYDATA,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.NotebookOutput.DisplayData)
    })
  ,

  'Stream' : _reflection.GeneratedProtocolMessageType('Stream', (_message.Message,), {
    'DESCRIPTOR' : _NOTEBOOKOUTPUT_STREAM,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.NotebookOutput.Stream)
    })
  ,

  'Error' : _reflection.GeneratedProtocolMessageType('Error', (_message.Message,), {
    'DESCRIPTOR' : _NOTEBOOKOUTPUT_ERROR,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.NotebookOutput.Error)
    })
  ,
  'DESCRIPTOR' : _NOTEBOOKOUTPUT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.NotebookOutput)
  })
_sym_db.RegisterMessage(NotebookOutput)
_sym_db.RegisterMessage(NotebookOutput.ExecuteResult)
_sym_db.RegisterMessage(NotebookOutput.DisplayData)
_sym_db.RegisterMessage(NotebookOutput.Stream)
_sym_db.RegisterMessage(NotebookOutput.Error)

Object = _reflection.GeneratedProtocolMessageType('Object', (_message.Message,), {
  'DESCRIPTOR' : _OBJECT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Object)
  })
_sym_db.RegisterMessage(Object)

ObjectDependency = _reflection.GeneratedProtocolMessageType('ObjectDependency', (_message.Message,), {
  'DESCRIPTOR' : _OBJECTDEPENDENCY,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ObjectDependency)
  })
_sym_db.RegisterMessage(ObjectDependency)

PTYInfo = _reflection.GeneratedProtocolMessageType('PTYInfo', (_message.Message,), {
  'DESCRIPTOR' : _PTYINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.PTYInfo)
  })
_sym_db.RegisterMessage(PTYInfo)

PortSpec = _reflection.GeneratedProtocolMessageType('PortSpec', (_message.Message,), {
  'DESCRIPTOR' : _PORTSPEC,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.PortSpec)
  })
_sym_db.RegisterMessage(PortSpec)

PortSpecs = _reflection.GeneratedProtocolMessageType('PortSpecs', (_message.Message,), {
  'DESCRIPTOR' : _PORTSPECS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.PortSpecs)
  })
_sym_db.RegisterMessage(PortSpecs)

Proxy = _reflection.GeneratedProtocolMessageType('Proxy', (_message.Message,), {
  'DESCRIPTOR' : _PROXY,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Proxy)
  })
_sym_db.RegisterMessage(Proxy)

ProxyAddIpRequest = _reflection.GeneratedProtocolMessageType('ProxyAddIpRequest', (_message.Message,), {
  'DESCRIPTOR' : _PROXYADDIPREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyAddIpRequest)
  })
_sym_db.RegisterMessage(ProxyAddIpRequest)

ProxyAddIpResponse = _reflection.GeneratedProtocolMessageType('ProxyAddIpResponse', (_message.Message,), {
  'DESCRIPTOR' : _PROXYADDIPRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyAddIpResponse)
  })
_sym_db.RegisterMessage(ProxyAddIpResponse)

ProxyCreateRequest = _reflection.GeneratedProtocolMessageType('ProxyCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _PROXYCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyCreateRequest)
  })
_sym_db.RegisterMessage(ProxyCreateRequest)

ProxyCreateResponse = _reflection.GeneratedProtocolMessageType('ProxyCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _PROXYCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyCreateResponse)
  })
_sym_db.RegisterMessage(ProxyCreateResponse)

ProxyDeleteRequest = _reflection.GeneratedProtocolMessageType('ProxyDeleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _PROXYDELETEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyDeleteRequest)
  })
_sym_db.RegisterMessage(ProxyDeleteRequest)

ProxyGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('ProxyGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _PROXYGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyGetOrCreateRequest)
  })
_sym_db.RegisterMessage(ProxyGetOrCreateRequest)

ProxyGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('ProxyGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _PROXYGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyGetOrCreateResponse)
  })
_sym_db.RegisterMessage(ProxyGetOrCreateResponse)

ProxyGetRequest = _reflection.GeneratedProtocolMessageType('ProxyGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _PROXYGETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyGetRequest)
  })
_sym_db.RegisterMessage(ProxyGetRequest)

ProxyGetResponse = _reflection.GeneratedProtocolMessageType('ProxyGetResponse', (_message.Message,), {
  'DESCRIPTOR' : _PROXYGETRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyGetResponse)
  })
_sym_db.RegisterMessage(ProxyGetResponse)

ProxyInfo = _reflection.GeneratedProtocolMessageType('ProxyInfo', (_message.Message,), {
  'DESCRIPTOR' : _PROXYINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyInfo)
  })
_sym_db.RegisterMessage(ProxyInfo)

ProxyIp = _reflection.GeneratedProtocolMessageType('ProxyIp', (_message.Message,), {
  'DESCRIPTOR' : _PROXYIP,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyIp)
  })
_sym_db.RegisterMessage(ProxyIp)

ProxyListResponse = _reflection.GeneratedProtocolMessageType('ProxyListResponse', (_message.Message,), {
  'DESCRIPTOR' : _PROXYLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyListResponse)
  })
_sym_db.RegisterMessage(ProxyListResponse)

ProxyRemoveIpRequest = _reflection.GeneratedProtocolMessageType('ProxyRemoveIpRequest', (_message.Message,), {
  'DESCRIPTOR' : _PROXYREMOVEIPREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.ProxyRemoveIpRequest)
  })
_sym_db.RegisterMessage(ProxyRemoveIpRequest)

QueueClearRequest = _reflection.GeneratedProtocolMessageType('QueueClearRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUECLEARREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueClearRequest)
  })
_sym_db.RegisterMessage(QueueClearRequest)

QueueDeleteRequest = _reflection.GeneratedProtocolMessageType('QueueDeleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUEDELETEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueDeleteRequest)
  })
_sym_db.RegisterMessage(QueueDeleteRequest)

QueueGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('QueueGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUEGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueGetOrCreateRequest)
  })
_sym_db.RegisterMessage(QueueGetOrCreateRequest)

QueueGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('QueueGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _QUEUEGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueGetOrCreateResponse)
  })
_sym_db.RegisterMessage(QueueGetOrCreateResponse)

QueueGetRequest = _reflection.GeneratedProtocolMessageType('QueueGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUEGETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueGetRequest)
  })
_sym_db.RegisterMessage(QueueGetRequest)

QueueGetResponse = _reflection.GeneratedProtocolMessageType('QueueGetResponse', (_message.Message,), {
  'DESCRIPTOR' : _QUEUEGETRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueGetResponse)
  })
_sym_db.RegisterMessage(QueueGetResponse)

QueueHeartbeatRequest = _reflection.GeneratedProtocolMessageType('QueueHeartbeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUEHEARTBEATREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueHeartbeatRequest)
  })
_sym_db.RegisterMessage(QueueHeartbeatRequest)

QueueItem = _reflection.GeneratedProtocolMessageType('QueueItem', (_message.Message,), {
  'DESCRIPTOR' : _QUEUEITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueItem)
  })
_sym_db.RegisterMessage(QueueItem)

QueueLenRequest = _reflection.GeneratedProtocolMessageType('QueueLenRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUELENREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueLenRequest)
  })
_sym_db.RegisterMessage(QueueLenRequest)

QueueLenResponse = _reflection.GeneratedProtocolMessageType('QueueLenResponse', (_message.Message,), {
  'DESCRIPTOR' : _QUEUELENRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueLenResponse)
  })
_sym_db.RegisterMessage(QueueLenResponse)

QueueListRequest = _reflection.GeneratedProtocolMessageType('QueueListRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUELISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueListRequest)
  })
_sym_db.RegisterMessage(QueueListRequest)

QueueListResponse = _reflection.GeneratedProtocolMessageType('QueueListResponse', (_message.Message,), {

  'QueueInfo' : _reflection.GeneratedProtocolMessageType('QueueInfo', (_message.Message,), {
    'DESCRIPTOR' : _QUEUELISTRESPONSE_QUEUEINFO,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.QueueListResponse.QueueInfo)
    })
  ,
  'DESCRIPTOR' : _QUEUELISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueListResponse)
  })
_sym_db.RegisterMessage(QueueListResponse)
_sym_db.RegisterMessage(QueueListResponse.QueueInfo)

QueueNextItemsRequest = _reflection.GeneratedProtocolMessageType('QueueNextItemsRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUENEXTITEMSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueNextItemsRequest)
  })
_sym_db.RegisterMessage(QueueNextItemsRequest)

QueueNextItemsResponse = _reflection.GeneratedProtocolMessageType('QueueNextItemsResponse', (_message.Message,), {
  'DESCRIPTOR' : _QUEUENEXTITEMSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueueNextItemsResponse)
  })
_sym_db.RegisterMessage(QueueNextItemsResponse)

QueuePutRequest = _reflection.GeneratedProtocolMessageType('QueuePutRequest', (_message.Message,), {
  'DESCRIPTOR' : _QUEUEPUTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.QueuePutRequest)
  })
_sym_db.RegisterMessage(QueuePutRequest)

RateLimit = _reflection.GeneratedProtocolMessageType('RateLimit', (_message.Message,), {
  'DESCRIPTOR' : _RATELIMIT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.RateLimit)
  })
_sym_db.RegisterMessage(RateLimit)

Resources = _reflection.GeneratedProtocolMessageType('Resources', (_message.Message,), {
  'DESCRIPTOR' : _RESOURCES,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Resources)
  })
_sym_db.RegisterMessage(Resources)

RuntimeInputMessage = _reflection.GeneratedProtocolMessageType('RuntimeInputMessage', (_message.Message,), {
  'DESCRIPTOR' : _RUNTIMEINPUTMESSAGE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.RuntimeInputMessage)
  })
_sym_db.RegisterMessage(RuntimeInputMessage)

RuntimeOutputBatch = _reflection.GeneratedProtocolMessageType('RuntimeOutputBatch', (_message.Message,), {
  'DESCRIPTOR' : _RUNTIMEOUTPUTBATCH,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.RuntimeOutputBatch)
  })
_sym_db.RegisterMessage(RuntimeOutputBatch)

RuntimeOutputMessage = _reflection.GeneratedProtocolMessageType('RuntimeOutputMessage', (_message.Message,), {
  'DESCRIPTOR' : _RUNTIMEOUTPUTMESSAGE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.RuntimeOutputMessage)
  })
_sym_db.RegisterMessage(RuntimeOutputMessage)

S3Mount = _reflection.GeneratedProtocolMessageType('S3Mount', (_message.Message,), {
  'DESCRIPTOR' : _S3MOUNT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.S3Mount)
  })
_sym_db.RegisterMessage(S3Mount)

Sandbox = _reflection.GeneratedProtocolMessageType('Sandbox', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOX,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Sandbox)
  })
_sym_db.RegisterMessage(Sandbox)

SandboxCreateRequest = _reflection.GeneratedProtocolMessageType('SandboxCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxCreateRequest)
  })
_sym_db.RegisterMessage(SandboxCreateRequest)

SandboxCreateResponse = _reflection.GeneratedProtocolMessageType('SandboxCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxCreateResponse)
  })
_sym_db.RegisterMessage(SandboxCreateResponse)

SandboxGetLogsRequest = _reflection.GeneratedProtocolMessageType('SandboxGetLogsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXGETLOGSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxGetLogsRequest)
  })
_sym_db.RegisterMessage(SandboxGetLogsRequest)

SandboxGetResourceUsageRequest = _reflection.GeneratedProtocolMessageType('SandboxGetResourceUsageRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXGETRESOURCEUSAGEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxGetResourceUsageRequest)
  })
_sym_db.RegisterMessage(SandboxGetResourceUsageRequest)

SandboxGetResourceUsageResponse = _reflection.GeneratedProtocolMessageType('SandboxGetResourceUsageResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXGETRESOURCEUSAGERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxGetResourceUsageResponse)
  })
_sym_db.RegisterMessage(SandboxGetResourceUsageResponse)

SandboxGetTaskIdRequest = _reflection.GeneratedProtocolMessageType('SandboxGetTaskIdRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXGETTASKIDREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxGetTaskIdRequest)
  })
_sym_db.RegisterMessage(SandboxGetTaskIdRequest)

SandboxGetTaskIdResponse = _reflection.GeneratedProtocolMessageType('SandboxGetTaskIdResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXGETTASKIDRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxGetTaskIdResponse)
  })
_sym_db.RegisterMessage(SandboxGetTaskIdResponse)

SandboxGetTunnelsRequest = _reflection.GeneratedProtocolMessageType('SandboxGetTunnelsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXGETTUNNELSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxGetTunnelsRequest)
  })
_sym_db.RegisterMessage(SandboxGetTunnelsRequest)

SandboxGetTunnelsResponse = _reflection.GeneratedProtocolMessageType('SandboxGetTunnelsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXGETTUNNELSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxGetTunnelsResponse)
  })
_sym_db.RegisterMessage(SandboxGetTunnelsResponse)

SandboxHandleMetadata = _reflection.GeneratedProtocolMessageType('SandboxHandleMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXHANDLEMETADATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxHandleMetadata)
  })
_sym_db.RegisterMessage(SandboxHandleMetadata)

SandboxInfo = _reflection.GeneratedProtocolMessageType('SandboxInfo', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxInfo)
  })
_sym_db.RegisterMessage(SandboxInfo)

SandboxListRequest = _reflection.GeneratedProtocolMessageType('SandboxListRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXLISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxListRequest)
  })
_sym_db.RegisterMessage(SandboxListRequest)

SandboxListResponse = _reflection.GeneratedProtocolMessageType('SandboxListResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxListResponse)
  })
_sym_db.RegisterMessage(SandboxListResponse)

SandboxRestoreRequest = _reflection.GeneratedProtocolMessageType('SandboxRestoreRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXRESTOREREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxRestoreRequest)
  })
_sym_db.RegisterMessage(SandboxRestoreRequest)

SandboxRestoreResponse = _reflection.GeneratedProtocolMessageType('SandboxRestoreResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXRESTORERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxRestoreResponse)
  })
_sym_db.RegisterMessage(SandboxRestoreResponse)

SandboxSnapshotFsRequest = _reflection.GeneratedProtocolMessageType('SandboxSnapshotFsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSNAPSHOTFSREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxSnapshotFsRequest)
  })
_sym_db.RegisterMessage(SandboxSnapshotFsRequest)

SandboxSnapshotFsResponse = _reflection.GeneratedProtocolMessageType('SandboxSnapshotFsResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSNAPSHOTFSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxSnapshotFsResponse)
  })
_sym_db.RegisterMessage(SandboxSnapshotFsResponse)

SandboxSnapshotGetRequest = _reflection.GeneratedProtocolMessageType('SandboxSnapshotGetRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSNAPSHOTGETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxSnapshotGetRequest)
  })
_sym_db.RegisterMessage(SandboxSnapshotGetRequest)

SandboxSnapshotGetResponse = _reflection.GeneratedProtocolMessageType('SandboxSnapshotGetResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSNAPSHOTGETRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxSnapshotGetResponse)
  })
_sym_db.RegisterMessage(SandboxSnapshotGetResponse)

SandboxSnapshotRequest = _reflection.GeneratedProtocolMessageType('SandboxSnapshotRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSNAPSHOTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxSnapshotRequest)
  })
_sym_db.RegisterMessage(SandboxSnapshotRequest)

SandboxSnapshotResponse = _reflection.GeneratedProtocolMessageType('SandboxSnapshotResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSNAPSHOTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxSnapshotResponse)
  })
_sym_db.RegisterMessage(SandboxSnapshotResponse)

SandboxSnapshotWaitRequest = _reflection.GeneratedProtocolMessageType('SandboxSnapshotWaitRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSNAPSHOTWAITREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxSnapshotWaitRequest)
  })
_sym_db.RegisterMessage(SandboxSnapshotWaitRequest)

SandboxSnapshotWaitResponse = _reflection.GeneratedProtocolMessageType('SandboxSnapshotWaitResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSNAPSHOTWAITRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxSnapshotWaitResponse)
  })
_sym_db.RegisterMessage(SandboxSnapshotWaitResponse)

SandboxStdinWriteRequest = _reflection.GeneratedProtocolMessageType('SandboxStdinWriteRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSTDINWRITEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxStdinWriteRequest)
  })
_sym_db.RegisterMessage(SandboxStdinWriteRequest)

SandboxStdinWriteResponse = _reflection.GeneratedProtocolMessageType('SandboxStdinWriteResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXSTDINWRITERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxStdinWriteResponse)
  })
_sym_db.RegisterMessage(SandboxStdinWriteResponse)

SandboxTag = _reflection.GeneratedProtocolMessageType('SandboxTag', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXTAG,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxTag)
  })
_sym_db.RegisterMessage(SandboxTag)

SandboxTagsSetRequest = _reflection.GeneratedProtocolMessageType('SandboxTagsSetRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXTAGSSETREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxTagsSetRequest)
  })
_sym_db.RegisterMessage(SandboxTagsSetRequest)

SandboxTerminateRequest = _reflection.GeneratedProtocolMessageType('SandboxTerminateRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXTERMINATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxTerminateRequest)
  })
_sym_db.RegisterMessage(SandboxTerminateRequest)

SandboxTerminateResponse = _reflection.GeneratedProtocolMessageType('SandboxTerminateResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXTERMINATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxTerminateResponse)
  })
_sym_db.RegisterMessage(SandboxTerminateResponse)

SandboxWaitRequest = _reflection.GeneratedProtocolMessageType('SandboxWaitRequest', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXWAITREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxWaitRequest)
  })
_sym_db.RegisterMessage(SandboxWaitRequest)

SandboxWaitResponse = _reflection.GeneratedProtocolMessageType('SandboxWaitResponse', (_message.Message,), {
  'DESCRIPTOR' : _SANDBOXWAITRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SandboxWaitResponse)
  })
_sym_db.RegisterMessage(SandboxWaitResponse)

Schedule = _reflection.GeneratedProtocolMessageType('Schedule', (_message.Message,), {

  'Cron' : _reflection.GeneratedProtocolMessageType('Cron', (_message.Message,), {
    'DESCRIPTOR' : _SCHEDULE_CRON,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Schedule.Cron)
    })
  ,

  'Period' : _reflection.GeneratedProtocolMessageType('Period', (_message.Message,), {
    'DESCRIPTOR' : _SCHEDULE_PERIOD,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.Schedule.Period)
    })
  ,
  'DESCRIPTOR' : _SCHEDULE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Schedule)
  })
_sym_db.RegisterMessage(Schedule)
_sym_db.RegisterMessage(Schedule.Cron)
_sym_db.RegisterMessage(Schedule.Period)

SchedulerPlacement = _reflection.GeneratedProtocolMessageType('SchedulerPlacement', (_message.Message,), {
  'DESCRIPTOR' : _SCHEDULERPLACEMENT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SchedulerPlacement)
  })
_sym_db.RegisterMessage(SchedulerPlacement)

SecretCreateRequest = _reflection.GeneratedProtocolMessageType('SecretCreateRequest', (_message.Message,), {

  'EnvDictEntry' : _reflection.GeneratedProtocolMessageType('EnvDictEntry', (_message.Message,), {
    'DESCRIPTOR' : _SECRETCREATEREQUEST_ENVDICTENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.SecretCreateRequest.EnvDictEntry)
    })
  ,
  'DESCRIPTOR' : _SECRETCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SecretCreateRequest)
  })
_sym_db.RegisterMessage(SecretCreateRequest)
_sym_db.RegisterMessage(SecretCreateRequest.EnvDictEntry)

SecretCreateResponse = _reflection.GeneratedProtocolMessageType('SecretCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _SECRETCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SecretCreateResponse)
  })
_sym_db.RegisterMessage(SecretCreateResponse)

SecretDeleteRequest = _reflection.GeneratedProtocolMessageType('SecretDeleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _SECRETDELETEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SecretDeleteRequest)
  })
_sym_db.RegisterMessage(SecretDeleteRequest)

SecretGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('SecretGetOrCreateRequest', (_message.Message,), {

  'EnvDictEntry' : _reflection.GeneratedProtocolMessageType('EnvDictEntry', (_message.Message,), {
    'DESCRIPTOR' : _SECRETGETORCREATEREQUEST_ENVDICTENTRY,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.SecretGetOrCreateRequest.EnvDictEntry)
    })
  ,
  'DESCRIPTOR' : _SECRETGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SecretGetOrCreateRequest)
  })
_sym_db.RegisterMessage(SecretGetOrCreateRequest)
_sym_db.RegisterMessage(SecretGetOrCreateRequest.EnvDictEntry)

SecretGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('SecretGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _SECRETGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SecretGetOrCreateResponse)
  })
_sym_db.RegisterMessage(SecretGetOrCreateResponse)

SecretListItem = _reflection.GeneratedProtocolMessageType('SecretListItem', (_message.Message,), {
  'DESCRIPTOR' : _SECRETLISTITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SecretListItem)
  })
_sym_db.RegisterMessage(SecretListItem)

SecretListRequest = _reflection.GeneratedProtocolMessageType('SecretListRequest', (_message.Message,), {
  'DESCRIPTOR' : _SECRETLISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SecretListRequest)
  })
_sym_db.RegisterMessage(SecretListRequest)

SecretListResponse = _reflection.GeneratedProtocolMessageType('SecretListResponse', (_message.Message,), {
  'DESCRIPTOR' : _SECRETLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SecretListResponse)
  })
_sym_db.RegisterMessage(SecretListResponse)

SharedVolumeDeleteRequest = _reflection.GeneratedProtocolMessageType('SharedVolumeDeleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEDELETEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeDeleteRequest)
  })
_sym_db.RegisterMessage(SharedVolumeDeleteRequest)

SharedVolumeGetFileRequest = _reflection.GeneratedProtocolMessageType('SharedVolumeGetFileRequest', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEGETFILEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeGetFileRequest)
  })
_sym_db.RegisterMessage(SharedVolumeGetFileRequest)

SharedVolumeGetFileResponse = _reflection.GeneratedProtocolMessageType('SharedVolumeGetFileResponse', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEGETFILERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeGetFileResponse)
  })
_sym_db.RegisterMessage(SharedVolumeGetFileResponse)

SharedVolumeGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('SharedVolumeGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeGetOrCreateRequest)
  })
_sym_db.RegisterMessage(SharedVolumeGetOrCreateRequest)

SharedVolumeGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('SharedVolumeGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeGetOrCreateResponse)
  })
_sym_db.RegisterMessage(SharedVolumeGetOrCreateResponse)

SharedVolumeHeartbeatRequest = _reflection.GeneratedProtocolMessageType('SharedVolumeHeartbeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEHEARTBEATREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeHeartbeatRequest)
  })
_sym_db.RegisterMessage(SharedVolumeHeartbeatRequest)

SharedVolumeListFilesRequest = _reflection.GeneratedProtocolMessageType('SharedVolumeListFilesRequest', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMELISTFILESREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeListFilesRequest)
  })
_sym_db.RegisterMessage(SharedVolumeListFilesRequest)

SharedVolumeListFilesResponse = _reflection.GeneratedProtocolMessageType('SharedVolumeListFilesResponse', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMELISTFILESRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeListFilesResponse)
  })
_sym_db.RegisterMessage(SharedVolumeListFilesResponse)

SharedVolumeListItem = _reflection.GeneratedProtocolMessageType('SharedVolumeListItem', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMELISTITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeListItem)
  })
_sym_db.RegisterMessage(SharedVolumeListItem)

SharedVolumeListRequest = _reflection.GeneratedProtocolMessageType('SharedVolumeListRequest', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMELISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeListRequest)
  })
_sym_db.RegisterMessage(SharedVolumeListRequest)

SharedVolumeListResponse = _reflection.GeneratedProtocolMessageType('SharedVolumeListResponse', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMELISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeListResponse)
  })
_sym_db.RegisterMessage(SharedVolumeListResponse)

SharedVolumeMount = _reflection.GeneratedProtocolMessageType('SharedVolumeMount', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEMOUNT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeMount)
  })
_sym_db.RegisterMessage(SharedVolumeMount)

SharedVolumePutFileRequest = _reflection.GeneratedProtocolMessageType('SharedVolumePutFileRequest', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEPUTFILEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumePutFileRequest)
  })
_sym_db.RegisterMessage(SharedVolumePutFileRequest)

SharedVolumePutFileResponse = _reflection.GeneratedProtocolMessageType('SharedVolumePutFileResponse', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEPUTFILERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumePutFileResponse)
  })
_sym_db.RegisterMessage(SharedVolumePutFileResponse)

SharedVolumeRemoveFileRequest = _reflection.GeneratedProtocolMessageType('SharedVolumeRemoveFileRequest', (_message.Message,), {
  'DESCRIPTOR' : _SHAREDVOLUMEREMOVEFILEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SharedVolumeRemoveFileRequest)
  })
_sym_db.RegisterMessage(SharedVolumeRemoveFileRequest)

SystemErrorMessage = _reflection.GeneratedProtocolMessageType('SystemErrorMessage', (_message.Message,), {
  'DESCRIPTOR' : _SYSTEMERRORMESSAGE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.SystemErrorMessage)
  })
_sym_db.RegisterMessage(SystemErrorMessage)

TaskClusterHelloRequest = _reflection.GeneratedProtocolMessageType('TaskClusterHelloRequest', (_message.Message,), {
  'DESCRIPTOR' : _TASKCLUSTERHELLOREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskClusterHelloRequest)
  })
_sym_db.RegisterMessage(TaskClusterHelloRequest)

TaskClusterHelloResponse = _reflection.GeneratedProtocolMessageType('TaskClusterHelloResponse', (_message.Message,), {
  'DESCRIPTOR' : _TASKCLUSTERHELLORESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskClusterHelloResponse)
  })
_sym_db.RegisterMessage(TaskClusterHelloResponse)

TaskCurrentInputsResponse = _reflection.GeneratedProtocolMessageType('TaskCurrentInputsResponse', (_message.Message,), {
  'DESCRIPTOR' : _TASKCURRENTINPUTSRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskCurrentInputsResponse)
  })
_sym_db.RegisterMessage(TaskCurrentInputsResponse)

TaskInfo = _reflection.GeneratedProtocolMessageType('TaskInfo', (_message.Message,), {
  'DESCRIPTOR' : _TASKINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskInfo)
  })
_sym_db.RegisterMessage(TaskInfo)

TaskListRequest = _reflection.GeneratedProtocolMessageType('TaskListRequest', (_message.Message,), {
  'DESCRIPTOR' : _TASKLISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskListRequest)
  })
_sym_db.RegisterMessage(TaskListRequest)

TaskListResponse = _reflection.GeneratedProtocolMessageType('TaskListResponse', (_message.Message,), {
  'DESCRIPTOR' : _TASKLISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskListResponse)
  })
_sym_db.RegisterMessage(TaskListResponse)

TaskLogs = _reflection.GeneratedProtocolMessageType('TaskLogs', (_message.Message,), {
  'DESCRIPTOR' : _TASKLOGS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskLogs)
  })
_sym_db.RegisterMessage(TaskLogs)

TaskLogsBatch = _reflection.GeneratedProtocolMessageType('TaskLogsBatch', (_message.Message,), {
  'DESCRIPTOR' : _TASKLOGSBATCH,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskLogsBatch)
  })
_sym_db.RegisterMessage(TaskLogsBatch)

TaskProgress = _reflection.GeneratedProtocolMessageType('TaskProgress', (_message.Message,), {
  'DESCRIPTOR' : _TASKPROGRESS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskProgress)
  })
_sym_db.RegisterMessage(TaskProgress)

TaskResultRequest = _reflection.GeneratedProtocolMessageType('TaskResultRequest', (_message.Message,), {
  'DESCRIPTOR' : _TASKRESULTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskResultRequest)
  })
_sym_db.RegisterMessage(TaskResultRequest)

TaskStats = _reflection.GeneratedProtocolMessageType('TaskStats', (_message.Message,), {
  'DESCRIPTOR' : _TASKSTATS,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskStats)
  })
_sym_db.RegisterMessage(TaskStats)

TaskTemplate = _reflection.GeneratedProtocolMessageType('TaskTemplate', (_message.Message,), {
  'DESCRIPTOR' : _TASKTEMPLATE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TaskTemplate)
  })
_sym_db.RegisterMessage(TaskTemplate)

TokenFlowCreateRequest = _reflection.GeneratedProtocolMessageType('TokenFlowCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _TOKENFLOWCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TokenFlowCreateRequest)
  })
_sym_db.RegisterMessage(TokenFlowCreateRequest)

TokenFlowCreateResponse = _reflection.GeneratedProtocolMessageType('TokenFlowCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _TOKENFLOWCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TokenFlowCreateResponse)
  })
_sym_db.RegisterMessage(TokenFlowCreateResponse)

TokenFlowWaitRequest = _reflection.GeneratedProtocolMessageType('TokenFlowWaitRequest', (_message.Message,), {
  'DESCRIPTOR' : _TOKENFLOWWAITREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TokenFlowWaitRequest)
  })
_sym_db.RegisterMessage(TokenFlowWaitRequest)

TokenFlowWaitResponse = _reflection.GeneratedProtocolMessageType('TokenFlowWaitResponse', (_message.Message,), {
  'DESCRIPTOR' : _TOKENFLOWWAITRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TokenFlowWaitResponse)
  })
_sym_db.RegisterMessage(TokenFlowWaitResponse)

TunnelData = _reflection.GeneratedProtocolMessageType('TunnelData', (_message.Message,), {
  'DESCRIPTOR' : _TUNNELDATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TunnelData)
  })
_sym_db.RegisterMessage(TunnelData)

TunnelStartRequest = _reflection.GeneratedProtocolMessageType('TunnelStartRequest', (_message.Message,), {
  'DESCRIPTOR' : _TUNNELSTARTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TunnelStartRequest)
  })
_sym_db.RegisterMessage(TunnelStartRequest)

TunnelStartResponse = _reflection.GeneratedProtocolMessageType('TunnelStartResponse', (_message.Message,), {
  'DESCRIPTOR' : _TUNNELSTARTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TunnelStartResponse)
  })
_sym_db.RegisterMessage(TunnelStartResponse)

TunnelStopRequest = _reflection.GeneratedProtocolMessageType('TunnelStopRequest', (_message.Message,), {
  'DESCRIPTOR' : _TUNNELSTOPREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TunnelStopRequest)
  })
_sym_db.RegisterMessage(TunnelStopRequest)

TunnelStopResponse = _reflection.GeneratedProtocolMessageType('TunnelStopResponse', (_message.Message,), {
  'DESCRIPTOR' : _TUNNELSTOPRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.TunnelStopResponse)
  })
_sym_db.RegisterMessage(TunnelStopResponse)

VolumeCommitRequest = _reflection.GeneratedProtocolMessageType('VolumeCommitRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMECOMMITREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeCommitRequest)
  })
_sym_db.RegisterMessage(VolumeCommitRequest)

VolumeCommitResponse = _reflection.GeneratedProtocolMessageType('VolumeCommitResponse', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMECOMMITRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeCommitResponse)
  })
_sym_db.RegisterMessage(VolumeCommitResponse)

VolumeCopyFiles2Request = _reflection.GeneratedProtocolMessageType('VolumeCopyFiles2Request', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMECOPYFILES2REQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeCopyFiles2Request)
  })
_sym_db.RegisterMessage(VolumeCopyFiles2Request)

VolumeCopyFilesRequest = _reflection.GeneratedProtocolMessageType('VolumeCopyFilesRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMECOPYFILESREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeCopyFilesRequest)
  })
_sym_db.RegisterMessage(VolumeCopyFilesRequest)

VolumeDeleteRequest = _reflection.GeneratedProtocolMessageType('VolumeDeleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEDELETEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeDeleteRequest)
  })
_sym_db.RegisterMessage(VolumeDeleteRequest)

VolumeGetFile2Request = _reflection.GeneratedProtocolMessageType('VolumeGetFile2Request', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEGETFILE2REQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeGetFile2Request)
  })
_sym_db.RegisterMessage(VolumeGetFile2Request)

VolumeGetFile2Response = _reflection.GeneratedProtocolMessageType('VolumeGetFile2Response', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEGETFILE2RESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeGetFile2Response)
  })
_sym_db.RegisterMessage(VolumeGetFile2Response)

VolumeGetFileRequest = _reflection.GeneratedProtocolMessageType('VolumeGetFileRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEGETFILEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeGetFileRequest)
  })
_sym_db.RegisterMessage(VolumeGetFileRequest)

VolumeGetFileResponse = _reflection.GeneratedProtocolMessageType('VolumeGetFileResponse', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEGETFILERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeGetFileResponse)
  })
_sym_db.RegisterMessage(VolumeGetFileResponse)

VolumeGetOrCreateRequest = _reflection.GeneratedProtocolMessageType('VolumeGetOrCreateRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEGETORCREATEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeGetOrCreateRequest)
  })
_sym_db.RegisterMessage(VolumeGetOrCreateRequest)

VolumeGetOrCreateResponse = _reflection.GeneratedProtocolMessageType('VolumeGetOrCreateResponse', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEGETORCREATERESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeGetOrCreateResponse)
  })
_sym_db.RegisterMessage(VolumeGetOrCreateResponse)

VolumeHeartbeatRequest = _reflection.GeneratedProtocolMessageType('VolumeHeartbeatRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEHEARTBEATREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeHeartbeatRequest)
  })
_sym_db.RegisterMessage(VolumeHeartbeatRequest)

VolumeListFiles2Request = _reflection.GeneratedProtocolMessageType('VolumeListFiles2Request', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMELISTFILES2REQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeListFiles2Request)
  })
_sym_db.RegisterMessage(VolumeListFiles2Request)

VolumeListFiles2Response = _reflection.GeneratedProtocolMessageType('VolumeListFiles2Response', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMELISTFILES2RESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeListFiles2Response)
  })
_sym_db.RegisterMessage(VolumeListFiles2Response)

VolumeListFilesRequest = _reflection.GeneratedProtocolMessageType('VolumeListFilesRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMELISTFILESREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeListFilesRequest)
  })
_sym_db.RegisterMessage(VolumeListFilesRequest)

VolumeListFilesResponse = _reflection.GeneratedProtocolMessageType('VolumeListFilesResponse', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMELISTFILESRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeListFilesResponse)
  })
_sym_db.RegisterMessage(VolumeListFilesResponse)

VolumeListItem = _reflection.GeneratedProtocolMessageType('VolumeListItem', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMELISTITEM,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeListItem)
  })
_sym_db.RegisterMessage(VolumeListItem)

VolumeListRequest = _reflection.GeneratedProtocolMessageType('VolumeListRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMELISTREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeListRequest)
  })
_sym_db.RegisterMessage(VolumeListRequest)

VolumeListResponse = _reflection.GeneratedProtocolMessageType('VolumeListResponse', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMELISTRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeListResponse)
  })
_sym_db.RegisterMessage(VolumeListResponse)

VolumeMetadata = _reflection.GeneratedProtocolMessageType('VolumeMetadata', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEMETADATA,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeMetadata)
  })
_sym_db.RegisterMessage(VolumeMetadata)

VolumeMount = _reflection.GeneratedProtocolMessageType('VolumeMount', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEMOUNT,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeMount)
  })
_sym_db.RegisterMessage(VolumeMount)

VolumePutFiles2Request = _reflection.GeneratedProtocolMessageType('VolumePutFiles2Request', (_message.Message,), {

  'File' : _reflection.GeneratedProtocolMessageType('File', (_message.Message,), {
    'DESCRIPTOR' : _VOLUMEPUTFILES2REQUEST_FILE,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.VolumePutFiles2Request.File)
    })
  ,

  'Block' : _reflection.GeneratedProtocolMessageType('Block', (_message.Message,), {
    'DESCRIPTOR' : _VOLUMEPUTFILES2REQUEST_BLOCK,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.VolumePutFiles2Request.Block)
    })
  ,
  'DESCRIPTOR' : _VOLUMEPUTFILES2REQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumePutFiles2Request)
  })
_sym_db.RegisterMessage(VolumePutFiles2Request)
_sym_db.RegisterMessage(VolumePutFiles2Request.File)
_sym_db.RegisterMessage(VolumePutFiles2Request.Block)

VolumePutFiles2Response = _reflection.GeneratedProtocolMessageType('VolumePutFiles2Response', (_message.Message,), {

  'MissingBlock' : _reflection.GeneratedProtocolMessageType('MissingBlock', (_message.Message,), {
    'DESCRIPTOR' : _VOLUMEPUTFILES2RESPONSE_MISSINGBLOCK,
    '__module__' : 'modal_proto.api_pb2'
    # @@protoc_insertion_point(class_scope:modal.client.VolumePutFiles2Response.MissingBlock)
    })
  ,
  'DESCRIPTOR' : _VOLUMEPUTFILES2RESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumePutFiles2Response)
  })
_sym_db.RegisterMessage(VolumePutFiles2Response)
_sym_db.RegisterMessage(VolumePutFiles2Response.MissingBlock)

VolumePutFilesRequest = _reflection.GeneratedProtocolMessageType('VolumePutFilesRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEPUTFILESREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumePutFilesRequest)
  })
_sym_db.RegisterMessage(VolumePutFilesRequest)

VolumeReloadRequest = _reflection.GeneratedProtocolMessageType('VolumeReloadRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMERELOADREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeReloadRequest)
  })
_sym_db.RegisterMessage(VolumeReloadRequest)

VolumeRemoveFile2Request = _reflection.GeneratedProtocolMessageType('VolumeRemoveFile2Request', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEREMOVEFILE2REQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeRemoveFile2Request)
  })
_sym_db.RegisterMessage(VolumeRemoveFile2Request)

VolumeRemoveFileRequest = _reflection.GeneratedProtocolMessageType('VolumeRemoveFileRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEREMOVEFILEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeRemoveFileRequest)
  })
_sym_db.RegisterMessage(VolumeRemoveFileRequest)

VolumeRenameRequest = _reflection.GeneratedProtocolMessageType('VolumeRenameRequest', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMERENAMEREQUEST,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.VolumeRenameRequest)
  })
_sym_db.RegisterMessage(VolumeRenameRequest)

Warning = _reflection.GeneratedProtocolMessageType('Warning', (_message.Message,), {
  'DESCRIPTOR' : _WARNING,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.Warning)
  })
_sym_db.RegisterMessage(Warning)

WebUrlInfo = _reflection.GeneratedProtocolMessageType('WebUrlInfo', (_message.Message,), {
  'DESCRIPTOR' : _WEBURLINFO,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.WebUrlInfo)
  })
_sym_db.RegisterMessage(WebUrlInfo)

WebhookConfig = _reflection.GeneratedProtocolMessageType('WebhookConfig', (_message.Message,), {
  'DESCRIPTOR' : _WEBHOOKCONFIG,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.WebhookConfig)
  })
_sym_db.RegisterMessage(WebhookConfig)

WorkspaceNameLookupResponse = _reflection.GeneratedProtocolMessageType('WorkspaceNameLookupResponse', (_message.Message,), {
  'DESCRIPTOR' : _WORKSPACENAMELOOKUPRESPONSE,
  '__module__' : 'modal_proto.api_pb2'
  # @@protoc_insertion_point(class_scope:modal.client.WorkspaceNameLookupResponse)
  })
_sym_db.RegisterMessage(WorkspaceNameLookupResponse)

_MODALCLIENT = DESCRIPTOR.services_by_name['ModalClient']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z$github.com/modal-labs/modal/go/proto'
  _APPSTATE.values_by_name["APP_STATE_DERIVED"]._options = None
  _APPSTATE.values_by_name["APP_STATE_DERIVED"]._serialized_options = b'\010\001'
  _APPCREATEREQUEST.fields_by_name['client_id']._options = None
  _APPCREATEREQUEST.fields_by_name['client_id']._serialized_options = b'\200\265\030\001'
  _APPDEPLOYREQUEST.fields_by_name['app_id']._options = None
  _APPDEPLOYREQUEST.fields_by_name['app_id']._serialized_options = b'\200\265\030\001'
  _APPLAYOUT_FUNCTIONIDSENTRY._options = None
  _APPLAYOUT_FUNCTIONIDSENTRY._serialized_options = b'8\001'
  _APPLAYOUT_CLASSIDSENTRY._options = None
  _APPLAYOUT_CLASSIDSENTRY._serialized_options = b'8\001'
  _APPPUBLISHREQUEST_FUNCTIONIDSENTRY._options = None
  _APPPUBLISHREQUEST_FUNCTIONIDSENTRY._serialized_options = b'8\001'
  _APPPUBLISHREQUEST_CLASSIDSENTRY._options = None
  _APPPUBLISHREQUEST_CLASSIDSENTRY._serialized_options = b'8\001'
  _APPPUBLISHREQUEST_DEFINITIONIDSENTRY._options = None
  _APPPUBLISHREQUEST_DEFINITIONIDSENTRY._serialized_options = b'8\001'
  _APPPUBLISHREQUEST.fields_by_name['app_id']._options = None
  _APPPUBLISHREQUEST.fields_by_name['app_id']._serialized_options = b'\200\265\030\001'
  _APPSETOBJECTSREQUEST_INDEXEDOBJECTIDSENTRY._options = None
  _APPSETOBJECTSREQUEST_INDEXEDOBJECTIDSENTRY._serialized_options = b'8\001'
  _APPSTOPREQUEST.fields_by_name['app_id']._options = None
  _APPSTOPREQUEST.fields_by_name['app_id']._serialized_options = b'\200\265\030\001'
  _CLASSCREATEREQUEST.fields_by_name['app_id']._options = None
  _CLASSCREATEREQUEST.fields_by_name['app_id']._serialized_options = b'\200\265\030\001'
  _CONTAINERARGUMENTS_TRACINGCONTEXTENTRY._options = None
  _CONTAINERARGUMENTS_TRACINGCONTEXTENTRY._serialized_options = b'8\001'
  _CONTAINEREXECREQUEST.fields_by_name['terminate_container_on_exit']._options = None
  _CONTAINEREXECREQUEST.fields_by_name['terminate_container_on_exit']._serialized_options = b'\030\001'
  _CONTAINERSTOPREQUEST.fields_by_name['task_id']._options = None
  _CONTAINERSTOPREQUEST.fields_by_name['task_id']._serialized_options = b'\200\265\030\001'
  _DICTGETORCREATEREQUEST.fields_by_name['deployment_name']._options = None
  _DICTGETORCREATEREQUEST.fields_by_name['deployment_name']._serialized_options = b'\200\265\030\001'
  _DICTUPDATEREQUEST.fields_by_name['dict_id']._options = None
  _DICTUPDATEREQUEST.fields_by_name['dict_id']._serialized_options = b'\200\265\030\001'
  _DOMAINCREATEREQUEST.fields_by_name['domain_name']._options = None
  _DOMAINCREATEREQUEST.fields_by_name['domain_name']._serialized_options = b'\200\265\030\001'
  _ENVIRONMENTCREATEREQUEST.fields_by_name['name']._options = None
  _ENVIRONMENTCREATEREQUEST.fields_by_name['name']._serialized_options = b'\200\265\030\001'
  _ENVIRONMENTDELETEREQUEST.fields_by_name['name']._options = None
  _ENVIRONMENTDELETEREQUEST.fields_by_name['name']._serialized_options = b'\200\265\030\001'
  _ENVIRONMENTGETORCREATEREQUEST.fields_by_name['deployment_name']._options = None
  _ENVIRONMENTGETORCREATEREQUEST.fields_by_name['deployment_name']._serialized_options = b'\200\265\030\001'
  _ENVIRONMENTUPDATEREQUEST.fields_by_name['current_name']._options = None
  _ENVIRONMENTUPDATEREQUEST.fields_by_name['current_name']._serialized_options = b'\200\265\030\001'
  _FUNCTION_METHODDEFINITIONSENTRY._options = None
  _FUNCTION_METHODDEFINITIONSENTRY._serialized_options = b'8\001'
  _FUNCTION_EXPERIMENTALOPTIONSENTRY._options = None
  _FUNCTION_EXPERIMENTALOPTIONSENTRY._serialized_options = b'8\001'
  _FUNCTIONCREATEREQUEST.fields_by_name['app_id']._options = None
  _FUNCTIONCREATEREQUEST.fields_by_name['app_id']._serialized_options = b'\200\265\030\001'
  _FUNCTIONCREATEREQUEST.fields_by_name['schedule']._options = None
  _FUNCTIONCREATEREQUEST.fields_by_name['schedule']._serialized_options = b'\030\001'
  _FUNCTIONCREATERESPONSE.fields_by_name['__deprecated_web_url']._options = None
  _FUNCTIONCREATERESPONSE.fields_by_name['__deprecated_web_url']._serialized_options = b'\030\001'
  _FUNCTIONDATA_METHODDEFINITIONSENTRY._options = None
  _FUNCTIONDATA_METHODDEFINITIONSENTRY._serialized_options = b'8\001'
  _FUNCTIONDATA_EXPERIMENTALOPTIONSENTRY._options = None
  _FUNCTIONDATA_EXPERIMENTALOPTIONSENTRY._serialized_options = b'8\001'
  _FUNCTIONHANDLEMETADATA_METHODHANDLEMETADATAENTRY._options = None
  _FUNCTIONHANDLEMETADATA_METHODHANDLEMETADATAENTRY._serialized_options = b'8\001'
  _FUNCTIONPRECREATEREQUEST_METHODDEFINITIONSENTRY._options = None
  _FUNCTIONPRECREATEREQUEST_METHODDEFINITIONSENTRY._serialized_options = b'8\001'
  _FUNCTIONPRECREATEREQUEST.fields_by_name['function_name']._options = None
  _FUNCTIONPRECREATEREQUEST.fields_by_name['function_name']._serialized_options = b'\200\265\030\001'
  _IMAGEGETORCREATEREQUEST.fields_by_name['app_id']._options = None
  _IMAGEGETORCREATEREQUEST.fields_by_name['app_id']._serialized_options = b'\200\265\030\001'
  _IMAGEMETADATA_PYTHONPACKAGESENTRY._options = None
  _IMAGEMETADATA_PYTHONPACKAGESENTRY._serialized_options = b'8\001'
  _PROXYADDIPREQUEST.fields_by_name['proxy_id']._options = None
  _PROXYADDIPREQUEST.fields_by_name['proxy_id']._serialized_options = b'\200\265\030\001'
  _PROXYCREATEREQUEST.fields_by_name['name']._options = None
  _PROXYCREATEREQUEST.fields_by_name['name']._serialized_options = b'\200\265\030\001'
  _PROXYDELETEREQUEST.fields_by_name['proxy_id']._options = None
  _PROXYDELETEREQUEST.fields_by_name['proxy_id']._serialized_options = b'\200\265\030\001'
  _PROXYGETORCREATEREQUEST.fields_by_name['deployment_name']._options = None
  _PROXYGETORCREATEREQUEST.fields_by_name['deployment_name']._serialized_options = b'\200\265\030\001'
  _PROXYGETREQUEST.fields_by_name['name']._options = None
  _PROXYGETREQUEST.fields_by_name['name']._serialized_options = b'\200\265\030\001'
  _PROXYREMOVEIPREQUEST.fields_by_name['proxy_ip']._options = None
  _PROXYREMOVEIPREQUEST.fields_by_name['proxy_ip']._serialized_options = b'\200\265\030\001'
  _QUEUEDELETEREQUEST.fields_by_name['queue_id']._options = None
  _QUEUEDELETEREQUEST.fields_by_name['queue_id']._serialized_options = b'\200\265\030\001'
  _SANDBOXCREATEREQUEST.fields_by_name['app_id']._options = None
  _SANDBOXCREATEREQUEST.fields_by_name['app_id']._serialized_options = b'\200\265\030\001'
  _SECRETCREATEREQUEST_ENVDICTENTRY._options = None
  _SECRETCREATEREQUEST_ENVDICTENTRY._serialized_options = b'8\001'
  _SECRETCREATEREQUEST.fields_by_name['app_id']._options = None
  _SECRETCREATEREQUEST.fields_by_name['app_id']._serialized_options = b'\200\265\030\001'
  _SECRETGETORCREATEREQUEST_ENVDICTENTRY._options = None
  _SECRETGETORCREATEREQUEST_ENVDICTENTRY._serialized_options = b'8\001'
  _SECRETGETORCREATEREQUEST.fields_by_name['deployment_name']._options = None
  _SECRETGETORCREATEREQUEST.fields_by_name['deployment_name']._serialized_options = b'\200\265\030\001'
  _SHAREDVOLUMEGETORCREATEREQUEST.fields_by_name['deployment_name']._options = None
  _SHAREDVOLUMEGETORCREATEREQUEST.fields_by_name['deployment_name']._serialized_options = b'\200\265\030\001'
  _SHAREDVOLUMEPUTFILEREQUEST.fields_by_name['shared_volume_id']._options = None
  _SHAREDVOLUMEPUTFILEREQUEST.fields_by_name['shared_volume_id']._serialized_options = b'\200\265\030\001'
  _SHAREDVOLUMEREMOVEFILEREQUEST.fields_by_name['shared_volume_id']._options = None
  _SHAREDVOLUMEREMOVEFILEREQUEST.fields_by_name['shared_volume_id']._serialized_options = b'\200\265\030\001'
  _VOLUMECOMMITREQUEST.fields_by_name['volume_id']._options = None
  _VOLUMECOMMITREQUEST.fields_by_name['volume_id']._serialized_options = b'\200\265\030\001'
  _VOLUMEDELETEREQUEST.fields_by_name['environment_name']._options = None
  _VOLUMEDELETEREQUEST.fields_by_name['environment_name']._serialized_options = b'\030\001'
  _VOLUMEGETORCREATEREQUEST.fields_by_name['deployment_name']._options = None
  _VOLUMEGETORCREATEREQUEST.fields_by_name['deployment_name']._serialized_options = b'\200\265\030\001'
  _VOLUMEREMOVEFILE2REQUEST.fields_by_name['volume_id']._options = None
  _VOLUMEREMOVEFILE2REQUEST.fields_by_name['volume_id']._serialized_options = b'\200\265\030\001'
  _VOLUMEREMOVEFILEREQUEST.fields_by_name['volume_id']._options = None
  _VOLUMEREMOVEFILEREQUEST.fields_by_name['volume_id']._serialized_options = b'\200\265\030\001'
  _VOLUMERENAMEREQUEST.fields_by_name['volume_id']._options = None
  _VOLUMERENAMEREQUEST.fields_by_name['volume_id']._serialized_options = b'\200\265\030\001'
  _WEBURLINFO.fields_by_name['has_unique_hash']._options = None
  _WEBURLINFO.fields_by_name['has_unique_hash']._serialized_options = b'\030\001'
  _WORKSPACENAMELOOKUPRESPONSE.fields_by_name['workspace_name']._options = None
  _WORKSPACENAMELOOKUPRESPONSE.fields_by_name['workspace_name']._serialized_options = b'\030\001'
  _APPDEPLOYVISIBILITY._serialized_start=52404
  _APPDEPLOYVISIBILITY._serialized_end=52535
  _APPDISCONNECTREASON._serialized_start=52538
  _APPDISCONNECTREASON._serialized_end=52827
  _APPSTATE._serialized_start=52830
  _APPSTATE._serialized_end=53103
  _APPSTOPSOURCE._serialized_start=53106
  _APPSTOPSOURCE._serialized_end=53239
  _CERTIFICATESTATUS._serialized_start=53242
  _CERTIFICATESTATUS._serialized_end=53387
  _CHECKPOINTSTATUS._serialized_start=53390
  _CHECKPOINTSTATUS._serialized_end=53567
  _CLIENTTYPE._serialized_start=53570
  _CLIENTTYPE._serialized_end=53773
  _CLOUDPROVIDER._serialized_start=53776
  _CLOUDPROVIDER._serialized_end=53944
  _DNSRECORDTYPE._serialized_start=53946
  _DNSRECORDTYPE._serialized_end=54036
  _DATAFORMAT._serialized_start=54038
  _DATAFORMAT._serialized_end=54157
  _DEPLOYMENTNAMESPACE._serialized_start=54160
  _DEPLOYMENTNAMESPACE._serialized_end=54288
  _EXECOUTPUTOPTION._serialized_start=54291
  _EXECOUTPUTOPTION._serialized_end=54437
  _FILEDESCRIPTOR._serialized_start=54440
  _FILEDESCRIPTOR._serialized_end=54571
  _FUNCTIONCALLINVOCATIONTYPE._serialized_start=54574
  _FUNCTIONCALLINVOCATIONTYPE._serialized_end=54825
  _FUNCTIONCALLTYPE._serialized_start=54827
  _FUNCTIONCALLTYPE._serialized_end=54939
  _GPUTYPE._serialized_start=54942
  _GPUTYPE._serialized_end=55148
  _OBJECTCREATIONTYPE._serialized_start=55151
  _OBJECTCREATIONTYPE._serialized_end=55439
  _PARAMETERTYPE._serialized_start=55442
  _PARAMETERTYPE._serialized_end=55681
  _PROGRESSTYPE._serialized_start=55683
  _PROGRESSTYPE._serialized_end=55745
  _PROXYIPSTATUS._serialized_start=55748
  _PROXYIPSTATUS._serialized_end=55917
  _PROXYTYPE._serialized_start=55919
  _PROXYTYPE._serialized_end=56003
  _RATELIMITINTERVAL._serialized_start=56005
  _RATELIMITINTERVAL._serialized_end=56125
  _REGISTRYAUTHTYPE._serialized_start=56128
  _REGISTRYAUTHTYPE._serialized_end=56306
  _SEEKWHENCE._serialized_start=56308
  _SEEKWHENCE._serialized_end=56362
  _SYSTEMERRORCODE._serialized_start=56365
  _SYSTEMERRORCODE._serialized_end=56789
  _TASKSTATE._serialized_start=56792
  _TASKSTATE._serialized_end=57140
  _TUNNELTYPE._serialized_start=57142
  _TUNNELTYPE._serialized_end=57203
  _VOLUMEFSVERSION._serialized_start=57205
  _VOLUMEFSVERSION._serialized_end=57309
  _WEBHOOKASYNCMODE._serialized_start=57312
  _WEBHOOKASYNCMODE._serialized_end=57466
  _WEBHOOKTYPE._serialized_start=57469
  _WEBHOOKTYPE._serialized_end=57622
  _APPCLIENTDISCONNECTREQUEST._serialized_start=157
  _APPCLIENTDISCONNECTREQUEST._serialized_end=271
  _APPCREATEREQUEST._serialized_start=274
  _APPCREATEREQUEST._serialized_end=407
  _APPCREATERESPONSE._serialized_start=409
  _APPCREATERESPONSE._serialized_end=488
  _APPDEPLOYREQUEST._serialized_start=491
  _APPDEPLOYREQUEST._serialized_end=690
  _APPDEPLOYRESPONSE._serialized_start=692
  _APPDEPLOYRESPONSE._serialized_end=724
  _APPDEPLOYMENTHISTORY._serialized_start=727
  _APPDEPLOYMENTHISTORY._serialized_end=1013
  _APPDEPLOYMENTHISTORYREQUEST._serialized_start=1015
  _APPDEPLOYMENTHISTORYREQUEST._serialized_end=1060
  _APPDEPLOYMENTHISTORYRESPONSE._serialized_start=1062
  _APPDEPLOYMENTHISTORYRESPONSE._serialized_end=1162
  _APPGETBYDEPLOYMENTNAMEREQUEST._serialized_start=1164
  _APPGETBYDEPLOYMENTNAMEREQUEST._serialized_end=1289
  _APPGETBYDEPLOYMENTNAMERESPONSE._serialized_start=1291
  _APPGETBYDEPLOYMENTNAMERESPONSE._serialized_end=1339
  _APPGETLAYOUTREQUEST._serialized_start=1341
  _APPGETLAYOUTREQUEST._serialized_end=1378
  _APPGETLAYOUTRESPONSE._serialized_start=1380
  _APPGETLAYOUTRESPONSE._serialized_end=1447
  _APPGETLOGSREQUEST._serialized_start=1450
  _APPGETLOGSREQUEST._serialized_end=1662
  _APPGETOBJECTSITEM._serialized_start=1664
  _APPGETOBJECTSITEM._serialized_end=1734
  _APPGETOBJECTSREQUEST._serialized_start=1736
  _APPGETOBJECTSREQUEST._serialized_end=1830
  _APPGETOBJECTSRESPONSE._serialized_start=1832
  _APPGETOBJECTSRESPONSE._serialized_end=1903
  _APPGETORCREATEREQUEST._serialized_start=1906
  _APPGETORCREATEREQUEST._serialized_end=2037
  _APPGETORCREATERESPONSE._serialized_start=2039
  _APPGETORCREATERESPONSE._serialized_end=2079
  _APPHEARTBEATREQUEST._serialized_start=2081
  _APPHEARTBEATREQUEST._serialized_end=2118
  _APPLAYOUT._serialized_start=2121
  _APPLAYOUT._serialized_end=2394
  _APPLAYOUT_FUNCTIONIDSENTRY._serialized_start=2295
  _APPLAYOUT_FUNCTIONIDSENTRY._serialized_end=2345
  _APPLAYOUT_CLASSIDSENTRY._serialized_start=2347
  _APPLAYOUT_CLASSIDSENTRY._serialized_end=2394
  _APPLISTREQUEST._serialized_start=2396
  _APPLISTREQUEST._serialized_end=2438
  _APPLISTRESPONSE._serialized_start=2441
  _APPLISTRESPONSE._serialized_end=2686
  _APPLISTRESPONSE_APPLISTITEM._serialized_start=2518
  _APPLISTRESPONSE_APPLISTITEM._serialized_end=2686
  _APPLOOKUPREQUEST._serialized_start=2688
  _APPLOOKUPREQUEST._serialized_end=2750
  _APPLOOKUPRESPONSE._serialized_start=2752
  _APPLOOKUPRESPONSE._serialized_end=2787
  _APPPUBLISHREQUEST._serialized_start=2790
  _APPPUBLISHREQUEST._serialized_end=3378
  _APPPUBLISHREQUEST_FUNCTIONIDSENTRY._serialized_start=2295
  _APPPUBLISHREQUEST_FUNCTIONIDSENTRY._serialized_end=2345
  _APPPUBLISHREQUEST_CLASSIDSENTRY._serialized_start=2347
  _APPPUBLISHREQUEST_CLASSIDSENTRY._serialized_end=2394
  _APPPUBLISHREQUEST_DEFINITIONIDSENTRY._serialized_start=3326
  _APPPUBLISHREQUEST_DEFINITIONIDSENTRY._serialized_end=3378
  _APPPUBLISHRESPONSE._serialized_start=3380
  _APPPUBLISHRESPONSE._serialized_end=3461
  _APPROLLBACKREQUEST._serialized_start=3463
  _APPROLLBACKREQUEST._serialized_end=3516
  _APPSETOBJECTSREQUEST._serialized_start=3519
  _APPSETOBJECTSREQUEST._serialized_end=3802
  _APPSETOBJECTSREQUEST_INDEXEDOBJECTIDSENTRY._serialized_start=3741
  _APPSETOBJECTSREQUEST_INDEXEDOBJECTIDSENTRY._serialized_end=3796
  _APPSTOPREQUEST._serialized_start=3804
  _APPSTOPREQUEST._serialized_end=3887
  _ASGI._serialized_start=3890
  _ASGI._serialized_end=5717
  _ASGI_HTTP._serialized_start=4710
  _ASGI_HTTP._serialized_end=4907
  _ASGI_HTTPREQUEST._serialized_start=4909
  _ASGI_HTTPREQUEST._serialized_end=4955
  _ASGI_HTTPRESPONSESTART._serialized_start=4957
  _ASGI_HTTPRESPONSESTART._serialized_end=5027
  _ASGI_HTTPRESPONSEBODY._serialized_start=5029
  _ASGI_HTTPRESPONSEBODY._serialized_end=5080
  _ASGI_HTTPRESPONSETRAILERS._serialized_start=5082
  _ASGI_HTTPRESPONSETRAILERS._serialized_end=5144
  _ASGI_HTTPDISCONNECT._serialized_start=5146
  _ASGI_HTTPDISCONNECT._serialized_end=5162
  _ASGI_WEBSOCKET._serialized_start=5165
  _ASGI_WEBSOCKET._serialized_end=5373
  _ASGI_WEBSOCKETCONNECT._serialized_start=5375
  _ASGI_WEBSOCKETCONNECT._serialized_end=5393
  _ASGI_WEBSOCKETACCEPT._serialized_start=5395
  _ASGI_WEBSOCKETACCEPT._serialized_end=5471
  _ASGI_WEBSOCKETRECEIVE._serialized_start=5473
  _ASGI_WEBSOCKETRECEIVE._serialized_end=5535
  _ASGI_WEBSOCKETSEND._serialized_start=5537
  _ASGI_WEBSOCKETSEND._serialized_end=5596
  _ASGI_WEBSOCKETDISCONNECT._serialized_start=5598
  _ASGI_WEBSOCKETDISCONNECT._serialized_end=5647
  _ASGI_WEBSOCKETCLOSE._serialized_start=5649
  _ASGI_WEBSOCKETCLOSE._serialized_end=5709
  _ATTEMPTAWAITREQUEST._serialized_start=5719
  _ATTEMPTAWAITREQUEST._serialized_end=5807
  _ATTEMPTAWAITRESPONSE._serialized_start=5809
  _ATTEMPTAWAITRESPONSE._serialized_end=5901
  _ATTEMPTRETRYREQUEST._serialized_start=5904
  _ATTEMPTRETRYREQUEST._serialized_end=6046
  _ATTEMPTRETRYRESPONSE._serialized_start=6048
  _ATTEMPTRETRYRESPONSE._serialized_end=6093
  _ATTEMPTSTARTREQUEST._serialized_start=6095
  _ATTEMPTSTARTREQUEST._serialized_end=6214
  _ATTEMPTSTARTRESPONSE._serialized_start=6216
  _ATTEMPTSTARTRESPONSE._serialized_end=6318
  _AUTOSCALERSETTINGS._serialized_start=6321
  _AUTOSCALERSETTINGS._serialized_end=6591
  _BASEIMAGE._serialized_start=6593
  _BASEIMAGE._serialized_end=6648
  _BLOBCREATEREQUEST._serialized_start=6650
  _BLOBCREATEREQUEST._serialized_end=6745
  _BLOBCREATERESPONSE._serialized_start=6748
  _BLOBCREATERESPONSE._serialized_end=6880
  _BLOBGETREQUEST._serialized_start=6882
  _BLOBGETREQUEST._serialized_end=6915
  _BLOBGETRESPONSE._serialized_start=6917
  _BLOBGETRESPONSE._serialized_end=6956
  _BUILDFUNCTION._serialized_start=6958
  _BUILDFUNCTION._serialized_end=7054
  _CANCELINPUTEVENT._serialized_start=7056
  _CANCELINPUTEVENT._serialized_end=7123
  _CHECKPOINTINFO._serialized_start=7126
  _CHECKPOINTINFO._serialized_end=7369
  _CLASSCREATEREQUEST._serialized_start=7372
  _CLASSCREATEREQUEST._serialized_end=7520
  _CLASSCREATERESPONSE._serialized_start=7522
  _CLASSCREATERESPONSE._serialized_end=7621
  _CLASSGETREQUEST._serialized_start=7624
  _CLASSGETREQUEST._serialized_end=7800
  _CLASSGETRESPONSE._serialized_start=7803
  _CLASSGETRESPONSE._serialized_end=7947
  _CLASSHANDLEMETADATA._serialized_start=7950
  _CLASSHANDLEMETADATA._serialized_end=8113
  _CLASSMETHOD._serialized_start=8116
  _CLASSMETHOD._serialized_end=8245
  _CLASSPARAMETERINFO._serialized_start=8248
  _CLASSPARAMETERINFO._serialized_end=8551
  _CLASSPARAMETERINFO_PARAMETERSERIALIZATIONFORMAT._serialized_start=8400
  _CLASSPARAMETERINFO_PARAMETERSERIALIZATIONFORMAT._serialized_end=8551
  _CLASSPARAMETERSET._serialized_start=8553
  _CLASSPARAMETERSET._serialized_end=8627
  _CLASSPARAMETERSPEC._serialized_start=8630
  _CLASSPARAMETERSPEC._serialized_end=8922
  _CLASSPARAMETERVALUE._serialized_start=8925
  _CLASSPARAMETERVALUE._serialized_end=9132
  _CLIENTHELLORESPONSE._serialized_start=9134
  _CLIENTHELLORESPONSE._serialized_end=9251
  _CLOUDBUCKETMOUNT._serialized_start=9254
  _CLOUDBUCKETMOUNT._serialized_end=9661
  _CLOUDBUCKETMOUNT_BUCKETTYPE._serialized_start=9545
  _CLOUDBUCKETMOUNT_BUCKETTYPE._serialized_end=9599
  _CLUSTERGETREQUEST._serialized_start=9663
  _CLUSTERGETREQUEST._serialized_end=9702
  _CLUSTERGETRESPONSE._serialized_start=9704
  _CLUSTERGETRESPONSE._serialized_end=9769
  _CLUSTERLISTREQUEST._serialized_start=9771
  _CLUSTERLISTREQUEST._serialized_end=9817
  _CLUSTERLISTRESPONSE._serialized_start=9819
  _CLUSTERLISTRESPONSE._serialized_end=9886
  _CLUSTERSTATS._serialized_start=9888
  _CLUSTERSTATS._serialized_end=9976
  _COMMITINFO._serialized_start=9979
  _COMMITINFO._serialized_end=10143
  _CONTAINERARGUMENTS._serialized_start=10146
  _CONTAINERARGUMENTS._serialized_end=10606
  _CONTAINERARGUMENTS_TRACINGCONTEXTENTRY._serialized_start=10535
  _CONTAINERARGUMENTS_TRACINGCONTEXTENTRY._serialized_end=10588
  _CONTAINERCHECKPOINTREQUEST._serialized_start=10608
  _CONTAINERCHECKPOINTREQUEST._serialized_end=10659
  _CONTAINEREXECGETOUTPUTREQUEST._serialized_start=10662
  _CONTAINEREXECGETOUTPUTREQUEST._serialized_end=10831
  _CONTAINEREXECPUTINPUTREQUEST._serialized_start=10833
  _CONTAINEREXECPUTINPUTREQUEST._serialized_end=10930
  _CONTAINEREXECREQUEST._serialized_start=10933
  _CONTAINEREXECREQUEST._serialized_end=11298
  _CONTAINEREXECRESPONSE._serialized_start=11300
  _CONTAINEREXECRESPONSE._serialized_end=11340
  _CONTAINEREXECWAITREQUEST._serialized_start=11342
  _CONTAINEREXECWAITREQUEST._serialized_end=11402
  _CONTAINEREXECWAITRESPONSE._serialized_start=11404
  _CONTAINEREXECWAITRESPONSE._serialized_end=11488
  _CONTAINERFILECLOSEREQUEST._serialized_start=11490
  _CONTAINERFILECLOSEREQUEST._serialized_end=11542
  _CONTAINERFILEDELETEBYTESREQUEST._serialized_start=11545
  _CONTAINERFILEDELETEBYTESREQUEST._serialized_end=11699
  _CONTAINERFILEFLUSHREQUEST._serialized_start=11701
  _CONTAINERFILEFLUSHREQUEST._serialized_end=11753
  _CONTAINERFILELSREQUEST._serialized_start=11755
  _CONTAINERFILELSREQUEST._serialized_end=11793
  _CONTAINERFILEMKDIRREQUEST._serialized_start=11795
  _CONTAINERFILEMKDIRREQUEST._serialized_end=11858
  _CONTAINERFILEOPENREQUEST._serialized_start=11860
  _CONTAINERFILEOPENREQUEST._serialized_end=11964
  _CONTAINERFILEREADLINEREQUEST._serialized_start=11966
  _CONTAINERFILEREADLINEREQUEST._serialized_end=12021
  _CONTAINERFILEREADREQUEST._serialized_start=12023
  _CONTAINERFILEREADREQUEST._serialized_end=12096
  _CONTAINERFILERMREQUEST._serialized_start=12098
  _CONTAINERFILERMREQUEST._serialized_end=12155
  _CONTAINERFILESEEKREQUEST._serialized_start=12157
  _CONTAINERFILESEEKREQUEST._serialized_end=12266
  _CONTAINERFILEWATCHREQUEST._serialized_start=12268
  _CONTAINERFILEWATCHREQUEST._serialized_end=12372
  _CONTAINERFILEWRITEREPLACEBYTESREQUEST._serialized_start=12375
  _CONTAINERFILEWRITEREPLACEBYTESREQUEST._serialized_end=12549
  _CONTAINERFILEWRITEREQUEST._serialized_start=12551
  _CONTAINERFILEWRITEREQUEST._serialized_end=12617
  _CONTAINERFILESYSTEMEXECGETOUTPUTREQUEST._serialized_start=12619
  _CONTAINERFILESYSTEMEXECGETOUTPUTREQUEST._serialized_end=12694
  _CONTAINERFILESYSTEMEXECREQUEST._serialized_start=12697
  _CONTAINERFILESYSTEMEXECREQUEST._serialized_end=13724
  _CONTAINERFILESYSTEMEXECRESPONSE._serialized_start=13726
  _CONTAINERFILESYSTEMEXECRESPONSE._serialized_end=13826
  _CONTAINERHEARTBEATREQUEST._serialized_start=13829
  _CONTAINERHEARTBEATREQUEST._serialized_end=13957
  _CONTAINERHEARTBEATRESPONSE._serialized_start=13959
  _CONTAINERHEARTBEATRESPONSE._serialized_end=14075
  _CONTAINERLOGREQUEST._serialized_start=14077
  _CONTAINERLOGREQUEST._serialized_end=14136
  _CONTAINERSTOPREQUEST._serialized_start=14138
  _CONTAINERSTOPREQUEST._serialized_end=14183
  _CONTAINERSTOPRESPONSE._serialized_start=14185
  _CONTAINERSTOPRESPONSE._serialized_end=14208
  _CUSTOMDOMAINCONFIG._serialized_start=14210
  _CUSTOMDOMAINCONFIG._serialized_end=14244
  _CUSTOMDOMAININFO._serialized_start=14246
  _CUSTOMDOMAININFO._serialized_end=14277
  _DNSRECORD._serialized_start=14279
  _DNSRECORD._serialized_end=14362
  _DATACHUNK._serialized_start=14364
  _DATACHUNK._serialized_end=14491
  _DICTCLEARREQUEST._serialized_start=14493
  _DICTCLEARREQUEST._serialized_end=14528
  _DICTCONTAINSREQUEST._serialized_start=14530
  _DICTCONTAINSREQUEST._serialized_end=14581
  _DICTCONTAINSRESPONSE._serialized_start=14583
  _DICTCONTAINSRESPONSE._serialized_end=14620
  _DICTCONTENTSREQUEST._serialized_start=14622
  _DICTCONTENTSREQUEST._serialized_end=14690
  _DICTDELETEREQUEST._serialized_start=14692
  _DICTDELETEREQUEST._serialized_end=14728
  _DICTENTRY._serialized_start=14730
  _DICTENTRY._serialized_end=14769
  _DICTGETORCREATEREQUEST._serialized_start=14772
  _DICTGETORCREATEREQUEST._serialized_end=15010
  _DICTGETORCREATERESPONSE._serialized_start=15012
  _DICTGETORCREATERESPONSE._serialized_end=15054
  _DICTGETREQUEST._serialized_start=15056
  _DICTGETREQUEST._serialized_end=15102
  _DICTGETRESPONSE._serialized_start=15104
  _DICTGETRESPONSE._serialized_end=15166
  _DICTHEARTBEATREQUEST._serialized_start=15168
  _DICTHEARTBEATREQUEST._serialized_end=15207
  _DICTLENREQUEST._serialized_start=15209
  _DICTLENREQUEST._serialized_end=15242
  _DICTLENRESPONSE._serialized_start=15244
  _DICTLENRESPONSE._serialized_end=15274
  _DICTLISTREQUEST._serialized_start=15276
  _DICTLISTREQUEST._serialized_end=15319
  _DICTLISTRESPONSE._serialized_start=15321
  _DICTLISTRESPONSE._serialized_end=15441
  _DICTLISTRESPONSE_DICTINFO._serialized_start=15397
  _DICTLISTRESPONSE_DICTINFO._serialized_end=15441
  _DICTPOPREQUEST._serialized_start=15443
  _DICTPOPREQUEST._serialized_end=15489
  _DICTPOPRESPONSE._serialized_start=15491
  _DICTPOPRESPONSE._serialized_end=15553
  _DICTUPDATEREQUEST._serialized_start=15555
  _DICTUPDATEREQUEST._serialized_end=15662
  _DICTUPDATERESPONSE._serialized_start=15664
  _DICTUPDATERESPONSE._serialized_end=15701
  _DOMAIN._serialized_start=15704
  _DOMAIN._serialized_end=15879
  _DOMAINCERTIFICATEVERIFYREQUEST._serialized_start=15881
  _DOMAINCERTIFICATEVERIFYREQUEST._serialized_end=15932
  _DOMAINCERTIFICATEVERIFYRESPONSE._serialized_start=15934
  _DOMAINCERTIFICATEVERIFYRESPONSE._serialized_end=16005
  _DOMAINCREATEREQUEST._serialized_start=16007
  _DOMAINCREATEREQUEST._serialized_end=16055
  _DOMAINCREATERESPONSE._serialized_start=16057
  _DOMAINCREATERESPONSE._serialized_end=16144
  _DOMAINLISTREQUEST._serialized_start=16146
  _DOMAINLISTREQUEST._serialized_end=16165
  _DOMAINLISTRESPONSE._serialized_start=16167
  _DOMAINLISTRESPONSE._serialized_end=16226
  _ENVIRONMENTCREATEREQUEST._serialized_start=16228
  _ENVIRONMENTCREATEREQUEST._serialized_end=16274
  _ENVIRONMENTDELETEREQUEST._serialized_start=16276
  _ENVIRONMENTDELETEREQUEST._serialized_end=16322
  _ENVIRONMENTGETORCREATEREQUEST._serialized_start=16324
  _ENVIRONMENTGETORCREATEREQUEST._serialized_end=16450
  _ENVIRONMENTGETORCREATERESPONSE._serialized_start=16452
  _ENVIRONMENTGETORCREATERESPONSE._serialized_end=16561
  _ENVIRONMENTLISTITEM._serialized_start=16563
  _ENVIRONMENTLISTITEM._serialized_end=16659
  _ENVIRONMENTLISTRESPONSE._serialized_start=16661
  _ENVIRONMENTLISTRESPONSE._serialized_end=16736
  _ENVIRONMENTMETADATA._serialized_start=16738
  _ENVIRONMENTMETADATA._serialized_end=16826
  _ENVIRONMENTSETTINGS._serialized_start=16828
  _ENVIRONMENTSETTINGS._serialized_end=16904
  _ENVIRONMENTUPDATEREQUEST._serialized_start=16907
  _ENVIRONMENTUPDATEREQUEST._serialized_end=17055
  _FILEENTRY._serialized_start=17058
  _FILEENTRY._serialized_end=17249
  _FILEENTRY_FILETYPE._serialized_start=17162
  _FILEENTRY_FILETYPE._serialized_end=17249
  _FILESYSTEMRUNTIMEOUTPUTBATCH._serialized_start=17252
  _FILESYSTEMRUNTIMEOUTPUTBATCH._serialized_end=17396
  _FUNCTION._serialized_start=17399
  _FUNCTION._serialized_end=20485
  _FUNCTION_METHODDEFINITIONSENTRY._serialized_start=19999
  _FUNCTION_METHODDEFINITIONSENTRY._serialized_end=20087
  _FUNCTION_EXPERIMENTALOPTIONSENTRY._serialized_start=20089
  _FUNCTION_EXPERIMENTALOPTIONSENTRY._serialized_end=20147
  _FUNCTION_DEFINITIONTYPE._serialized_start=20149
  _FUNCTION_DEFINITIONTYPE._serialized_end=20256
  _FUNCTION_FUNCTIONTYPE._serialized_start=20258
  _FUNCTION_FUNCTIONTYPE._serialized_end=20360
  _FUNCTIONASYNCINVOKEREQUEST._serialized_start=20487
  _FUNCTIONASYNCINVOKEREQUEST._serialized_end=20605
  _FUNCTIONASYNCINVOKERESPONSE._serialized_start=20607
  _FUNCTIONASYNCINVOKERESPONSE._serialized_end=20694
  _FUNCTIONBINDPARAMSREQUEST._serialized_start=20697
  _FUNCTIONBINDPARAMSREQUEST._serialized_end=20855
  _FUNCTIONBINDPARAMSRESPONSE._serialized_start=20857
  _FUNCTIONBINDPARAMSRESPONSE._serialized_end=20975
  _FUNCTIONCALLCALLGRAPHINFO._serialized_start=20977
  _FUNCTIONCALLCALLGRAPHINFO._serialized_end=21099
  _FUNCTIONCALLCANCELREQUEST._serialized_start=21101
  _FUNCTIONCALLCANCELREQUEST._serialized_end=21184
  _FUNCTIONCALLGETDATAREQUEST._serialized_start=21186
  _FUNCTIONCALLGETDATAREQUEST._serialized_end=21260
  _FUNCTIONCALLINFO._serialized_start=21263
  _FUNCTIONCALLINFO._serialized_end=21714
  _FUNCTIONCALLLISTREQUEST._serialized_start=21716
  _FUNCTIONCALLLISTREQUEST._serialized_end=21762
  _FUNCTIONCALLLISTRESPONSE._serialized_start=21764
  _FUNCTIONCALLLISTRESPONSE._serialized_end=21846
  _FUNCTIONCALLPUTDATAREQUEST._serialized_start=21848
  _FUNCTIONCALLPUTDATAREQUEST._serialized_end=21948
  _FUNCTIONCREATEREQUEST._serialized_start=21951
  _FUNCTIONCREATEREQUEST._serialized_end=22171
  _FUNCTIONCREATERESPONSE._serialized_start=22174
  _FUNCTIONCREATERESPONSE._serialized_end=22406
  _FUNCTIONDATA._serialized_start=22409
  _FUNCTIONDATA._serialized_end=23925
  _FUNCTIONDATA_METHODDEFINITIONSENTRY._serialized_start=19999
  _FUNCTIONDATA_METHODDEFINITIONSENTRY._serialized_end=20087
  _FUNCTIONDATA_RANKEDFUNCTION._serialized_start=23760
  _FUNCTIONDATA_RANKEDFUNCTION._serialized_end=23832
  _FUNCTIONDATA_EXPERIMENTALOPTIONSENTRY._serialized_start=20089
  _FUNCTIONDATA_EXPERIMENTALOPTIONSENTRY._serialized_end=20147
  _FUNCTIONEXTENDED._serialized_start=23928
  _FUNCTIONEXTENDED._serialized_end=24099
  _FUNCTIONGETCALLGRAPHREQUEST._serialized_start=24101
  _FUNCTIONGETCALLGRAPHREQUEST._serialized_end=24156
  _FUNCTIONGETCALLGRAPHRESPONSE._serialized_start=24159
  _FUNCTIONGETCALLGRAPHRESPONSE._serialized_end=24304
  _FUNCTIONGETCURRENTSTATSREQUEST._serialized_start=24306
  _FUNCTIONGETCURRENTSTATSREQUEST._serialized_end=24359
  _FUNCTIONGETDYNAMICCONCURRENCYREQUEST._serialized_start=24361
  _FUNCTIONGETDYNAMICCONCURRENCYREQUEST._serialized_end=24473
  _FUNCTIONGETDYNAMICCONCURRENCYRESPONSE._serialized_start=24475
  _FUNCTIONGETDYNAMICCONCURRENCYRESPONSE._serialized_end=24535
  _FUNCTIONGETINPUTSITEM._serialized_start=24538
  _FUNCTIONGETINPUTSITEM._serialized_end=24778
  _FUNCTIONGETINPUTSREQUEST._serialized_start=24781
  _FUNCTIONGETINPUTSREQUEST._serialized_end=24963
  _FUNCTIONGETINPUTSRESPONSE._serialized_start=24965
  _FUNCTIONGETINPUTSRESPONSE._serialized_end=25080
  _FUNCTIONGETOUTPUTSITEM._serialized_start=25083
  _FUNCTIONGETOUTPUTSITEM._serialized_end=25343
  _FUNCTIONGETOUTPUTSREQUEST._serialized_start=25346
  _FUNCTIONGETOUTPUTSREQUEST._serialized_end=25527
  _FUNCTIONGETOUTPUTSRESPONSE._serialized_start=25530
  _FUNCTIONGETOUTPUTSRESPONSE._serialized_end=25681
  _FUNCTIONGETREQUEST._serialized_start=25684
  _FUNCTIONGETREQUEST._serialized_end=25822
  _FUNCTIONGETRESPONSE._serialized_start=25825
  _FUNCTIONGETRESPONSE._serialized_end=25978
  _FUNCTIONGETSERIALIZEDREQUEST._serialized_start=25980
  _FUNCTIONGETSERIALIZEDREQUEST._serialized_end=26031
  _FUNCTIONGETSERIALIZEDRESPONSE._serialized_start=26033
  _FUNCTIONGETSERIALIZEDRESPONSE._serialized_end=26119
  _FUNCTIONHANDLEMETADATA._serialized_start=26122
  _FUNCTIONHANDLEMETADATA._serialized_end=26702
  _FUNCTIONHANDLEMETADATA_METHODHANDLEMETADATAENTRY._serialized_start=26585
  _FUNCTIONHANDLEMETADATA_METHODHANDLEMETADATAENTRY._serialized_end=26682
  _FUNCTIONINPUT._serialized_start=26705
  _FUNCTIONINPUT._serialized_end=26884
  _FUNCTIONMAPREQUEST._serialized_start=26887
  _FUNCTIONMAPREQUEST._serialized_end=27208
  _FUNCTIONMAPRESPONSE._serialized_start=27211
  _FUNCTIONMAPRESPONSE._serialized_end=27482
  _FUNCTIONOPTIONS._serialized_start=27485
  _FUNCTIONOPTIONS._serialized_end=28284
  _FUNCTIONPRECREATEREQUEST._serialized_start=28287
  _FUNCTIONPRECREATEREQUEST._serialized_end=28787
  _FUNCTIONPRECREATEREQUEST_METHODDEFINITIONSENTRY._serialized_start=19999
  _FUNCTIONPRECREATEREQUEST_METHODDEFINITIONSENTRY._serialized_end=20087
  _FUNCTIONPRECREATERESPONSE._serialized_start=28789
  _FUNCTIONPRECREATERESPONSE._serialized_end=28900
  _FUNCTIONPUTINPUTSITEM._serialized_start=28902
  _FUNCTIONPUTINPUTSITEM._serialized_end=28982
  _FUNCTIONPUTINPUTSREQUEST._serialized_start=28984
  _FUNCTIONPUTINPUTSREQUEST._serialized_end=29110
  _FUNCTIONPUTINPUTSRESPONSE._serialized_start=29112
  _FUNCTIONPUTINPUTSRESPONSE._serialized_end=29200
  _FUNCTIONPUTINPUTSRESPONSEITEM._serialized_start=29202
  _FUNCTIONPUTINPUTSRESPONSEITEM._serialized_end=29283
  _FUNCTIONPUTOUTPUTSITEM._serialized_start=29286
  _FUNCTIONPUTOUTPUTSITEM._serialized_end=29494
  _FUNCTIONPUTOUTPUTSREQUEST._serialized_start=29496
  _FUNCTIONPUTOUTPUTSREQUEST._serialized_end=29600
  _FUNCTIONRETRYINPUTSITEM._serialized_start=29602
  _FUNCTIONRETRYINPUTSITEM._serialized_end=29711
  _FUNCTIONRETRYINPUTSREQUEST._serialized_start=29713
  _FUNCTIONRETRYINPUTSREQUEST._serialized_end=29823
  _FUNCTIONRETRYINPUTSRESPONSE._serialized_start=29825
  _FUNCTIONRETRYINPUTSRESPONSE._serialized_end=29874
  _FUNCTIONRETRYPOLICY._serialized_start=29876
  _FUNCTIONRETRYPOLICY._serialized_end=29991
  _FUNCTIONSCHEMA._serialized_start=29994
  _FUNCTIONSCHEMA._serialized_end=30267
  _FUNCTIONSCHEMA_FUNCTIONSCHEMATYPE._serialized_start=30190
  _FUNCTIONSCHEMA_FUNCTIONSCHEMATYPE._serialized_end=30267
  _FUNCTIONSTATS._serialized_start=30269
  _FUNCTIONSTATS._serialized_end=30326
  _FUNCTIONUPDATESCHEDULINGPARAMSREQUEST._serialized_start=30329
  _FUNCTIONUPDATESCHEDULINGPARAMSREQUEST._serialized_end=30474
  _FUNCTIONUPDATESCHEDULINGPARAMSRESPONSE._serialized_start=30476
  _FUNCTIONUPDATESCHEDULINGPARAMSRESPONSE._serialized_end=30516
  _GPUCONFIG._serialized_start=30518
  _GPUCONFIG._serialized_end=30599
  _GENERATORDONE._serialized_start=30601
  _GENERATORDONE._serialized_end=30637
  _GENERICPAYLOADTYPE._serialized_start=30639
  _GENERICPAYLOADTYPE._serialized_end=30760
  _GENERICRESULT._serialized_start=30763
  _GENERICRESULT._serialized_end=31256
  _GENERICRESULT_GENERICSTATUS._serialized_start=31010
  _GENERICRESULT_GENERICSTATUS._serialized_end=31242
  _IMAGE._serialized_start=31259
  _IMAGE._serialized_end=31723
  _IMAGECONTEXTFILE._serialized_start=31725
  _IMAGECONTEXTFILE._serialized_end=31775
  _IMAGEFROMIDREQUEST._serialized_start=31777
  _IMAGEFROMIDREQUEST._serialized_end=31815
  _IMAGEFROMIDRESPONSE._serialized_start=31817
  _IMAGEFROMIDRESPONSE._serialized_end=31903
  _IMAGEGETORCREATEREQUEST._serialized_start=31906
  _IMAGEGETORCREATEREQUEST._serialized_end=32198
  _IMAGEGETORCREATERESPONSE._serialized_start=32201
  _IMAGEGETORCREATERESPONSE._serialized_end=32337
  _IMAGEJOINSTREAMINGREQUEST._serialized_start=32339
  _IMAGEJOINSTREAMINGREQUEST._serialized_end=32459
  _IMAGEJOINSTREAMINGRESPONSE._serialized_start=32462
  _IMAGEJOINSTREAMINGRESPONSE._serialized_end=32656
  _IMAGEMETADATA._serialized_start=32659
  _IMAGEMETADATA._serialized_end=33011
  _IMAGEMETADATA_PYTHONPACKAGESENTRY._serialized_start=32874
  _IMAGEMETADATA_PYTHONPACKAGESENTRY._serialized_end=32927
  _IMAGEREGISTRYCONFIG._serialized_start=33013
  _IMAGEREGISTRYCONFIG._serialized_end=33113
  _INPUTCALLGRAPHINFO._serialized_start=33116
  _INPUTCALLGRAPHINFO._serialized_end=33256
  _INPUTCATEGORYINFO._serialized_start=33258
  _INPUTCATEGORYINFO._serialized_end=33333
  _INPUTINFO._serialized_start=33336
  _INPUTINFO._serialized_end=33489
  _METHODDEFINITION._serialized_start=33492
  _METHODDEFINITION._serialized_end=33826
  _MOUNTFILE._serialized_start=33828
  _MOUNTFILE._serialized_end=33933
  _MOUNTGETORCREATEREQUEST._serialized_start=33936
  _MOUNTGETORCREATEREQUEST._serialized_end=34186
  _MOUNTGETORCREATERESPONSE._serialized_start=34188
  _MOUNTGETORCREATERESPONSE._serialized_end=34292
  _MOUNTHANDLEMETADATA._serialized_start=34294
  _MOUNTHANDLEMETADATA._serialized_end=34352
  _MOUNTPUTFILEREQUEST._serialized_start=34354
  _MOUNTPUTFILEREQUEST._serialized_end=34449
  _MOUNTPUTFILERESPONSE._serialized_start=34451
  _MOUNTPUTFILERESPONSE._serialized_end=34489
  _MULTIPARTUPLOAD._serialized_start=34491
  _MULTIPARTUPLOAD._serialized_end=34574
  _NETWORKACCESS._serialized_start=34577
  _NETWORKACCESS._serialized_end=34767
  _NETWORKACCESS_NETWORKACCESSTYPE._serialized_start=34693
  _NETWORKACCESS_NETWORKACCESSTYPE._serialized_end=34767
  _NOTEBOOKKERNELPUBLISHRESULTSREQUEST._serialized_start=34770
  _NOTEBOOKKERNELPUBLISHRESULTSREQUEST._serialized_end=35190
  _NOTEBOOKKERNELPUBLISHRESULTSREQUEST_EXECUTEREPLY._serialized_start=34909
  _NOTEBOOKKERNELPUBLISHRESULTSREQUEST_EXECUTEREPLY._serialized_end=34982
  _NOTEBOOKKERNELPUBLISHRESULTSREQUEST_CELLRESULT._serialized_start=34985
  _NOTEBOOKKERNELPUBLISHRESULTSREQUEST_CELLRESULT._serialized_end=35190
  _NOTEBOOKOUTPUT._serialized_start=35193
  _NOTEBOOKOUTPUT._serialized_end=35847
  _NOTEBOOKOUTPUT_EXECUTERESULT._serialized_start=35455
  _NOTEBOOKOUTPUT_EXECUTERESULT._serialized_end=35577
  _NOTEBOOKOUTPUT_DISPLAYDATA._serialized_start=35580
  _NOTEBOOKOUTPUT_DISPLAYDATA._serialized_end=35735
  _NOTEBOOKOUTPUT_STREAM._serialized_start=35737
  _NOTEBOOKOUTPUT_STREAM._serialized_end=35773
  _NOTEBOOKOUTPUT_ERROR._serialized_start=35775
  _NOTEBOOKOUTPUT_ERROR._serialized_end=35832
  _OBJECT._serialized_start=35850
  _OBJECT._serialized_end=36241
  _OBJECTDEPENDENCY._serialized_start=36243
  _OBJECTDEPENDENCY._serialized_end=36280
  _PTYINFO._serialized_start=36283
  _PTYINFO._serialized_end=36545
  _PTYINFO_PTYTYPE._serialized_start=36467
  _PTYINFO_PTYTYPE._serialized_end=36545
  _PORTSPEC._serialized_start=36547
  _PORTSPEC._serialized_end=36660
  _PORTSPECS._serialized_start=36662
  _PORTSPECS._serialized_end=36712
  _PROXY._serialized_start=36714
  _PROXY._serialized_end=36841
  _PROXYADDIPREQUEST._serialized_start=36843
  _PROXYADDIPREQUEST._serialized_end=36886
  _PROXYADDIPRESPONSE._serialized_start=36888
  _PROXYADDIPRESPONSE._serialized_end=36949
  _PROXYCREATEREQUEST._serialized_start=36951
  _PROXYCREATEREQUEST._serialized_end=37017
  _PROXYCREATERESPONSE._serialized_start=37019
  _PROXYCREATERESPONSE._serialized_end=37076
  _PROXYDELETEREQUEST._serialized_start=37078
  _PROXYDELETEREQUEST._serialized_end=37122
  _PROXYGETORCREATEREQUEST._serialized_start=37125
  _PROXYGETORCREATEREQUEST._serialized_end=37325
  _PROXYGETORCREATERESPONSE._serialized_start=37327
  _PROXYGETORCREATERESPONSE._serialized_end=37371
  _PROXYGETREQUEST._serialized_start=37373
  _PROXYGETREQUEST._serialized_end=37436
  _PROXYGETRESPONSE._serialized_start=37438
  _PROXYGETRESPONSE._serialized_end=37492
  _PROXYINFO._serialized_start=37495
  _PROXYINFO._serialized_end=37632
  _PROXYIP._serialized_start=37634
  _PROXYIP._serialized_end=37752
  _PROXYLISTRESPONSE._serialized_start=37754
  _PROXYLISTRESPONSE._serialized_end=37811
  _PROXYREMOVEIPREQUEST._serialized_start=37813
  _PROXYREMOVEIPREQUEST._serialized_end=37859
  _QUEUECLEARREQUEST._serialized_start=37861
  _QUEUECLEARREQUEST._serialized_end=37945
  _QUEUEDELETEREQUEST._serialized_start=37947
  _QUEUEDELETEREQUEST._serialized_end=37991
  _QUEUEGETORCREATEREQUEST._serialized_start=37994
  _QUEUEGETORCREATEREQUEST._serialized_end=38188
  _QUEUEGETORCREATERESPONSE._serialized_start=38190
  _QUEUEGETORCREATERESPONSE._serialized_end=38234
  _QUEUEGETREQUEST._serialized_start=38236
  _QUEUEGETREQUEST._serialized_end=38329
  _QUEUEGETRESPONSE._serialized_start=38331
  _QUEUEGETRESPONSE._serialized_end=38365
  _QUEUEHEARTBEATREQUEST._serialized_start=38367
  _QUEUEHEARTBEATREQUEST._serialized_end=38408
  _QUEUEITEM._serialized_start=38410
  _QUEUEITEM._serialized_end=38454
  _QUEUELENREQUEST._serialized_start=38456
  _QUEUELENREQUEST._serialized_end=38529
  _QUEUELENRESPONSE._serialized_start=38531
  _QUEUELENRESPONSE._serialized_end=38562
  _QUEUELISTREQUEST._serialized_start=38564
  _QUEUELISTREQUEST._serialized_end=38634
  _QUEUELISTRESPONSE._serialized_start=38637
  _QUEUELISTRESPONSE._serialized_end=38806
  _QUEUELISTRESPONSE_QUEUEINFO._serialized_start=38717
  _QUEUELISTRESPONSE_QUEUEINFO._serialized_end=38806
  _QUEUENEXTITEMSREQUEST._serialized_start=38808
  _QUEUENEXTITEMSREQUEST._serialized_end=38922
  _QUEUENEXTITEMSRESPONSE._serialized_start=38924
  _QUEUENEXTITEMSRESPONSE._serialized_end=38988
  _QUEUEPUTREQUEST._serialized_start=38990
  _QUEUEPUTREQUEST._serialized_end=39095
  _RATELIMIT._serialized_start=39097
  _RATELIMIT._serialized_end=39174
  _RESOURCES._serialized_start=39177
  _RESOURCES._serialized_end=39358
  _RUNTIMEINPUTMESSAGE._serialized_start=39360
  _RUNTIMEINPUTMESSAGE._serialized_end=39434
  _RUNTIMEOUTPUTBATCH._serialized_start=39437
  _RUNTIMEOUTPUTBATCH._serialized_end=39721
  _RUNTIMEOUTPUTMESSAGE._serialized_start=39723
  _RUNTIMEOUTPUTMESSAGE._serialized_end=39840
  _S3MOUNT._serialized_start=39842
  _S3MOUNT._serialized_end=39942
  _SANDBOX._serialized_start=39945
  _SANDBOX._serialized_end=41001
  _SANDBOXCREATEREQUEST._serialized_start=41003
  _SANDBOXCREATEREQUEST._serialized_end=41116
  _SANDBOXCREATERESPONSE._serialized_start=41118
  _SANDBOXCREATERESPONSE._serialized_end=41161
  _SANDBOXGETLOGSREQUEST._serialized_start=41164
  _SANDBOXGETLOGSREQUEST._serialized_end=41302
  _SANDBOXGETRESOURCEUSAGEREQUEST._serialized_start=41304
  _SANDBOXGETRESOURCEUSAGEREQUEST._serialized_end=41356
  _SANDBOXGETRESOURCEUSAGERESPONSE._serialized_start=41359
  _SANDBOXGETRESOURCEUSAGERESPONSE._serialized_end=41503
  _SANDBOXGETTASKIDREQUEST._serialized_start=41505
  _SANDBOXGETTASKIDREQUEST._serialized_end=41610
  _SANDBOXGETTASKIDRESPONSE._serialized_start=41613
  _SANDBOXGETTASKIDRESPONSE._serialized_end=41744
  _SANDBOXGETTUNNELSREQUEST._serialized_start=41746
  _SANDBOXGETTUNNELSREQUEST._serialized_end=41809
  _SANDBOXGETTUNNELSRESPONSE._serialized_start=41811
  _SANDBOXGETTUNNELSRESPONSE._serialized_end=41926
  _SANDBOXHANDLEMETADATA._serialized_start=41928
  _SANDBOXHANDLEMETADATA._serialized_end=41996
  _SANDBOXINFO._serialized_start=41998
  _SANDBOXINFO._serialized_end=42108
  _SANDBOXLISTREQUEST._serialized_start=42111
  _SANDBOXLISTREQUEST._serialized_end=42265
  _SANDBOXLISTRESPONSE._serialized_start=42267
  _SANDBOXLISTRESPONSE._serialized_end=42334
  _SANDBOXRESTOREREQUEST._serialized_start=42336
  _SANDBOXRESTOREREQUEST._serialized_end=42380
  _SANDBOXRESTORERESPONSE._serialized_start=42382
  _SANDBOXRESTORERESPONSE._serialized_end=42426
  _SANDBOXSNAPSHOTFSREQUEST._serialized_start=42428
  _SANDBOXSNAPSHOTFSREQUEST._serialized_end=42491
  _SANDBOXSNAPSHOTFSRESPONSE._serialized_start=42494
  _SANDBOXSNAPSHOTFSRESPONSE._serialized_end=42637
  _SANDBOXSNAPSHOTGETREQUEST._serialized_start=42639
  _SANDBOXSNAPSHOTGETREQUEST._serialized_end=42687
  _SANDBOXSNAPSHOTGETRESPONSE._serialized_start=42689
  _SANDBOXSNAPSHOTGETRESPONSE._serialized_end=42738
  _SANDBOXSNAPSHOTREQUEST._serialized_start=42740
  _SANDBOXSNAPSHOTREQUEST._serialized_end=42784
  _SANDBOXSNAPSHOTRESPONSE._serialized_start=42786
  _SANDBOXSNAPSHOTRESPONSE._serialized_end=42832
  _SANDBOXSNAPSHOTWAITREQUEST._serialized_start=42834
  _SANDBOXSNAPSHOTWAITREQUEST._serialized_end=42900
  _SANDBOXSNAPSHOTWAITRESPONSE._serialized_start=42902
  _SANDBOXSNAPSHOTWAITRESPONSE._serialized_end=42976
  _SANDBOXSTDINWRITEREQUEST._serialized_start=42978
  _SANDBOXSTDINWRITEREQUEST._serialized_end=43067
  _SANDBOXSTDINWRITERESPONSE._serialized_start=43069
  _SANDBOXSTDINWRITERESPONSE._serialized_end=43096
  _SANDBOXTAG._serialized_start=43098
  _SANDBOXTAG._serialized_end=43147
  _SANDBOXTAGSSETREQUEST._serialized_start=43149
  _SANDBOXTAGSSETREQUEST._serialized_end=43258
  _SANDBOXTERMINATEREQUEST._serialized_start=43260
  _SANDBOXTERMINATEREQUEST._serialized_end=43305
  _SANDBOXTERMINATERESPONSE._serialized_start=43307
  _SANDBOXTERMINATERESPONSE._serialized_end=43387
  _SANDBOXWAITREQUEST._serialized_start=43389
  _SANDBOXWAITREQUEST._serialized_end=43446
  _SANDBOXWAITRESPONSE._serialized_start=43448
  _SANDBOXWAITRESPONSE._serialized_end=43514
  _SCHEDULE._serialized_start=43517
  _SCHEDULE._serialized_end=43805
  _SCHEDULE_CRON._serialized_start=43623
  _SCHEDULE_CRON._serialized_end=43668
  _SCHEDULE_PERIOD._serialized_start=43670
  _SCHEDULE_PERIOD._serialized_end=43787
  _SCHEDULERPLACEMENT._serialized_start=43808
  _SCHEDULERPLACEMENT._serialized_end=43946
  _SECRETCREATEREQUEST._serialized_start=43949
  _SECRETCREATEREQUEST._serialized_end=44157
  _SECRETCREATEREQUEST_ENVDICTENTRY._serialized_start=44111
  _SECRETCREATEREQUEST_ENVDICTENTRY._serialized_end=44157
  _SECRETCREATERESPONSE._serialized_start=44159
  _SECRETCREATERESPONSE._serialized_end=44200
  _SECRETDELETEREQUEST._serialized_start=44202
  _SECRETDELETEREQUEST._serialized_end=44242
  _SECRETGETORCREATEREQUEST._serialized_start=44245
  _SECRETGETORCREATEREQUEST._serialized_end=44604
  _SECRETGETORCREATEREQUEST_ENVDICTENTRY._serialized_start=44111
  _SECRETGETORCREATEREQUEST_ENVDICTENTRY._serialized_end=44157
  _SECRETGETORCREATERESPONSE._serialized_start=44606
  _SECRETGETORCREATERESPONSE._serialized_end=44652
  _SECRETLISTITEM._serialized_start=44654
  _SECRETLISTITEM._serialized_end=44772
  _SECRETLISTREQUEST._serialized_start=44774
  _SECRETLISTREQUEST._serialized_end=44819
  _SECRETLISTRESPONSE._serialized_start=44821
  _SECRETLISTRESPONSE._serialized_end=44912
  _SHAREDVOLUMEDELETEREQUEST._serialized_start=44914
  _SHAREDVOLUMEDELETEREQUEST._serialized_end=44967
  _SHAREDVOLUMEGETFILEREQUEST._serialized_start=44969
  _SHAREDVOLUMEGETFILEREQUEST._serialized_end=45037
  _SHAREDVOLUMEGETFILERESPONSE._serialized_start=45039
  _SHAREDVOLUMEGETFILERESPONSE._serialized_end=45122
  _SHAREDVOLUMEGETORCREATEREQUEST._serialized_start=45125
  _SHAREDVOLUMEGETORCREATEREQUEST._serialized_end=45348
  _SHAREDVOLUMEGETORCREATERESPONSE._serialized_start=45350
  _SHAREDVOLUMEGETORCREATERESPONSE._serialized_end=45409
  _SHAREDVOLUMEHEARTBEATREQUEST._serialized_start=45411
  _SHAREDVOLUMEHEARTBEATREQUEST._serialized_end=45467
  _SHAREDVOLUMELISTFILESREQUEST._serialized_start=45469
  _SHAREDVOLUMELISTFILESREQUEST._serialized_end=45539
  _SHAREDVOLUMELISTFILESRESPONSE._serialized_start=45541
  _SHAREDVOLUMELISTFILESRESPONSE._serialized_end=45614
  _SHAREDVOLUMELISTITEM._serialized_start=45617
  _SHAREDVOLUMELISTITEM._serialized_end=45753
  _SHAREDVOLUMELISTREQUEST._serialized_start=45755
  _SHAREDVOLUMELISTREQUEST._serialized_end=45806
  _SHAREDVOLUMELISTRESPONSE._serialized_start=45808
  _SHAREDVOLUMELISTRESPONSE._serialized_end=45911
  _SHAREDVOLUMEMOUNT._serialized_start=45914
  _SHAREDVOLUMEMOUNT._serialized_end=46060
  _SHAREDVOLUMEPUTFILEREQUEST._serialized_start=46063
  _SHAREDVOLUMEPUTFILEREQUEST._serialized_end=46230
  _SHAREDVOLUMEPUTFILERESPONSE._serialized_start=46232
  _SHAREDVOLUMEPUTFILERESPONSE._serialized_end=46277
  _SHAREDVOLUMEREMOVEFILEREQUEST._serialized_start=46279
  _SHAREDVOLUMEREMOVEFILEREQUEST._serialized_end=46375
  _SYSTEMERRORMESSAGE._serialized_start=46377
  _SYSTEMERRORMESSAGE._serialized_end=46471
  _TASKCLUSTERHELLOREQUEST._serialized_start=46473
  _TASKCLUSTERHELLOREQUEST._serialized_end=46537
  _TASKCLUSTERHELLORESPONSE._serialized_start=46539
  _TASKCLUSTERHELLORESPONSE._serialized_end=46630
  _TASKCURRENTINPUTSRESPONSE._serialized_start=46632
  _TASKCURRENTINPUTSRESPONSE._serialized_end=46678
  _TASKINFO._serialized_start=46681
  _TASKINFO._serialized_end=46848
  _TASKLISTREQUEST._serialized_start=46850
  _TASKLISTREQUEST._serialized_end=46893
  _TASKLISTRESPONSE._serialized_start=46895
  _TASKLISTRESPONSE._serialized_end=46953
  _TASKLOGS._serialized_start=46956
  _TASKLOGS._serialized_end=47216
  _TASKLOGSBATCH._serialized_start=47219
  _TASKLOGSBATCH._serialized_end=47443
  _TASKPROGRESS._serialized_start=47445
  _TASKPROGRESS._serialized_end=47557
  _TASKRESULTREQUEST._serialized_start=47559
  _TASKRESULTREQUEST._serialized_end=47623
  _TASKSTATS._serialized_start=47625
  _TASKSTATS._serialized_end=47714
  _TASKTEMPLATE._serialized_start=47717
  _TASKTEMPLATE._serialized_end=47869
  _TOKENFLOWCREATEREQUEST._serialized_start=47871
  _TOKENFLOWCREATEREQUEST._serialized_end=47957
  _TOKENFLOWCREATERESPONSE._serialized_start=47959
  _TOKENFLOWCREATERESPONSE._serialized_end=48059
  _TOKENFLOWWAITREQUEST._serialized_start=48061
  _TOKENFLOWWAITREQUEST._serialized_end=48144
  _TOKENFLOWWAITRESPONSE._serialized_start=48146
  _TOKENFLOWWAITRESPONSE._serialized_end=48254
  _TUNNELDATA._serialized_start=48257
  _TUNNELDATA._serialized_end=48425
  _TUNNELSTARTREQUEST._serialized_start=48427
  _TUNNELSTARTREQUEST._serialized_end=48550
  _TUNNELSTARTRESPONSE._serialized_start=48553
  _TUNNELSTARTRESPONSE._serialized_end=48706
  _TUNNELSTOPREQUEST._serialized_start=48708
  _TUNNELSTOPREQUEST._serialized_end=48741
  _TUNNELSTOPRESPONSE._serialized_start=48743
  _TUNNELSTOPRESPONSE._serialized_end=48779
  _VOLUMECOMMITREQUEST._serialized_start=48781
  _VOLUMECOMMITREQUEST._serialized_end=48827
  _VOLUMECOMMITRESPONSE._serialized_start=48829
  _VOLUMECOMMITRESPONSE._serialized_end=48872
  _VOLUMECOPYFILES2REQUEST._serialized_start=48874
  _VOLUMECOPYFILES2REQUEST._serialized_end=48974
  _VOLUMECOPYFILESREQUEST._serialized_start=48976
  _VOLUMECOPYFILESREQUEST._serialized_end=49075
  _VOLUMEDELETEREQUEST._serialized_start=49077
  _VOLUMEDELETEREQUEST._serialized_end=49147
  _VOLUMEGETFILE2REQUEST._serialized_start=49149
  _VOLUMEGETFILE2REQUEST._serialized_end=49233
  _VOLUMEGETFILE2RESPONSE._serialized_start=49235
  _VOLUMEGETFILE2RESPONSE._serialized_end=49319
  _VOLUMEGETFILEREQUEST._serialized_start=49321
  _VOLUMEGETFILEREQUEST._serialized_end=49404
  _VOLUMEGETFILERESPONSE._serialized_start=49406
  _VOLUMEGETFILERESPONSE._serialized_end=49525
  _VOLUMEGETORCREATEREQUEST._serialized_start=49528
  _VOLUMEGETORCREATEREQUEST._serialized_end=49793
  _VOLUMEGETORCREATERESPONSE._serialized_start=49796
  _VOLUMEGETORCREATERESPONSE._serialized_end=49938
  _VOLUMEHEARTBEATREQUEST._serialized_start=49940
  _VOLUMEHEARTBEATREQUEST._serialized_end=49983
  _VOLUMELISTFILES2REQUEST._serialized_start=49985
  _VOLUMELISTFILES2REQUEST._serialized_end=50104
  _VOLUMELISTFILES2RESPONSE._serialized_start=50106
  _VOLUMELISTFILES2RESPONSE._serialized_end=50174
  _VOLUMELISTFILESREQUEST._serialized_start=50176
  _VOLUMELISTFILESREQUEST._serialized_end=50294
  _VOLUMELISTFILESRESPONSE._serialized_start=50296
  _VOLUMELISTFILESRESPONSE._serialized_end=50363
  _VOLUMELISTITEM._serialized_start=50365
  _VOLUMELISTITEM._serialized_end=50435
  _VOLUMELISTREQUEST._serialized_start=50437
  _VOLUMELISTREQUEST._serialized_end=50482
  _VOLUMELISTRESPONSE._serialized_start=50484
  _VOLUMELISTRESPONSE._serialized_end=50575
  _VOLUMEMETADATA._serialized_start=50577
  _VOLUMEMETADATA._serialized_end=50641
  _VOLUMEMOUNT._serialized_start=50643
  _VOLUMEMOUNT._serialized_end=50748
  _VOLUMEPUTFILES2REQUEST._serialized_start=50751
  _VOLUMEPUTFILES2REQUEST._serialized_end=51097
  _VOLUMEPUTFILES2REQUEST_FILE._serialized_start=50897
  _VOLUMEPUTFILES2REQUEST_FILE._serialized_end=51019
  _VOLUMEPUTFILES2REQUEST_BLOCK._serialized_start=51021
  _VOLUMEPUTFILES2REQUEST_BLOCK._serialized_end=51097
  _VOLUMEPUTFILES2RESPONSE._serialized_start=51100
  _VOLUMEPUTFILES2RESPONSE._serialized_end=51275
  _VOLUMEPUTFILES2RESPONSE_MISSINGBLOCK._serialized_start=51203
  _VOLUMEPUTFILES2RESPONSE_MISSINGBLOCK._serialized_end=51275
  _VOLUMEPUTFILESREQUEST._serialized_start=51277
  _VOLUMEPUTFILESREQUEST._serialized_end=51402
  _VOLUMERELOADREQUEST._serialized_start=51404
  _VOLUMERELOADREQUEST._serialized_end=51444
  _VOLUMEREMOVEFILE2REQUEST._serialized_start=51446
  _VOLUMEREMOVEFILE2REQUEST._serialized_end=51530
  _VOLUMEREMOVEFILEREQUEST._serialized_start=51532
  _VOLUMEREMOVEFILEREQUEST._serialized_end=51615
  _VOLUMERENAMEREQUEST._serialized_start=51617
  _VOLUMERENAMEREQUEST._serialized_end=51677
  _WARNING._serialized_start=51680
  _WARNING._serialized_end=51912
  _WARNING_WARNINGTYPE._serialized_start=51758
  _WARNING_WARNINGTYPE._serialized_end=51912
  _WEBURLINFO._serialized_start=51914
  _WEBURLINFO._serialized_end=51996
  _WEBHOOKCONFIG._serialized_start=51999
  _WEBHOOKCONFIG._serialized_end=52324
  _WORKSPACENAMELOOKUPRESPONSE._serialized_start=52326
  _WORKSPACENAMELOOKUPRESPONSE._serialized_end=52401
  _MODALCLIENT._serialized_start=57625
  _MODALCLIENT._serialized_end=71440
# @@protoc_insertion_point(module_scope)
