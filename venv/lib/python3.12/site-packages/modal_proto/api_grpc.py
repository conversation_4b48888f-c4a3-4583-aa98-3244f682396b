# Generated by the Protocol Buffers compiler. DO NOT EDIT!
# source: modal_proto/api.proto
# plugin: grpclib.plugin.main
import abc
import typing

import grpclib.const
import grpclib.client
if typing.TYPE_CHECKING:
    import grpclib.server

import modal_proto.options_pb2
import google.protobuf.empty_pb2
import google.protobuf.struct_pb2
import google.protobuf.wrappers_pb2
import modal_proto.api_pb2


class ModalClientBase(abc.ABC):

    @abc.abstractmethod
    async def AppClientDisconnect(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppClientDisconnectRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def AppCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppCreateRequest, modal_proto.api_pb2.AppCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppDeploy(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppDeployRequest, modal_proto.api_pb2.AppDeployResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppDeploymentHistory(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppDeploymentHistoryRequest, modal_proto.api_pb2.AppDeploymentHistoryResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppGetByDeploymentName(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppGetByDeploymentNameRequest, modal_proto.api_pb2.AppGetByDeploymentNameResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppGetLayout(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppGetLayoutRequest, modal_proto.api_pb2.AppGetLayoutResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppGetLogs(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppGetLogsRequest, modal_proto.api_pb2.TaskLogsBatch]') -> None:
        pass

    @abc.abstractmethod
    async def AppGetObjects(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppGetObjectsRequest, modal_proto.api_pb2.AppGetObjectsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppGetOrCreateRequest, modal_proto.api_pb2.AppGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppHeartbeat(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppHeartbeatRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def AppList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppListRequest, modal_proto.api_pb2.AppListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppLookup(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppLookupRequest, modal_proto.api_pb2.AppLookupResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppPublish(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppPublishRequest, modal_proto.api_pb2.AppPublishResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AppRollback(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppRollbackRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def AppSetObjects(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppSetObjectsRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def AppStop(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AppStopRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def AttemptAwait(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AttemptAwaitRequest, modal_proto.api_pb2.AttemptAwaitResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AttemptRetry(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AttemptRetryRequest, modal_proto.api_pb2.AttemptRetryResponse]') -> None:
        pass

    @abc.abstractmethod
    async def AttemptStart(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.AttemptStartRequest, modal_proto.api_pb2.AttemptStartResponse]') -> None:
        pass

    @abc.abstractmethod
    async def BlobCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.BlobCreateRequest, modal_proto.api_pb2.BlobCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def BlobGet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.BlobGetRequest, modal_proto.api_pb2.BlobGetResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ClassCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ClassCreateRequest, modal_proto.api_pb2.ClassCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ClassGet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ClassGetRequest, modal_proto.api_pb2.ClassGetResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ClientHello(self, stream: 'grpclib.server.Stream[google.protobuf.empty_pb2.Empty, modal_proto.api_pb2.ClientHelloResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ClusterGet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ClusterGetRequest, modal_proto.api_pb2.ClusterGetResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ClusterList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ClusterListRequest, modal_proto.api_pb2.ClusterListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerCheckpoint(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerCheckpointRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerExec(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerExecRequest, modal_proto.api_pb2.ContainerExecResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerExecGetOutput(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerExecGetOutputRequest, modal_proto.api_pb2.RuntimeOutputBatch]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerExecPutInput(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerExecPutInputRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerExecWait(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerExecWaitRequest, modal_proto.api_pb2.ContainerExecWaitResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerFilesystemExec(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerFilesystemExecRequest, modal_proto.api_pb2.ContainerFilesystemExecResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerFilesystemExecGetOutput(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerFilesystemExecGetOutputRequest, modal_proto.api_pb2.FilesystemRuntimeOutputBatch]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerHeartbeat(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerHeartbeatRequest, modal_proto.api_pb2.ContainerHeartbeatResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerHello(self, stream: 'grpclib.server.Stream[google.protobuf.empty_pb2.Empty, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerLog(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerLogRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def ContainerStop(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ContainerStopRequest, modal_proto.api_pb2.ContainerStopResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DictClear(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictClearRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def DictContains(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictContainsRequest, modal_proto.api_pb2.DictContainsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DictContents(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictContentsRequest, modal_proto.api_pb2.DictEntry]') -> None:
        pass

    @abc.abstractmethod
    async def DictDelete(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictDeleteRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def DictGet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictGetRequest, modal_proto.api_pb2.DictGetResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DictGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictGetOrCreateRequest, modal_proto.api_pb2.DictGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DictHeartbeat(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictHeartbeatRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def DictLen(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictLenRequest, modal_proto.api_pb2.DictLenResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DictList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictListRequest, modal_proto.api_pb2.DictListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DictPop(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictPopRequest, modal_proto.api_pb2.DictPopResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DictUpdate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DictUpdateRequest, modal_proto.api_pb2.DictUpdateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DomainCertificateVerify(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DomainCertificateVerifyRequest, modal_proto.api_pb2.DomainCertificateVerifyResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DomainCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DomainCreateRequest, modal_proto.api_pb2.DomainCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def DomainList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.DomainListRequest, modal_proto.api_pb2.DomainListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def EnvironmentCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.EnvironmentCreateRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def EnvironmentDelete(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.EnvironmentDeleteRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def EnvironmentGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.EnvironmentGetOrCreateRequest, modal_proto.api_pb2.EnvironmentGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def EnvironmentList(self, stream: 'grpclib.server.Stream[google.protobuf.empty_pb2.Empty, modal_proto.api_pb2.EnvironmentListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def EnvironmentUpdate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.EnvironmentUpdateRequest, modal_proto.api_pb2.EnvironmentListItem]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionAsyncInvoke(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionAsyncInvokeRequest, modal_proto.api_pb2.FunctionAsyncInvokeResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionBindParams(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionBindParamsRequest, modal_proto.api_pb2.FunctionBindParamsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionCallCancel(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionCallCancelRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionCallGetDataIn(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionCallGetDataRequest, modal_proto.api_pb2.DataChunk]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionCallGetDataOut(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionCallGetDataRequest, modal_proto.api_pb2.DataChunk]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionCallList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionCallListRequest, modal_proto.api_pb2.FunctionCallListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionCallPutDataOut(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionCallPutDataRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionCreateRequest, modal_proto.api_pb2.FunctionCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionGet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionGetRequest, modal_proto.api_pb2.FunctionGetResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionGetCallGraph(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionGetCallGraphRequest, modal_proto.api_pb2.FunctionGetCallGraphResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionGetCurrentStats(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionGetCurrentStatsRequest, modal_proto.api_pb2.FunctionStats]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionGetDynamicConcurrency(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionGetDynamicConcurrencyRequest, modal_proto.api_pb2.FunctionGetDynamicConcurrencyResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionGetInputs(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionGetInputsRequest, modal_proto.api_pb2.FunctionGetInputsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionGetOutputs(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionGetOutputsRequest, modal_proto.api_pb2.FunctionGetOutputsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionGetSerialized(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionGetSerializedRequest, modal_proto.api_pb2.FunctionGetSerializedResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionMap(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionMapRequest, modal_proto.api_pb2.FunctionMapResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionPrecreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionPrecreateRequest, modal_proto.api_pb2.FunctionPrecreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionPutInputs(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionPutInputsRequest, modal_proto.api_pb2.FunctionPutInputsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionPutOutputs(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionPutOutputsRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionRetryInputs(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionRetryInputsRequest, modal_proto.api_pb2.FunctionRetryInputsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionStartPtyShell(self, stream: 'grpclib.server.Stream[google.protobuf.empty_pb2.Empty, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def FunctionUpdateSchedulingParams(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.FunctionUpdateSchedulingParamsRequest, modal_proto.api_pb2.FunctionUpdateSchedulingParamsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ImageFromId(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ImageFromIdRequest, modal_proto.api_pb2.ImageFromIdResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ImageGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ImageGetOrCreateRequest, modal_proto.api_pb2.ImageGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ImageJoinStreaming(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ImageJoinStreamingRequest, modal_proto.api_pb2.ImageJoinStreamingResponse]') -> None:
        pass

    @abc.abstractmethod
    async def MountGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.MountGetOrCreateRequest, modal_proto.api_pb2.MountGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def MountPutFile(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.MountPutFileRequest, modal_proto.api_pb2.MountPutFileResponse]') -> None:
        pass

    @abc.abstractmethod
    async def NotebookKernelPublishResults(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.NotebookKernelPublishResultsRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def ProxyAddIp(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ProxyAddIpRequest, modal_proto.api_pb2.ProxyAddIpResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ProxyCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ProxyCreateRequest, modal_proto.api_pb2.ProxyCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ProxyDelete(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ProxyDeleteRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def ProxyGet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ProxyGetRequest, modal_proto.api_pb2.ProxyGetResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ProxyGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ProxyGetOrCreateRequest, modal_proto.api_pb2.ProxyGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ProxyList(self, stream: 'grpclib.server.Stream[google.protobuf.empty_pb2.Empty, modal_proto.api_pb2.ProxyListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def ProxyRemoveIp(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.ProxyRemoveIpRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def QueueClear(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueueClearRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def QueueDelete(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueueDeleteRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def QueueGet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueueGetRequest, modal_proto.api_pb2.QueueGetResponse]') -> None:
        pass

    @abc.abstractmethod
    async def QueueGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueueGetOrCreateRequest, modal_proto.api_pb2.QueueGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def QueueHeartbeat(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueueHeartbeatRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def QueueLen(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueueLenRequest, modal_proto.api_pb2.QueueLenResponse]') -> None:
        pass

    @abc.abstractmethod
    async def QueueList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueueListRequest, modal_proto.api_pb2.QueueListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def QueueNextItems(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueueNextItemsRequest, modal_proto.api_pb2.QueueNextItemsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def QueuePut(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.QueuePutRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxCreateRequest, modal_proto.api_pb2.SandboxCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxGetLogs(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxGetLogsRequest, modal_proto.api_pb2.TaskLogsBatch]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxGetResourceUsage(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxGetResourceUsageRequest, modal_proto.api_pb2.SandboxGetResourceUsageResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxGetTaskId(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxGetTaskIdRequest, modal_proto.api_pb2.SandboxGetTaskIdResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxGetTunnels(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxGetTunnelsRequest, modal_proto.api_pb2.SandboxGetTunnelsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxListRequest, modal_proto.api_pb2.SandboxListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxRestore(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxRestoreRequest, modal_proto.api_pb2.SandboxRestoreResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxSnapshot(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxSnapshotRequest, modal_proto.api_pb2.SandboxSnapshotResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxSnapshotFs(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxSnapshotFsRequest, modal_proto.api_pb2.SandboxSnapshotFsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxSnapshotGet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxSnapshotGetRequest, modal_proto.api_pb2.SandboxSnapshotGetResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxSnapshotWait(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxSnapshotWaitRequest, modal_proto.api_pb2.SandboxSnapshotWaitResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxStdinWrite(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxStdinWriteRequest, modal_proto.api_pb2.SandboxStdinWriteResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxTagsSet(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxTagsSetRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxTerminate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxTerminateRequest, modal_proto.api_pb2.SandboxTerminateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SandboxWait(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SandboxWaitRequest, modal_proto.api_pb2.SandboxWaitResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SecretDelete(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SecretDeleteRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def SecretGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SecretGetOrCreateRequest, modal_proto.api_pb2.SecretGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SecretList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SecretListRequest, modal_proto.api_pb2.SecretListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumeDelete(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumeDeleteRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumeGetFile(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumeGetFileRequest, modal_proto.api_pb2.SharedVolumeGetFileResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumeGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumeGetOrCreateRequest, modal_proto.api_pb2.SharedVolumeGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumeHeartbeat(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumeHeartbeatRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumeList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumeListRequest, modal_proto.api_pb2.SharedVolumeListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumeListFiles(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumeListFilesRequest, modal_proto.api_pb2.SharedVolumeListFilesResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumeListFilesStream(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumeListFilesRequest, modal_proto.api_pb2.SharedVolumeListFilesResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumePutFile(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumePutFileRequest, modal_proto.api_pb2.SharedVolumePutFileResponse]') -> None:
        pass

    @abc.abstractmethod
    async def SharedVolumeRemoveFile(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.SharedVolumeRemoveFileRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def TaskClusterHello(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.TaskClusterHelloRequest, modal_proto.api_pb2.TaskClusterHelloResponse]') -> None:
        pass

    @abc.abstractmethod
    async def TaskCurrentInputs(self, stream: 'grpclib.server.Stream[google.protobuf.empty_pb2.Empty, modal_proto.api_pb2.TaskCurrentInputsResponse]') -> None:
        pass

    @abc.abstractmethod
    async def TaskList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.TaskListRequest, modal_proto.api_pb2.TaskListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def TaskResult(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.TaskResultRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def TokenFlowCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.TokenFlowCreateRequest, modal_proto.api_pb2.TokenFlowCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def TokenFlowWait(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.TokenFlowWaitRequest, modal_proto.api_pb2.TokenFlowWaitResponse]') -> None:
        pass

    @abc.abstractmethod
    async def TunnelStart(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.TunnelStartRequest, modal_proto.api_pb2.TunnelStartResponse]') -> None:
        pass

    @abc.abstractmethod
    async def TunnelStop(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.TunnelStopRequest, modal_proto.api_pb2.TunnelStopResponse]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeCommit(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeCommitRequest, modal_proto.api_pb2.VolumeCommitResponse]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeCopyFiles(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeCopyFilesRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeCopyFiles2(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeCopyFiles2Request, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeDelete(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeDeleteRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeGetFile(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeGetFileRequest, modal_proto.api_pb2.VolumeGetFileResponse]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeGetFile2(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeGetFile2Request, modal_proto.api_pb2.VolumeGetFile2Response]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeGetOrCreate(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeGetOrCreateRequest, modal_proto.api_pb2.VolumeGetOrCreateResponse]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeHeartbeat(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeHeartbeatRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeList(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeListRequest, modal_proto.api_pb2.VolumeListResponse]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeListFiles(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeListFilesRequest, modal_proto.api_pb2.VolumeListFilesResponse]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeListFiles2(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeListFiles2Request, modal_proto.api_pb2.VolumeListFiles2Response]') -> None:
        pass

    @abc.abstractmethod
    async def VolumePutFiles(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumePutFilesRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def VolumePutFiles2(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumePutFiles2Request, modal_proto.api_pb2.VolumePutFiles2Response]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeReload(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeReloadRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeRemoveFile(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeRemoveFileRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeRemoveFile2(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeRemoveFile2Request, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def VolumeRename(self, stream: 'grpclib.server.Stream[modal_proto.api_pb2.VolumeRenameRequest, google.protobuf.empty_pb2.Empty]') -> None:
        pass

    @abc.abstractmethod
    async def WorkspaceNameLookup(self, stream: 'grpclib.server.Stream[google.protobuf.empty_pb2.Empty, modal_proto.api_pb2.WorkspaceNameLookupResponse]') -> None:
        pass

    def __mapping__(self) -> typing.Dict[str, grpclib.const.Handler]:
        return {
            '/modal.client.ModalClient/AppClientDisconnect': grpclib.const.Handler(
                self.AppClientDisconnect,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppClientDisconnectRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/AppCreate': grpclib.const.Handler(
                self.AppCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppCreateRequest,
                modal_proto.api_pb2.AppCreateResponse,
            ),
            '/modal.client.ModalClient/AppDeploy': grpclib.const.Handler(
                self.AppDeploy,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppDeployRequest,
                modal_proto.api_pb2.AppDeployResponse,
            ),
            '/modal.client.ModalClient/AppDeploymentHistory': grpclib.const.Handler(
                self.AppDeploymentHistory,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppDeploymentHistoryRequest,
                modal_proto.api_pb2.AppDeploymentHistoryResponse,
            ),
            '/modal.client.ModalClient/AppGetByDeploymentName': grpclib.const.Handler(
                self.AppGetByDeploymentName,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppGetByDeploymentNameRequest,
                modal_proto.api_pb2.AppGetByDeploymentNameResponse,
            ),
            '/modal.client.ModalClient/AppGetLayout': grpclib.const.Handler(
                self.AppGetLayout,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppGetLayoutRequest,
                modal_proto.api_pb2.AppGetLayoutResponse,
            ),
            '/modal.client.ModalClient/AppGetLogs': grpclib.const.Handler(
                self.AppGetLogs,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.AppGetLogsRequest,
                modal_proto.api_pb2.TaskLogsBatch,
            ),
            '/modal.client.ModalClient/AppGetObjects': grpclib.const.Handler(
                self.AppGetObjects,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppGetObjectsRequest,
                modal_proto.api_pb2.AppGetObjectsResponse,
            ),
            '/modal.client.ModalClient/AppGetOrCreate': grpclib.const.Handler(
                self.AppGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppGetOrCreateRequest,
                modal_proto.api_pb2.AppGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/AppHeartbeat': grpclib.const.Handler(
                self.AppHeartbeat,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppHeartbeatRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/AppList': grpclib.const.Handler(
                self.AppList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppListRequest,
                modal_proto.api_pb2.AppListResponse,
            ),
            '/modal.client.ModalClient/AppLookup': grpclib.const.Handler(
                self.AppLookup,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppLookupRequest,
                modal_proto.api_pb2.AppLookupResponse,
            ),
            '/modal.client.ModalClient/AppPublish': grpclib.const.Handler(
                self.AppPublish,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppPublishRequest,
                modal_proto.api_pb2.AppPublishResponse,
            ),
            '/modal.client.ModalClient/AppRollback': grpclib.const.Handler(
                self.AppRollback,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppRollbackRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/AppSetObjects': grpclib.const.Handler(
                self.AppSetObjects,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppSetObjectsRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/AppStop': grpclib.const.Handler(
                self.AppStop,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AppStopRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/AttemptAwait': grpclib.const.Handler(
                self.AttemptAwait,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AttemptAwaitRequest,
                modal_proto.api_pb2.AttemptAwaitResponse,
            ),
            '/modal.client.ModalClient/AttemptRetry': grpclib.const.Handler(
                self.AttemptRetry,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AttemptRetryRequest,
                modal_proto.api_pb2.AttemptRetryResponse,
            ),
            '/modal.client.ModalClient/AttemptStart': grpclib.const.Handler(
                self.AttemptStart,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.AttemptStartRequest,
                modal_proto.api_pb2.AttemptStartResponse,
            ),
            '/modal.client.ModalClient/BlobCreate': grpclib.const.Handler(
                self.BlobCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.BlobCreateRequest,
                modal_proto.api_pb2.BlobCreateResponse,
            ),
            '/modal.client.ModalClient/BlobGet': grpclib.const.Handler(
                self.BlobGet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.BlobGetRequest,
                modal_proto.api_pb2.BlobGetResponse,
            ),
            '/modal.client.ModalClient/ClassCreate': grpclib.const.Handler(
                self.ClassCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ClassCreateRequest,
                modal_proto.api_pb2.ClassCreateResponse,
            ),
            '/modal.client.ModalClient/ClassGet': grpclib.const.Handler(
                self.ClassGet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ClassGetRequest,
                modal_proto.api_pb2.ClassGetResponse,
            ),
            '/modal.client.ModalClient/ClientHello': grpclib.const.Handler(
                self.ClientHello,
                grpclib.const.Cardinality.UNARY_UNARY,
                google.protobuf.empty_pb2.Empty,
                modal_proto.api_pb2.ClientHelloResponse,
            ),
            '/modal.client.ModalClient/ClusterGet': grpclib.const.Handler(
                self.ClusterGet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ClusterGetRequest,
                modal_proto.api_pb2.ClusterGetResponse,
            ),
            '/modal.client.ModalClient/ClusterList': grpclib.const.Handler(
                self.ClusterList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ClusterListRequest,
                modal_proto.api_pb2.ClusterListResponse,
            ),
            '/modal.client.ModalClient/ContainerCheckpoint': grpclib.const.Handler(
                self.ContainerCheckpoint,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ContainerCheckpointRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/ContainerExec': grpclib.const.Handler(
                self.ContainerExec,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ContainerExecRequest,
                modal_proto.api_pb2.ContainerExecResponse,
            ),
            '/modal.client.ModalClient/ContainerExecGetOutput': grpclib.const.Handler(
                self.ContainerExecGetOutput,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.ContainerExecGetOutputRequest,
                modal_proto.api_pb2.RuntimeOutputBatch,
            ),
            '/modal.client.ModalClient/ContainerExecPutInput': grpclib.const.Handler(
                self.ContainerExecPutInput,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ContainerExecPutInputRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/ContainerExecWait': grpclib.const.Handler(
                self.ContainerExecWait,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ContainerExecWaitRequest,
                modal_proto.api_pb2.ContainerExecWaitResponse,
            ),
            '/modal.client.ModalClient/ContainerFilesystemExec': grpclib.const.Handler(
                self.ContainerFilesystemExec,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ContainerFilesystemExecRequest,
                modal_proto.api_pb2.ContainerFilesystemExecResponse,
            ),
            '/modal.client.ModalClient/ContainerFilesystemExecGetOutput': grpclib.const.Handler(
                self.ContainerFilesystemExecGetOutput,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.ContainerFilesystemExecGetOutputRequest,
                modal_proto.api_pb2.FilesystemRuntimeOutputBatch,
            ),
            '/modal.client.ModalClient/ContainerHeartbeat': grpclib.const.Handler(
                self.ContainerHeartbeat,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ContainerHeartbeatRequest,
                modal_proto.api_pb2.ContainerHeartbeatResponse,
            ),
            '/modal.client.ModalClient/ContainerHello': grpclib.const.Handler(
                self.ContainerHello,
                grpclib.const.Cardinality.UNARY_UNARY,
                google.protobuf.empty_pb2.Empty,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/ContainerLog': grpclib.const.Handler(
                self.ContainerLog,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ContainerLogRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/ContainerStop': grpclib.const.Handler(
                self.ContainerStop,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ContainerStopRequest,
                modal_proto.api_pb2.ContainerStopResponse,
            ),
            '/modal.client.ModalClient/DictClear': grpclib.const.Handler(
                self.DictClear,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictClearRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/DictContains': grpclib.const.Handler(
                self.DictContains,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictContainsRequest,
                modal_proto.api_pb2.DictContainsResponse,
            ),
            '/modal.client.ModalClient/DictContents': grpclib.const.Handler(
                self.DictContents,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.DictContentsRequest,
                modal_proto.api_pb2.DictEntry,
            ),
            '/modal.client.ModalClient/DictDelete': grpclib.const.Handler(
                self.DictDelete,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictDeleteRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/DictGet': grpclib.const.Handler(
                self.DictGet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictGetRequest,
                modal_proto.api_pb2.DictGetResponse,
            ),
            '/modal.client.ModalClient/DictGetOrCreate': grpclib.const.Handler(
                self.DictGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictGetOrCreateRequest,
                modal_proto.api_pb2.DictGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/DictHeartbeat': grpclib.const.Handler(
                self.DictHeartbeat,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictHeartbeatRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/DictLen': grpclib.const.Handler(
                self.DictLen,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictLenRequest,
                modal_proto.api_pb2.DictLenResponse,
            ),
            '/modal.client.ModalClient/DictList': grpclib.const.Handler(
                self.DictList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictListRequest,
                modal_proto.api_pb2.DictListResponse,
            ),
            '/modal.client.ModalClient/DictPop': grpclib.const.Handler(
                self.DictPop,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictPopRequest,
                modal_proto.api_pb2.DictPopResponse,
            ),
            '/modal.client.ModalClient/DictUpdate': grpclib.const.Handler(
                self.DictUpdate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DictUpdateRequest,
                modal_proto.api_pb2.DictUpdateResponse,
            ),
            '/modal.client.ModalClient/DomainCertificateVerify': grpclib.const.Handler(
                self.DomainCertificateVerify,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DomainCertificateVerifyRequest,
                modal_proto.api_pb2.DomainCertificateVerifyResponse,
            ),
            '/modal.client.ModalClient/DomainCreate': grpclib.const.Handler(
                self.DomainCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DomainCreateRequest,
                modal_proto.api_pb2.DomainCreateResponse,
            ),
            '/modal.client.ModalClient/DomainList': grpclib.const.Handler(
                self.DomainList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.DomainListRequest,
                modal_proto.api_pb2.DomainListResponse,
            ),
            '/modal.client.ModalClient/EnvironmentCreate': grpclib.const.Handler(
                self.EnvironmentCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.EnvironmentCreateRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/EnvironmentDelete': grpclib.const.Handler(
                self.EnvironmentDelete,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.EnvironmentDeleteRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/EnvironmentGetOrCreate': grpclib.const.Handler(
                self.EnvironmentGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.EnvironmentGetOrCreateRequest,
                modal_proto.api_pb2.EnvironmentGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/EnvironmentList': grpclib.const.Handler(
                self.EnvironmentList,
                grpclib.const.Cardinality.UNARY_UNARY,
                google.protobuf.empty_pb2.Empty,
                modal_proto.api_pb2.EnvironmentListResponse,
            ),
            '/modal.client.ModalClient/EnvironmentUpdate': grpclib.const.Handler(
                self.EnvironmentUpdate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.EnvironmentUpdateRequest,
                modal_proto.api_pb2.EnvironmentListItem,
            ),
            '/modal.client.ModalClient/FunctionAsyncInvoke': grpclib.const.Handler(
                self.FunctionAsyncInvoke,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionAsyncInvokeRequest,
                modal_proto.api_pb2.FunctionAsyncInvokeResponse,
            ),
            '/modal.client.ModalClient/FunctionBindParams': grpclib.const.Handler(
                self.FunctionBindParams,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionBindParamsRequest,
                modal_proto.api_pb2.FunctionBindParamsResponse,
            ),
            '/modal.client.ModalClient/FunctionCallCancel': grpclib.const.Handler(
                self.FunctionCallCancel,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionCallCancelRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/FunctionCallGetDataIn': grpclib.const.Handler(
                self.FunctionCallGetDataIn,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.FunctionCallGetDataRequest,
                modal_proto.api_pb2.DataChunk,
            ),
            '/modal.client.ModalClient/FunctionCallGetDataOut': grpclib.const.Handler(
                self.FunctionCallGetDataOut,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.FunctionCallGetDataRequest,
                modal_proto.api_pb2.DataChunk,
            ),
            '/modal.client.ModalClient/FunctionCallList': grpclib.const.Handler(
                self.FunctionCallList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionCallListRequest,
                modal_proto.api_pb2.FunctionCallListResponse,
            ),
            '/modal.client.ModalClient/FunctionCallPutDataOut': grpclib.const.Handler(
                self.FunctionCallPutDataOut,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionCallPutDataRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/FunctionCreate': grpclib.const.Handler(
                self.FunctionCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionCreateRequest,
                modal_proto.api_pb2.FunctionCreateResponse,
            ),
            '/modal.client.ModalClient/FunctionGet': grpclib.const.Handler(
                self.FunctionGet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionGetRequest,
                modal_proto.api_pb2.FunctionGetResponse,
            ),
            '/modal.client.ModalClient/FunctionGetCallGraph': grpclib.const.Handler(
                self.FunctionGetCallGraph,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionGetCallGraphRequest,
                modal_proto.api_pb2.FunctionGetCallGraphResponse,
            ),
            '/modal.client.ModalClient/FunctionGetCurrentStats': grpclib.const.Handler(
                self.FunctionGetCurrentStats,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionGetCurrentStatsRequest,
                modal_proto.api_pb2.FunctionStats,
            ),
            '/modal.client.ModalClient/FunctionGetDynamicConcurrency': grpclib.const.Handler(
                self.FunctionGetDynamicConcurrency,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionGetDynamicConcurrencyRequest,
                modal_proto.api_pb2.FunctionGetDynamicConcurrencyResponse,
            ),
            '/modal.client.ModalClient/FunctionGetInputs': grpclib.const.Handler(
                self.FunctionGetInputs,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionGetInputsRequest,
                modal_proto.api_pb2.FunctionGetInputsResponse,
            ),
            '/modal.client.ModalClient/FunctionGetOutputs': grpclib.const.Handler(
                self.FunctionGetOutputs,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionGetOutputsRequest,
                modal_proto.api_pb2.FunctionGetOutputsResponse,
            ),
            '/modal.client.ModalClient/FunctionGetSerialized': grpclib.const.Handler(
                self.FunctionGetSerialized,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionGetSerializedRequest,
                modal_proto.api_pb2.FunctionGetSerializedResponse,
            ),
            '/modal.client.ModalClient/FunctionMap': grpclib.const.Handler(
                self.FunctionMap,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionMapRequest,
                modal_proto.api_pb2.FunctionMapResponse,
            ),
            '/modal.client.ModalClient/FunctionPrecreate': grpclib.const.Handler(
                self.FunctionPrecreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionPrecreateRequest,
                modal_proto.api_pb2.FunctionPrecreateResponse,
            ),
            '/modal.client.ModalClient/FunctionPutInputs': grpclib.const.Handler(
                self.FunctionPutInputs,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionPutInputsRequest,
                modal_proto.api_pb2.FunctionPutInputsResponse,
            ),
            '/modal.client.ModalClient/FunctionPutOutputs': grpclib.const.Handler(
                self.FunctionPutOutputs,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionPutOutputsRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/FunctionRetryInputs': grpclib.const.Handler(
                self.FunctionRetryInputs,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionRetryInputsRequest,
                modal_proto.api_pb2.FunctionRetryInputsResponse,
            ),
            '/modal.client.ModalClient/FunctionStartPtyShell': grpclib.const.Handler(
                self.FunctionStartPtyShell,
                grpclib.const.Cardinality.UNARY_UNARY,
                google.protobuf.empty_pb2.Empty,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/FunctionUpdateSchedulingParams': grpclib.const.Handler(
                self.FunctionUpdateSchedulingParams,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.FunctionUpdateSchedulingParamsRequest,
                modal_proto.api_pb2.FunctionUpdateSchedulingParamsResponse,
            ),
            '/modal.client.ModalClient/ImageFromId': grpclib.const.Handler(
                self.ImageFromId,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ImageFromIdRequest,
                modal_proto.api_pb2.ImageFromIdResponse,
            ),
            '/modal.client.ModalClient/ImageGetOrCreate': grpclib.const.Handler(
                self.ImageGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ImageGetOrCreateRequest,
                modal_proto.api_pb2.ImageGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/ImageJoinStreaming': grpclib.const.Handler(
                self.ImageJoinStreaming,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.ImageJoinStreamingRequest,
                modal_proto.api_pb2.ImageJoinStreamingResponse,
            ),
            '/modal.client.ModalClient/MountGetOrCreate': grpclib.const.Handler(
                self.MountGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.MountGetOrCreateRequest,
                modal_proto.api_pb2.MountGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/MountPutFile': grpclib.const.Handler(
                self.MountPutFile,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.MountPutFileRequest,
                modal_proto.api_pb2.MountPutFileResponse,
            ),
            '/modal.client.ModalClient/NotebookKernelPublishResults': grpclib.const.Handler(
                self.NotebookKernelPublishResults,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.NotebookKernelPublishResultsRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/ProxyAddIp': grpclib.const.Handler(
                self.ProxyAddIp,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ProxyAddIpRequest,
                modal_proto.api_pb2.ProxyAddIpResponse,
            ),
            '/modal.client.ModalClient/ProxyCreate': grpclib.const.Handler(
                self.ProxyCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ProxyCreateRequest,
                modal_proto.api_pb2.ProxyCreateResponse,
            ),
            '/modal.client.ModalClient/ProxyDelete': grpclib.const.Handler(
                self.ProxyDelete,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ProxyDeleteRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/ProxyGet': grpclib.const.Handler(
                self.ProxyGet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ProxyGetRequest,
                modal_proto.api_pb2.ProxyGetResponse,
            ),
            '/modal.client.ModalClient/ProxyGetOrCreate': grpclib.const.Handler(
                self.ProxyGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ProxyGetOrCreateRequest,
                modal_proto.api_pb2.ProxyGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/ProxyList': grpclib.const.Handler(
                self.ProxyList,
                grpclib.const.Cardinality.UNARY_UNARY,
                google.protobuf.empty_pb2.Empty,
                modal_proto.api_pb2.ProxyListResponse,
            ),
            '/modal.client.ModalClient/ProxyRemoveIp': grpclib.const.Handler(
                self.ProxyRemoveIp,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.ProxyRemoveIpRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/QueueClear': grpclib.const.Handler(
                self.QueueClear,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueueClearRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/QueueDelete': grpclib.const.Handler(
                self.QueueDelete,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueueDeleteRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/QueueGet': grpclib.const.Handler(
                self.QueueGet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueueGetRequest,
                modal_proto.api_pb2.QueueGetResponse,
            ),
            '/modal.client.ModalClient/QueueGetOrCreate': grpclib.const.Handler(
                self.QueueGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueueGetOrCreateRequest,
                modal_proto.api_pb2.QueueGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/QueueHeartbeat': grpclib.const.Handler(
                self.QueueHeartbeat,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueueHeartbeatRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/QueueLen': grpclib.const.Handler(
                self.QueueLen,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueueLenRequest,
                modal_proto.api_pb2.QueueLenResponse,
            ),
            '/modal.client.ModalClient/QueueList': grpclib.const.Handler(
                self.QueueList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueueListRequest,
                modal_proto.api_pb2.QueueListResponse,
            ),
            '/modal.client.ModalClient/QueueNextItems': grpclib.const.Handler(
                self.QueueNextItems,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueueNextItemsRequest,
                modal_proto.api_pb2.QueueNextItemsResponse,
            ),
            '/modal.client.ModalClient/QueuePut': grpclib.const.Handler(
                self.QueuePut,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.QueuePutRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/SandboxCreate': grpclib.const.Handler(
                self.SandboxCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxCreateRequest,
                modal_proto.api_pb2.SandboxCreateResponse,
            ),
            '/modal.client.ModalClient/SandboxGetLogs': grpclib.const.Handler(
                self.SandboxGetLogs,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.SandboxGetLogsRequest,
                modal_proto.api_pb2.TaskLogsBatch,
            ),
            '/modal.client.ModalClient/SandboxGetResourceUsage': grpclib.const.Handler(
                self.SandboxGetResourceUsage,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxGetResourceUsageRequest,
                modal_proto.api_pb2.SandboxGetResourceUsageResponse,
            ),
            '/modal.client.ModalClient/SandboxGetTaskId': grpclib.const.Handler(
                self.SandboxGetTaskId,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxGetTaskIdRequest,
                modal_proto.api_pb2.SandboxGetTaskIdResponse,
            ),
            '/modal.client.ModalClient/SandboxGetTunnels': grpclib.const.Handler(
                self.SandboxGetTunnels,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxGetTunnelsRequest,
                modal_proto.api_pb2.SandboxGetTunnelsResponse,
            ),
            '/modal.client.ModalClient/SandboxList': grpclib.const.Handler(
                self.SandboxList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxListRequest,
                modal_proto.api_pb2.SandboxListResponse,
            ),
            '/modal.client.ModalClient/SandboxRestore': grpclib.const.Handler(
                self.SandboxRestore,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxRestoreRequest,
                modal_proto.api_pb2.SandboxRestoreResponse,
            ),
            '/modal.client.ModalClient/SandboxSnapshot': grpclib.const.Handler(
                self.SandboxSnapshot,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxSnapshotRequest,
                modal_proto.api_pb2.SandboxSnapshotResponse,
            ),
            '/modal.client.ModalClient/SandboxSnapshotFs': grpclib.const.Handler(
                self.SandboxSnapshotFs,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxSnapshotFsRequest,
                modal_proto.api_pb2.SandboxSnapshotFsResponse,
            ),
            '/modal.client.ModalClient/SandboxSnapshotGet': grpclib.const.Handler(
                self.SandboxSnapshotGet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxSnapshotGetRequest,
                modal_proto.api_pb2.SandboxSnapshotGetResponse,
            ),
            '/modal.client.ModalClient/SandboxSnapshotWait': grpclib.const.Handler(
                self.SandboxSnapshotWait,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxSnapshotWaitRequest,
                modal_proto.api_pb2.SandboxSnapshotWaitResponse,
            ),
            '/modal.client.ModalClient/SandboxStdinWrite': grpclib.const.Handler(
                self.SandboxStdinWrite,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxStdinWriteRequest,
                modal_proto.api_pb2.SandboxStdinWriteResponse,
            ),
            '/modal.client.ModalClient/SandboxTagsSet': grpclib.const.Handler(
                self.SandboxTagsSet,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxTagsSetRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/SandboxTerminate': grpclib.const.Handler(
                self.SandboxTerminate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxTerminateRequest,
                modal_proto.api_pb2.SandboxTerminateResponse,
            ),
            '/modal.client.ModalClient/SandboxWait': grpclib.const.Handler(
                self.SandboxWait,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SandboxWaitRequest,
                modal_proto.api_pb2.SandboxWaitResponse,
            ),
            '/modal.client.ModalClient/SecretDelete': grpclib.const.Handler(
                self.SecretDelete,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SecretDeleteRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/SecretGetOrCreate': grpclib.const.Handler(
                self.SecretGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SecretGetOrCreateRequest,
                modal_proto.api_pb2.SecretGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/SecretList': grpclib.const.Handler(
                self.SecretList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SecretListRequest,
                modal_proto.api_pb2.SecretListResponse,
            ),
            '/modal.client.ModalClient/SharedVolumeDelete': grpclib.const.Handler(
                self.SharedVolumeDelete,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SharedVolumeDeleteRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/SharedVolumeGetFile': grpclib.const.Handler(
                self.SharedVolumeGetFile,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SharedVolumeGetFileRequest,
                modal_proto.api_pb2.SharedVolumeGetFileResponse,
            ),
            '/modal.client.ModalClient/SharedVolumeGetOrCreate': grpclib.const.Handler(
                self.SharedVolumeGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SharedVolumeGetOrCreateRequest,
                modal_proto.api_pb2.SharedVolumeGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/SharedVolumeHeartbeat': grpclib.const.Handler(
                self.SharedVolumeHeartbeat,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SharedVolumeHeartbeatRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/SharedVolumeList': grpclib.const.Handler(
                self.SharedVolumeList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SharedVolumeListRequest,
                modal_proto.api_pb2.SharedVolumeListResponse,
            ),
            '/modal.client.ModalClient/SharedVolumeListFiles': grpclib.const.Handler(
                self.SharedVolumeListFiles,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SharedVolumeListFilesRequest,
                modal_proto.api_pb2.SharedVolumeListFilesResponse,
            ),
            '/modal.client.ModalClient/SharedVolumeListFilesStream': grpclib.const.Handler(
                self.SharedVolumeListFilesStream,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.SharedVolumeListFilesRequest,
                modal_proto.api_pb2.SharedVolumeListFilesResponse,
            ),
            '/modal.client.ModalClient/SharedVolumePutFile': grpclib.const.Handler(
                self.SharedVolumePutFile,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SharedVolumePutFileRequest,
                modal_proto.api_pb2.SharedVolumePutFileResponse,
            ),
            '/modal.client.ModalClient/SharedVolumeRemoveFile': grpclib.const.Handler(
                self.SharedVolumeRemoveFile,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.SharedVolumeRemoveFileRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/TaskClusterHello': grpclib.const.Handler(
                self.TaskClusterHello,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.TaskClusterHelloRequest,
                modal_proto.api_pb2.TaskClusterHelloResponse,
            ),
            '/modal.client.ModalClient/TaskCurrentInputs': grpclib.const.Handler(
                self.TaskCurrentInputs,
                grpclib.const.Cardinality.UNARY_UNARY,
                google.protobuf.empty_pb2.Empty,
                modal_proto.api_pb2.TaskCurrentInputsResponse,
            ),
            '/modal.client.ModalClient/TaskList': grpclib.const.Handler(
                self.TaskList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.TaskListRequest,
                modal_proto.api_pb2.TaskListResponse,
            ),
            '/modal.client.ModalClient/TaskResult': grpclib.const.Handler(
                self.TaskResult,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.TaskResultRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/TokenFlowCreate': grpclib.const.Handler(
                self.TokenFlowCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.TokenFlowCreateRequest,
                modal_proto.api_pb2.TokenFlowCreateResponse,
            ),
            '/modal.client.ModalClient/TokenFlowWait': grpclib.const.Handler(
                self.TokenFlowWait,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.TokenFlowWaitRequest,
                modal_proto.api_pb2.TokenFlowWaitResponse,
            ),
            '/modal.client.ModalClient/TunnelStart': grpclib.const.Handler(
                self.TunnelStart,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.TunnelStartRequest,
                modal_proto.api_pb2.TunnelStartResponse,
            ),
            '/modal.client.ModalClient/TunnelStop': grpclib.const.Handler(
                self.TunnelStop,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.TunnelStopRequest,
                modal_proto.api_pb2.TunnelStopResponse,
            ),
            '/modal.client.ModalClient/VolumeCommit': grpclib.const.Handler(
                self.VolumeCommit,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeCommitRequest,
                modal_proto.api_pb2.VolumeCommitResponse,
            ),
            '/modal.client.ModalClient/VolumeCopyFiles': grpclib.const.Handler(
                self.VolumeCopyFiles,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeCopyFilesRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/VolumeCopyFiles2': grpclib.const.Handler(
                self.VolumeCopyFiles2,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeCopyFiles2Request,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/VolumeDelete': grpclib.const.Handler(
                self.VolumeDelete,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeDeleteRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/VolumeGetFile': grpclib.const.Handler(
                self.VolumeGetFile,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeGetFileRequest,
                modal_proto.api_pb2.VolumeGetFileResponse,
            ),
            '/modal.client.ModalClient/VolumeGetFile2': grpclib.const.Handler(
                self.VolumeGetFile2,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeGetFile2Request,
                modal_proto.api_pb2.VolumeGetFile2Response,
            ),
            '/modal.client.ModalClient/VolumeGetOrCreate': grpclib.const.Handler(
                self.VolumeGetOrCreate,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeGetOrCreateRequest,
                modal_proto.api_pb2.VolumeGetOrCreateResponse,
            ),
            '/modal.client.ModalClient/VolumeHeartbeat': grpclib.const.Handler(
                self.VolumeHeartbeat,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeHeartbeatRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/VolumeList': grpclib.const.Handler(
                self.VolumeList,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeListRequest,
                modal_proto.api_pb2.VolumeListResponse,
            ),
            '/modal.client.ModalClient/VolumeListFiles': grpclib.const.Handler(
                self.VolumeListFiles,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.VolumeListFilesRequest,
                modal_proto.api_pb2.VolumeListFilesResponse,
            ),
            '/modal.client.ModalClient/VolumeListFiles2': grpclib.const.Handler(
                self.VolumeListFiles2,
                grpclib.const.Cardinality.UNARY_STREAM,
                modal_proto.api_pb2.VolumeListFiles2Request,
                modal_proto.api_pb2.VolumeListFiles2Response,
            ),
            '/modal.client.ModalClient/VolumePutFiles': grpclib.const.Handler(
                self.VolumePutFiles,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumePutFilesRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/VolumePutFiles2': grpclib.const.Handler(
                self.VolumePutFiles2,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumePutFiles2Request,
                modal_proto.api_pb2.VolumePutFiles2Response,
            ),
            '/modal.client.ModalClient/VolumeReload': grpclib.const.Handler(
                self.VolumeReload,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeReloadRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/VolumeRemoveFile': grpclib.const.Handler(
                self.VolumeRemoveFile,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeRemoveFileRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/VolumeRemoveFile2': grpclib.const.Handler(
                self.VolumeRemoveFile2,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeRemoveFile2Request,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/VolumeRename': grpclib.const.Handler(
                self.VolumeRename,
                grpclib.const.Cardinality.UNARY_UNARY,
                modal_proto.api_pb2.VolumeRenameRequest,
                google.protobuf.empty_pb2.Empty,
            ),
            '/modal.client.ModalClient/WorkspaceNameLookup': grpclib.const.Handler(
                self.WorkspaceNameLookup,
                grpclib.const.Cardinality.UNARY_UNARY,
                google.protobuf.empty_pb2.Empty,
                modal_proto.api_pb2.WorkspaceNameLookupResponse,
            ),
        }


class ModalClientStub:

    def __init__(self, channel: grpclib.client.Channel) -> None:
        self.AppClientDisconnect = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppClientDisconnect',
            modal_proto.api_pb2.AppClientDisconnectRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.AppCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppCreate',
            modal_proto.api_pb2.AppCreateRequest,
            modal_proto.api_pb2.AppCreateResponse,
        )
        self.AppDeploy = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppDeploy',
            modal_proto.api_pb2.AppDeployRequest,
            modal_proto.api_pb2.AppDeployResponse,
        )
        self.AppDeploymentHistory = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppDeploymentHistory',
            modal_proto.api_pb2.AppDeploymentHistoryRequest,
            modal_proto.api_pb2.AppDeploymentHistoryResponse,
        )
        self.AppGetByDeploymentName = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppGetByDeploymentName',
            modal_proto.api_pb2.AppGetByDeploymentNameRequest,
            modal_proto.api_pb2.AppGetByDeploymentNameResponse,
        )
        self.AppGetLayout = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppGetLayout',
            modal_proto.api_pb2.AppGetLayoutRequest,
            modal_proto.api_pb2.AppGetLayoutResponse,
        )
        self.AppGetLogs = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/AppGetLogs',
            modal_proto.api_pb2.AppGetLogsRequest,
            modal_proto.api_pb2.TaskLogsBatch,
        )
        self.AppGetObjects = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppGetObjects',
            modal_proto.api_pb2.AppGetObjectsRequest,
            modal_proto.api_pb2.AppGetObjectsResponse,
        )
        self.AppGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppGetOrCreate',
            modal_proto.api_pb2.AppGetOrCreateRequest,
            modal_proto.api_pb2.AppGetOrCreateResponse,
        )
        self.AppHeartbeat = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppHeartbeat',
            modal_proto.api_pb2.AppHeartbeatRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.AppList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppList',
            modal_proto.api_pb2.AppListRequest,
            modal_proto.api_pb2.AppListResponse,
        )
        self.AppLookup = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppLookup',
            modal_proto.api_pb2.AppLookupRequest,
            modal_proto.api_pb2.AppLookupResponse,
        )
        self.AppPublish = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppPublish',
            modal_proto.api_pb2.AppPublishRequest,
            modal_proto.api_pb2.AppPublishResponse,
        )
        self.AppRollback = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppRollback',
            modal_proto.api_pb2.AppRollbackRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.AppSetObjects = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppSetObjects',
            modal_proto.api_pb2.AppSetObjectsRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.AppStop = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AppStop',
            modal_proto.api_pb2.AppStopRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.AttemptAwait = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AttemptAwait',
            modal_proto.api_pb2.AttemptAwaitRequest,
            modal_proto.api_pb2.AttemptAwaitResponse,
        )
        self.AttemptRetry = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AttemptRetry',
            modal_proto.api_pb2.AttemptRetryRequest,
            modal_proto.api_pb2.AttemptRetryResponse,
        )
        self.AttemptStart = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/AttemptStart',
            modal_proto.api_pb2.AttemptStartRequest,
            modal_proto.api_pb2.AttemptStartResponse,
        )
        self.BlobCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/BlobCreate',
            modal_proto.api_pb2.BlobCreateRequest,
            modal_proto.api_pb2.BlobCreateResponse,
        )
        self.BlobGet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/BlobGet',
            modal_proto.api_pb2.BlobGetRequest,
            modal_proto.api_pb2.BlobGetResponse,
        )
        self.ClassCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ClassCreate',
            modal_proto.api_pb2.ClassCreateRequest,
            modal_proto.api_pb2.ClassCreateResponse,
        )
        self.ClassGet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ClassGet',
            modal_proto.api_pb2.ClassGetRequest,
            modal_proto.api_pb2.ClassGetResponse,
        )
        self.ClientHello = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ClientHello',
            google.protobuf.empty_pb2.Empty,
            modal_proto.api_pb2.ClientHelloResponse,
        )
        self.ClusterGet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ClusterGet',
            modal_proto.api_pb2.ClusterGetRequest,
            modal_proto.api_pb2.ClusterGetResponse,
        )
        self.ClusterList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ClusterList',
            modal_proto.api_pb2.ClusterListRequest,
            modal_proto.api_pb2.ClusterListResponse,
        )
        self.ContainerCheckpoint = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerCheckpoint',
            modal_proto.api_pb2.ContainerCheckpointRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.ContainerExec = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerExec',
            modal_proto.api_pb2.ContainerExecRequest,
            modal_proto.api_pb2.ContainerExecResponse,
        )
        self.ContainerExecGetOutput = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/ContainerExecGetOutput',
            modal_proto.api_pb2.ContainerExecGetOutputRequest,
            modal_proto.api_pb2.RuntimeOutputBatch,
        )
        self.ContainerExecPutInput = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerExecPutInput',
            modal_proto.api_pb2.ContainerExecPutInputRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.ContainerExecWait = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerExecWait',
            modal_proto.api_pb2.ContainerExecWaitRequest,
            modal_proto.api_pb2.ContainerExecWaitResponse,
        )
        self.ContainerFilesystemExec = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerFilesystemExec',
            modal_proto.api_pb2.ContainerFilesystemExecRequest,
            modal_proto.api_pb2.ContainerFilesystemExecResponse,
        )
        self.ContainerFilesystemExecGetOutput = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/ContainerFilesystemExecGetOutput',
            modal_proto.api_pb2.ContainerFilesystemExecGetOutputRequest,
            modal_proto.api_pb2.FilesystemRuntimeOutputBatch,
        )
        self.ContainerHeartbeat = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerHeartbeat',
            modal_proto.api_pb2.ContainerHeartbeatRequest,
            modal_proto.api_pb2.ContainerHeartbeatResponse,
        )
        self.ContainerHello = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerHello',
            google.protobuf.empty_pb2.Empty,
            google.protobuf.empty_pb2.Empty,
        )
        self.ContainerLog = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerLog',
            modal_proto.api_pb2.ContainerLogRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.ContainerStop = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ContainerStop',
            modal_proto.api_pb2.ContainerStopRequest,
            modal_proto.api_pb2.ContainerStopResponse,
        )
        self.DictClear = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictClear',
            modal_proto.api_pb2.DictClearRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.DictContains = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictContains',
            modal_proto.api_pb2.DictContainsRequest,
            modal_proto.api_pb2.DictContainsResponse,
        )
        self.DictContents = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/DictContents',
            modal_proto.api_pb2.DictContentsRequest,
            modal_proto.api_pb2.DictEntry,
        )
        self.DictDelete = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictDelete',
            modal_proto.api_pb2.DictDeleteRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.DictGet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictGet',
            modal_proto.api_pb2.DictGetRequest,
            modal_proto.api_pb2.DictGetResponse,
        )
        self.DictGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictGetOrCreate',
            modal_proto.api_pb2.DictGetOrCreateRequest,
            modal_proto.api_pb2.DictGetOrCreateResponse,
        )
        self.DictHeartbeat = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictHeartbeat',
            modal_proto.api_pb2.DictHeartbeatRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.DictLen = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictLen',
            modal_proto.api_pb2.DictLenRequest,
            modal_proto.api_pb2.DictLenResponse,
        )
        self.DictList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictList',
            modal_proto.api_pb2.DictListRequest,
            modal_proto.api_pb2.DictListResponse,
        )
        self.DictPop = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictPop',
            modal_proto.api_pb2.DictPopRequest,
            modal_proto.api_pb2.DictPopResponse,
        )
        self.DictUpdate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DictUpdate',
            modal_proto.api_pb2.DictUpdateRequest,
            modal_proto.api_pb2.DictUpdateResponse,
        )
        self.DomainCertificateVerify = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DomainCertificateVerify',
            modal_proto.api_pb2.DomainCertificateVerifyRequest,
            modal_proto.api_pb2.DomainCertificateVerifyResponse,
        )
        self.DomainCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DomainCreate',
            modal_proto.api_pb2.DomainCreateRequest,
            modal_proto.api_pb2.DomainCreateResponse,
        )
        self.DomainList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/DomainList',
            modal_proto.api_pb2.DomainListRequest,
            modal_proto.api_pb2.DomainListResponse,
        )
        self.EnvironmentCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/EnvironmentCreate',
            modal_proto.api_pb2.EnvironmentCreateRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.EnvironmentDelete = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/EnvironmentDelete',
            modal_proto.api_pb2.EnvironmentDeleteRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.EnvironmentGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/EnvironmentGetOrCreate',
            modal_proto.api_pb2.EnvironmentGetOrCreateRequest,
            modal_proto.api_pb2.EnvironmentGetOrCreateResponse,
        )
        self.EnvironmentList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/EnvironmentList',
            google.protobuf.empty_pb2.Empty,
            modal_proto.api_pb2.EnvironmentListResponse,
        )
        self.EnvironmentUpdate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/EnvironmentUpdate',
            modal_proto.api_pb2.EnvironmentUpdateRequest,
            modal_proto.api_pb2.EnvironmentListItem,
        )
        self.FunctionAsyncInvoke = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionAsyncInvoke',
            modal_proto.api_pb2.FunctionAsyncInvokeRequest,
            modal_proto.api_pb2.FunctionAsyncInvokeResponse,
        )
        self.FunctionBindParams = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionBindParams',
            modal_proto.api_pb2.FunctionBindParamsRequest,
            modal_proto.api_pb2.FunctionBindParamsResponse,
        )
        self.FunctionCallCancel = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionCallCancel',
            modal_proto.api_pb2.FunctionCallCancelRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.FunctionCallGetDataIn = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/FunctionCallGetDataIn',
            modal_proto.api_pb2.FunctionCallGetDataRequest,
            modal_proto.api_pb2.DataChunk,
        )
        self.FunctionCallGetDataOut = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/FunctionCallGetDataOut',
            modal_proto.api_pb2.FunctionCallGetDataRequest,
            modal_proto.api_pb2.DataChunk,
        )
        self.FunctionCallList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionCallList',
            modal_proto.api_pb2.FunctionCallListRequest,
            modal_proto.api_pb2.FunctionCallListResponse,
        )
        self.FunctionCallPutDataOut = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionCallPutDataOut',
            modal_proto.api_pb2.FunctionCallPutDataRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.FunctionCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionCreate',
            modal_proto.api_pb2.FunctionCreateRequest,
            modal_proto.api_pb2.FunctionCreateResponse,
        )
        self.FunctionGet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionGet',
            modal_proto.api_pb2.FunctionGetRequest,
            modal_proto.api_pb2.FunctionGetResponse,
        )
        self.FunctionGetCallGraph = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionGetCallGraph',
            modal_proto.api_pb2.FunctionGetCallGraphRequest,
            modal_proto.api_pb2.FunctionGetCallGraphResponse,
        )
        self.FunctionGetCurrentStats = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionGetCurrentStats',
            modal_proto.api_pb2.FunctionGetCurrentStatsRequest,
            modal_proto.api_pb2.FunctionStats,
        )
        self.FunctionGetDynamicConcurrency = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionGetDynamicConcurrency',
            modal_proto.api_pb2.FunctionGetDynamicConcurrencyRequest,
            modal_proto.api_pb2.FunctionGetDynamicConcurrencyResponse,
        )
        self.FunctionGetInputs = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionGetInputs',
            modal_proto.api_pb2.FunctionGetInputsRequest,
            modal_proto.api_pb2.FunctionGetInputsResponse,
        )
        self.FunctionGetOutputs = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionGetOutputs',
            modal_proto.api_pb2.FunctionGetOutputsRequest,
            modal_proto.api_pb2.FunctionGetOutputsResponse,
        )
        self.FunctionGetSerialized = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionGetSerialized',
            modal_proto.api_pb2.FunctionGetSerializedRequest,
            modal_proto.api_pb2.FunctionGetSerializedResponse,
        )
        self.FunctionMap = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionMap',
            modal_proto.api_pb2.FunctionMapRequest,
            modal_proto.api_pb2.FunctionMapResponse,
        )
        self.FunctionPrecreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionPrecreate',
            modal_proto.api_pb2.FunctionPrecreateRequest,
            modal_proto.api_pb2.FunctionPrecreateResponse,
        )
        self.FunctionPutInputs = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionPutInputs',
            modal_proto.api_pb2.FunctionPutInputsRequest,
            modal_proto.api_pb2.FunctionPutInputsResponse,
        )
        self.FunctionPutOutputs = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionPutOutputs',
            modal_proto.api_pb2.FunctionPutOutputsRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.FunctionRetryInputs = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionRetryInputs',
            modal_proto.api_pb2.FunctionRetryInputsRequest,
            modal_proto.api_pb2.FunctionRetryInputsResponse,
        )
        self.FunctionStartPtyShell = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionStartPtyShell',
            google.protobuf.empty_pb2.Empty,
            google.protobuf.empty_pb2.Empty,
        )
        self.FunctionUpdateSchedulingParams = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/FunctionUpdateSchedulingParams',
            modal_proto.api_pb2.FunctionUpdateSchedulingParamsRequest,
            modal_proto.api_pb2.FunctionUpdateSchedulingParamsResponse,
        )
        self.ImageFromId = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ImageFromId',
            modal_proto.api_pb2.ImageFromIdRequest,
            modal_proto.api_pb2.ImageFromIdResponse,
        )
        self.ImageGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ImageGetOrCreate',
            modal_proto.api_pb2.ImageGetOrCreateRequest,
            modal_proto.api_pb2.ImageGetOrCreateResponse,
        )
        self.ImageJoinStreaming = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/ImageJoinStreaming',
            modal_proto.api_pb2.ImageJoinStreamingRequest,
            modal_proto.api_pb2.ImageJoinStreamingResponse,
        )
        self.MountGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/MountGetOrCreate',
            modal_proto.api_pb2.MountGetOrCreateRequest,
            modal_proto.api_pb2.MountGetOrCreateResponse,
        )
        self.MountPutFile = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/MountPutFile',
            modal_proto.api_pb2.MountPutFileRequest,
            modal_proto.api_pb2.MountPutFileResponse,
        )
        self.NotebookKernelPublishResults = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/NotebookKernelPublishResults',
            modal_proto.api_pb2.NotebookKernelPublishResultsRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.ProxyAddIp = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ProxyAddIp',
            modal_proto.api_pb2.ProxyAddIpRequest,
            modal_proto.api_pb2.ProxyAddIpResponse,
        )
        self.ProxyCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ProxyCreate',
            modal_proto.api_pb2.ProxyCreateRequest,
            modal_proto.api_pb2.ProxyCreateResponse,
        )
        self.ProxyDelete = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ProxyDelete',
            modal_proto.api_pb2.ProxyDeleteRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.ProxyGet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ProxyGet',
            modal_proto.api_pb2.ProxyGetRequest,
            modal_proto.api_pb2.ProxyGetResponse,
        )
        self.ProxyGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ProxyGetOrCreate',
            modal_proto.api_pb2.ProxyGetOrCreateRequest,
            modal_proto.api_pb2.ProxyGetOrCreateResponse,
        )
        self.ProxyList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ProxyList',
            google.protobuf.empty_pb2.Empty,
            modal_proto.api_pb2.ProxyListResponse,
        )
        self.ProxyRemoveIp = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/ProxyRemoveIp',
            modal_proto.api_pb2.ProxyRemoveIpRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.QueueClear = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueueClear',
            modal_proto.api_pb2.QueueClearRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.QueueDelete = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueueDelete',
            modal_proto.api_pb2.QueueDeleteRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.QueueGet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueueGet',
            modal_proto.api_pb2.QueueGetRequest,
            modal_proto.api_pb2.QueueGetResponse,
        )
        self.QueueGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueueGetOrCreate',
            modal_proto.api_pb2.QueueGetOrCreateRequest,
            modal_proto.api_pb2.QueueGetOrCreateResponse,
        )
        self.QueueHeartbeat = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueueHeartbeat',
            modal_proto.api_pb2.QueueHeartbeatRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.QueueLen = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueueLen',
            modal_proto.api_pb2.QueueLenRequest,
            modal_proto.api_pb2.QueueLenResponse,
        )
        self.QueueList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueueList',
            modal_proto.api_pb2.QueueListRequest,
            modal_proto.api_pb2.QueueListResponse,
        )
        self.QueueNextItems = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueueNextItems',
            modal_proto.api_pb2.QueueNextItemsRequest,
            modal_proto.api_pb2.QueueNextItemsResponse,
        )
        self.QueuePut = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/QueuePut',
            modal_proto.api_pb2.QueuePutRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.SandboxCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxCreate',
            modal_proto.api_pb2.SandboxCreateRequest,
            modal_proto.api_pb2.SandboxCreateResponse,
        )
        self.SandboxGetLogs = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/SandboxGetLogs',
            modal_proto.api_pb2.SandboxGetLogsRequest,
            modal_proto.api_pb2.TaskLogsBatch,
        )
        self.SandboxGetResourceUsage = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxGetResourceUsage',
            modal_proto.api_pb2.SandboxGetResourceUsageRequest,
            modal_proto.api_pb2.SandboxGetResourceUsageResponse,
        )
        self.SandboxGetTaskId = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxGetTaskId',
            modal_proto.api_pb2.SandboxGetTaskIdRequest,
            modal_proto.api_pb2.SandboxGetTaskIdResponse,
        )
        self.SandboxGetTunnels = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxGetTunnels',
            modal_proto.api_pb2.SandboxGetTunnelsRequest,
            modal_proto.api_pb2.SandboxGetTunnelsResponse,
        )
        self.SandboxList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxList',
            modal_proto.api_pb2.SandboxListRequest,
            modal_proto.api_pb2.SandboxListResponse,
        )
        self.SandboxRestore = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxRestore',
            modal_proto.api_pb2.SandboxRestoreRequest,
            modal_proto.api_pb2.SandboxRestoreResponse,
        )
        self.SandboxSnapshot = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxSnapshot',
            modal_proto.api_pb2.SandboxSnapshotRequest,
            modal_proto.api_pb2.SandboxSnapshotResponse,
        )
        self.SandboxSnapshotFs = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxSnapshotFs',
            modal_proto.api_pb2.SandboxSnapshotFsRequest,
            modal_proto.api_pb2.SandboxSnapshotFsResponse,
        )
        self.SandboxSnapshotGet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxSnapshotGet',
            modal_proto.api_pb2.SandboxSnapshotGetRequest,
            modal_proto.api_pb2.SandboxSnapshotGetResponse,
        )
        self.SandboxSnapshotWait = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxSnapshotWait',
            modal_proto.api_pb2.SandboxSnapshotWaitRequest,
            modal_proto.api_pb2.SandboxSnapshotWaitResponse,
        )
        self.SandboxStdinWrite = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxStdinWrite',
            modal_proto.api_pb2.SandboxStdinWriteRequest,
            modal_proto.api_pb2.SandboxStdinWriteResponse,
        )
        self.SandboxTagsSet = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxTagsSet',
            modal_proto.api_pb2.SandboxTagsSetRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.SandboxTerminate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxTerminate',
            modal_proto.api_pb2.SandboxTerminateRequest,
            modal_proto.api_pb2.SandboxTerminateResponse,
        )
        self.SandboxWait = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SandboxWait',
            modal_proto.api_pb2.SandboxWaitRequest,
            modal_proto.api_pb2.SandboxWaitResponse,
        )
        self.SecretDelete = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SecretDelete',
            modal_proto.api_pb2.SecretDeleteRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.SecretGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SecretGetOrCreate',
            modal_proto.api_pb2.SecretGetOrCreateRequest,
            modal_proto.api_pb2.SecretGetOrCreateResponse,
        )
        self.SecretList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SecretList',
            modal_proto.api_pb2.SecretListRequest,
            modal_proto.api_pb2.SecretListResponse,
        )
        self.SharedVolumeDelete = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumeDelete',
            modal_proto.api_pb2.SharedVolumeDeleteRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.SharedVolumeGetFile = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumeGetFile',
            modal_proto.api_pb2.SharedVolumeGetFileRequest,
            modal_proto.api_pb2.SharedVolumeGetFileResponse,
        )
        self.SharedVolumeGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumeGetOrCreate',
            modal_proto.api_pb2.SharedVolumeGetOrCreateRequest,
            modal_proto.api_pb2.SharedVolumeGetOrCreateResponse,
        )
        self.SharedVolumeHeartbeat = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumeHeartbeat',
            modal_proto.api_pb2.SharedVolumeHeartbeatRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.SharedVolumeList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumeList',
            modal_proto.api_pb2.SharedVolumeListRequest,
            modal_proto.api_pb2.SharedVolumeListResponse,
        )
        self.SharedVolumeListFiles = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumeListFiles',
            modal_proto.api_pb2.SharedVolumeListFilesRequest,
            modal_proto.api_pb2.SharedVolumeListFilesResponse,
        )
        self.SharedVolumeListFilesStream = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumeListFilesStream',
            modal_proto.api_pb2.SharedVolumeListFilesRequest,
            modal_proto.api_pb2.SharedVolumeListFilesResponse,
        )
        self.SharedVolumePutFile = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumePutFile',
            modal_proto.api_pb2.SharedVolumePutFileRequest,
            modal_proto.api_pb2.SharedVolumePutFileResponse,
        )
        self.SharedVolumeRemoveFile = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/SharedVolumeRemoveFile',
            modal_proto.api_pb2.SharedVolumeRemoveFileRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.TaskClusterHello = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/TaskClusterHello',
            modal_proto.api_pb2.TaskClusterHelloRequest,
            modal_proto.api_pb2.TaskClusterHelloResponse,
        )
        self.TaskCurrentInputs = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/TaskCurrentInputs',
            google.protobuf.empty_pb2.Empty,
            modal_proto.api_pb2.TaskCurrentInputsResponse,
        )
        self.TaskList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/TaskList',
            modal_proto.api_pb2.TaskListRequest,
            modal_proto.api_pb2.TaskListResponse,
        )
        self.TaskResult = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/TaskResult',
            modal_proto.api_pb2.TaskResultRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.TokenFlowCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/TokenFlowCreate',
            modal_proto.api_pb2.TokenFlowCreateRequest,
            modal_proto.api_pb2.TokenFlowCreateResponse,
        )
        self.TokenFlowWait = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/TokenFlowWait',
            modal_proto.api_pb2.TokenFlowWaitRequest,
            modal_proto.api_pb2.TokenFlowWaitResponse,
        )
        self.TunnelStart = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/TunnelStart',
            modal_proto.api_pb2.TunnelStartRequest,
            modal_proto.api_pb2.TunnelStartResponse,
        )
        self.TunnelStop = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/TunnelStop',
            modal_proto.api_pb2.TunnelStopRequest,
            modal_proto.api_pb2.TunnelStopResponse,
        )
        self.VolumeCommit = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeCommit',
            modal_proto.api_pb2.VolumeCommitRequest,
            modal_proto.api_pb2.VolumeCommitResponse,
        )
        self.VolumeCopyFiles = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeCopyFiles',
            modal_proto.api_pb2.VolumeCopyFilesRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.VolumeCopyFiles2 = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeCopyFiles2',
            modal_proto.api_pb2.VolumeCopyFiles2Request,
            google.protobuf.empty_pb2.Empty,
        )
        self.VolumeDelete = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeDelete',
            modal_proto.api_pb2.VolumeDeleteRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.VolumeGetFile = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeGetFile',
            modal_proto.api_pb2.VolumeGetFileRequest,
            modal_proto.api_pb2.VolumeGetFileResponse,
        )
        self.VolumeGetFile2 = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeGetFile2',
            modal_proto.api_pb2.VolumeGetFile2Request,
            modal_proto.api_pb2.VolumeGetFile2Response,
        )
        self.VolumeGetOrCreate = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeGetOrCreate',
            modal_proto.api_pb2.VolumeGetOrCreateRequest,
            modal_proto.api_pb2.VolumeGetOrCreateResponse,
        )
        self.VolumeHeartbeat = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeHeartbeat',
            modal_proto.api_pb2.VolumeHeartbeatRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.VolumeList = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeList',
            modal_proto.api_pb2.VolumeListRequest,
            modal_proto.api_pb2.VolumeListResponse,
        )
        self.VolumeListFiles = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/VolumeListFiles',
            modal_proto.api_pb2.VolumeListFilesRequest,
            modal_proto.api_pb2.VolumeListFilesResponse,
        )
        self.VolumeListFiles2 = grpclib.client.UnaryStreamMethod(
            channel,
            '/modal.client.ModalClient/VolumeListFiles2',
            modal_proto.api_pb2.VolumeListFiles2Request,
            modal_proto.api_pb2.VolumeListFiles2Response,
        )
        self.VolumePutFiles = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumePutFiles',
            modal_proto.api_pb2.VolumePutFilesRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.VolumePutFiles2 = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumePutFiles2',
            modal_proto.api_pb2.VolumePutFiles2Request,
            modal_proto.api_pb2.VolumePutFiles2Response,
        )
        self.VolumeReload = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeReload',
            modal_proto.api_pb2.VolumeReloadRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.VolumeRemoveFile = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeRemoveFile',
            modal_proto.api_pb2.VolumeRemoveFileRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.VolumeRemoveFile2 = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeRemoveFile2',
            modal_proto.api_pb2.VolumeRemoveFile2Request,
            google.protobuf.empty_pb2.Empty,
        )
        self.VolumeRename = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/VolumeRename',
            modal_proto.api_pb2.VolumeRenameRequest,
            google.protobuf.empty_pb2.Empty,
        )
        self.WorkspaceNameLookup = grpclib.client.UnaryUnaryMethod(
            channel,
            '/modal.client.ModalClient/WorkspaceNameLookup',
            google.protobuf.empty_pb2.Empty,
            modal_proto.api_pb2.WorkspaceNameLookupResponse,
        )
