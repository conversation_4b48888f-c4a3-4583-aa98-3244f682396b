import collections.abc
import modal._functions
import modal._partial_function
import modal._utils.function_utils
import modal.client
import modal.cloud_bucket_mount
import modal.cls
import modal.functions
import modal.gpu
import modal.image
import modal.network_file_system
import modal.partial_function
import modal.proxy
import modal.retries
import modal.running_app
import modal.schedule
import modal.scheduler_placement
import modal.secret
import modal.volume
import pathlib
import synchronicity.combined_types
import typing
import typing_extensions

class _LocalEntrypoint:
    _info: modal._utils.function_utils.FunctionInfo
    _app: _App

    def __init__(self, info: modal._utils.function_utils.FunctionInfo, app: _App) -> None: ...
    def __call__(self, *args: typing.Any, **kwargs: typing.Any) -> typing.Any: ...
    @property
    def info(self) -> modal._utils.function_utils.FunctionInfo: ...
    @property
    def app(self) -> _App: ...

class LocalEntrypoint:
    _info: modal._utils.function_utils.FunctionInfo
    _app: App

    def __init__(self, info: modal._utils.function_utils.FunctionInfo, app: App) -> None: ...
    def __call__(self, *args: typing.Any, **kwargs: typing.Any) -> typing.Any: ...
    @property
    def info(self) -> modal._utils.function_utils.FunctionInfo: ...
    @property
    def app(self) -> App: ...

def check_sequence(items: typing.Sequence[typing.Any], item_type: type[typing.Any], error_msg: str) -> None: ...

CLS_T = typing.TypeVar("CLS_T", bound="type[typing.Any]")

P = typing_extensions.ParamSpec("P")

ReturnType = typing.TypeVar("ReturnType")

OriginalReturnType = typing.TypeVar("OriginalReturnType")

class _FunctionDecoratorType:
    @typing.overload
    def __call__(
        self, func: modal.partial_function.PartialFunction[P, ReturnType, OriginalReturnType]
    ) -> modal.functions.Function[P, ReturnType, OriginalReturnType]: ...
    @typing.overload
    def __call__(
        self, func: collections.abc.Callable[P, collections.abc.Coroutine[typing.Any, typing.Any, ReturnType]]
    ) -> modal.functions.Function[P, ReturnType, collections.abc.Coroutine[typing.Any, typing.Any, ReturnType]]: ...
    @typing.overload
    def __call__(
        self, func: collections.abc.Callable[P, ReturnType]
    ) -> modal.functions.Function[P, ReturnType, ReturnType]: ...

class _App:
    _all_apps: typing.ClassVar[dict[typing.Optional[str], list[_App]]]
    _container_app: typing.ClassVar[typing.Optional[_App]]
    _name: typing.Optional[str]
    _description: typing.Optional[str]
    _functions: dict[str, modal._functions._Function]
    _classes: dict[str, modal.cls._Cls]
    _image: typing.Optional[modal.image._Image]
    _secrets: collections.abc.Sequence[modal.secret._Secret]
    _volumes: dict[typing.Union[str, pathlib.PurePosixPath], modal.volume._Volume]
    _web_endpoints: list[str]
    _local_entrypoints: dict[str, _LocalEntrypoint]
    _app_id: typing.Optional[str]
    _running_app: typing.Optional[modal.running_app.RunningApp]
    _client: typing.Optional[modal.client._Client]
    _include_source_default: typing.Optional[bool]

    def __init__(
        self,
        name: typing.Optional[str] = None,
        *,
        image: typing.Optional[modal.image._Image] = None,
        secrets: collections.abc.Sequence[modal.secret._Secret] = [],
        volumes: dict[typing.Union[str, pathlib.PurePosixPath], modal.volume._Volume] = {},
        include_source: typing.Optional[bool] = None,
    ) -> None: ...
    @property
    def name(self) -> typing.Optional[str]: ...
    @property
    def is_interactive(self) -> bool: ...
    @property
    def app_id(self) -> typing.Optional[str]: ...
    @property
    def description(self) -> typing.Optional[str]: ...
    @staticmethod
    async def lookup(
        name: str,
        *,
        client: typing.Optional[modal.client._Client] = None,
        environment_name: typing.Optional[str] = None,
        create_if_missing: bool = False,
    ) -> _App: ...
    def set_description(self, description: str): ...
    def _validate_blueprint_value(self, key: str, value: typing.Any): ...
    @property
    def image(self) -> modal.image._Image: ...
    @image.setter
    def image(self, value): ...
    def _uncreate_all_objects(self): ...
    def _set_local_app(
        self, client: modal.client._Client, running_app: modal.running_app.RunningApp
    ) -> typing.AsyncContextManager[None]: ...
    def run(
        self,
        *,
        client: typing.Optional[modal.client._Client] = None,
        detach: bool = False,
        interactive: bool = False,
        environment_name: typing.Optional[str] = None,
    ) -> typing.AsyncContextManager[_App]: ...
    async def deploy(
        self,
        *,
        name: typing.Optional[str] = None,
        environment_name: typing.Optional[str] = None,
        tag: str = "",
        client: typing.Optional[modal.client._Client] = None,
    ) -> typing_extensions.Self: ...
    def _get_default_image(self): ...
    def _get_watch_mounts(self): ...
    def _add_function(self, function: modal._functions._Function, is_web_endpoint: bool): ...
    def _add_class(self, tag: str, cls: modal.cls._Cls): ...
    def _init_container(self, client: modal.client._Client, running_app: modal.running_app.RunningApp): ...
    @property
    def registered_functions(self) -> dict[str, modal._functions._Function]: ...
    @property
    def registered_classes(self) -> dict[str, modal.cls._Cls]: ...
    @property
    def registered_entrypoints(self) -> dict[str, _LocalEntrypoint]: ...
    @property
    def registered_web_endpoints(self) -> list[str]: ...
    def local_entrypoint(
        self, _warn_parentheses_missing: typing.Any = None, *, name: typing.Optional[str] = None
    ) -> collections.abc.Callable[[collections.abc.Callable[..., typing.Any]], _LocalEntrypoint]: ...
    def function(
        self,
        _warn_parentheses_missing: typing.Any = None,
        *,
        image: typing.Optional[modal.image._Image] = None,
        schedule: typing.Optional[modal.schedule.Schedule] = None,
        secrets: collections.abc.Sequence[modal.secret._Secret] = (),
        gpu: typing.Union[None, str, modal.gpu._GPUConfig, list[typing.Union[None, str, modal.gpu._GPUConfig]]] = None,
        serialized: bool = False,
        network_file_systems: dict[
            typing.Union[str, pathlib.PurePosixPath], modal.network_file_system._NetworkFileSystem
        ] = {},
        volumes: dict[
            typing.Union[str, pathlib.PurePosixPath],
            typing.Union[modal.volume._Volume, modal.cloud_bucket_mount._CloudBucketMount],
        ] = {},
        cpu: typing.Union[float, tuple[float, float], None] = None,
        memory: typing.Union[int, tuple[int, int], None] = None,
        ephemeral_disk: typing.Optional[int] = None,
        min_containers: typing.Optional[int] = None,
        max_containers: typing.Optional[int] = None,
        buffer_containers: typing.Optional[int] = None,
        scaledown_window: typing.Optional[int] = None,
        proxy: typing.Optional[modal.proxy._Proxy] = None,
        retries: typing.Union[int, modal.retries.Retries, None] = None,
        timeout: typing.Optional[int] = None,
        name: typing.Optional[str] = None,
        is_generator: typing.Optional[bool] = None,
        cloud: typing.Optional[str] = None,
        region: typing.Union[str, collections.abc.Sequence[str], None] = None,
        enable_memory_snapshot: bool = False,
        block_network: bool = False,
        restrict_modal_access: bool = False,
        max_inputs: typing.Optional[int] = None,
        i6pn: typing.Optional[bool] = None,
        include_source: typing.Optional[bool] = None,
        experimental_options: typing.Optional[dict[str, typing.Any]] = None,
        _experimental_scheduler_placement: typing.Optional[modal.scheduler_placement.SchedulerPlacement] = None,
        _experimental_proxy_ip: typing.Optional[str] = None,
        _experimental_custom_scaling_factor: typing.Optional[float] = None,
        _experimental_enable_gpu_snapshot: bool = False,
        keep_warm: typing.Optional[int] = None,
        concurrency_limit: typing.Optional[int] = None,
        container_idle_timeout: typing.Optional[int] = None,
        allow_concurrent_inputs: typing.Optional[int] = None,
        _experimental_buffer_containers: typing.Optional[int] = None,
        allow_cross_region_volumes: typing.Optional[bool] = None,
    ) -> _FunctionDecoratorType: ...
    @typing_extensions.dataclass_transform(
        field_specifiers=(modal.cls.parameter,),
        kw_only_default=True,
    )
    def cls(
        self,
        _warn_parentheses_missing: typing.Optional[bool] = None,
        *,
        image: typing.Optional[modal.image._Image] = None,
        secrets: collections.abc.Sequence[modal.secret._Secret] = (),
        gpu: typing.Union[None, str, modal.gpu._GPUConfig, list[typing.Union[None, str, modal.gpu._GPUConfig]]] = None,
        serialized: bool = False,
        network_file_systems: dict[
            typing.Union[str, pathlib.PurePosixPath], modal.network_file_system._NetworkFileSystem
        ] = {},
        volumes: dict[
            typing.Union[str, pathlib.PurePosixPath],
            typing.Union[modal.volume._Volume, modal.cloud_bucket_mount._CloudBucketMount],
        ] = {},
        cpu: typing.Union[float, tuple[float, float], None] = None,
        memory: typing.Union[int, tuple[int, int], None] = None,
        ephemeral_disk: typing.Optional[int] = None,
        min_containers: typing.Optional[int] = None,
        max_containers: typing.Optional[int] = None,
        buffer_containers: typing.Optional[int] = None,
        scaledown_window: typing.Optional[int] = None,
        proxy: typing.Optional[modal.proxy._Proxy] = None,
        retries: typing.Union[int, modal.retries.Retries, None] = None,
        timeout: typing.Optional[int] = None,
        cloud: typing.Optional[str] = None,
        region: typing.Union[str, collections.abc.Sequence[str], None] = None,
        enable_memory_snapshot: bool = False,
        block_network: bool = False,
        restrict_modal_access: bool = False,
        max_inputs: typing.Optional[int] = None,
        include_source: typing.Optional[bool] = None,
        experimental_options: typing.Optional[dict[str, typing.Any]] = None,
        _experimental_scheduler_placement: typing.Optional[modal.scheduler_placement.SchedulerPlacement] = None,
        _experimental_proxy_ip: typing.Optional[str] = None,
        _experimental_custom_scaling_factor: typing.Optional[float] = None,
        _experimental_enable_gpu_snapshot: bool = False,
        keep_warm: typing.Optional[int] = None,
        concurrency_limit: typing.Optional[int] = None,
        container_idle_timeout: typing.Optional[int] = None,
        allow_concurrent_inputs: typing.Optional[int] = None,
        _experimental_buffer_containers: typing.Optional[int] = None,
        allow_cross_region_volumes: typing.Optional[bool] = None,
    ) -> collections.abc.Callable[[typing.Union[CLS_T, modal._partial_function._PartialFunction]], CLS_T]: ...
    def include(self, /, other_app: _App) -> typing_extensions.Self: ...
    def _logs(
        self, client: typing.Optional[modal.client._Client] = None
    ) -> collections.abc.AsyncGenerator[str, None]: ...
    @classmethod
    def _get_container_app(cls) -> typing.Optional[_App]: ...
    @classmethod
    def _reset_container_app(cls): ...

SUPERSELF = typing.TypeVar("SUPERSELF", covariant=True)

class App:
    _all_apps: typing.ClassVar[dict[typing.Optional[str], list[App]]]
    _container_app: typing.ClassVar[typing.Optional[App]]
    _name: typing.Optional[str]
    _description: typing.Optional[str]
    _functions: dict[str, modal.functions.Function]
    _classes: dict[str, modal.cls.Cls]
    _image: typing.Optional[modal.image.Image]
    _secrets: collections.abc.Sequence[modal.secret.Secret]
    _volumes: dict[typing.Union[str, pathlib.PurePosixPath], modal.volume.Volume]
    _web_endpoints: list[str]
    _local_entrypoints: dict[str, LocalEntrypoint]
    _app_id: typing.Optional[str]
    _running_app: typing.Optional[modal.running_app.RunningApp]
    _client: typing.Optional[modal.client.Client]
    _include_source_default: typing.Optional[bool]

    def __init__(
        self,
        name: typing.Optional[str] = None,
        *,
        image: typing.Optional[modal.image.Image] = None,
        secrets: collections.abc.Sequence[modal.secret.Secret] = [],
        volumes: dict[typing.Union[str, pathlib.PurePosixPath], modal.volume.Volume] = {},
        include_source: typing.Optional[bool] = None,
    ) -> None: ...
    @property
    def name(self) -> typing.Optional[str]: ...
    @property
    def is_interactive(self) -> bool: ...
    @property
    def app_id(self) -> typing.Optional[str]: ...
    @property
    def description(self) -> typing.Optional[str]: ...

    class __lookup_spec(typing_extensions.Protocol):
        def __call__(
            self,
            /,
            name: str,
            *,
            client: typing.Optional[modal.client.Client] = None,
            environment_name: typing.Optional[str] = None,
            create_if_missing: bool = False,
        ) -> App: ...
        async def aio(
            self,
            /,
            name: str,
            *,
            client: typing.Optional[modal.client.Client] = None,
            environment_name: typing.Optional[str] = None,
            create_if_missing: bool = False,
        ) -> App: ...

    lookup: __lookup_spec

    def set_description(self, description: str): ...
    def _validate_blueprint_value(self, key: str, value: typing.Any): ...
    @property
    def image(self) -> modal.image.Image: ...
    @image.setter
    def image(self, value): ...
    def _uncreate_all_objects(self): ...

    class ___set_local_app_spec(typing_extensions.Protocol[SUPERSELF]):
        def __call__(
            self, /, client: modal.client.Client, running_app: modal.running_app.RunningApp
        ) -> synchronicity.combined_types.AsyncAndBlockingContextManager[None]: ...
        def aio(
            self, /, client: modal.client.Client, running_app: modal.running_app.RunningApp
        ) -> typing.AsyncContextManager[None]: ...

    _set_local_app: ___set_local_app_spec[typing_extensions.Self]

    class __run_spec(typing_extensions.Protocol[SUPERSELF]):
        def __call__(
            self,
            /,
            *,
            client: typing.Optional[modal.client.Client] = None,
            detach: bool = False,
            interactive: bool = False,
            environment_name: typing.Optional[str] = None,
        ) -> synchronicity.combined_types.AsyncAndBlockingContextManager[App]: ...
        def aio(
            self,
            /,
            *,
            client: typing.Optional[modal.client.Client] = None,
            detach: bool = False,
            interactive: bool = False,
            environment_name: typing.Optional[str] = None,
        ) -> typing.AsyncContextManager[App]: ...

    run: __run_spec[typing_extensions.Self]

    class __deploy_spec(typing_extensions.Protocol[SUPERSELF]):
        def __call__(
            self,
            /,
            *,
            name: typing.Optional[str] = None,
            environment_name: typing.Optional[str] = None,
            tag: str = "",
            client: typing.Optional[modal.client.Client] = None,
        ) -> SUPERSELF: ...
        async def aio(
            self,
            /,
            *,
            name: typing.Optional[str] = None,
            environment_name: typing.Optional[str] = None,
            tag: str = "",
            client: typing.Optional[modal.client.Client] = None,
        ) -> SUPERSELF: ...

    deploy: __deploy_spec[typing_extensions.Self]

    def _get_default_image(self): ...
    def _get_watch_mounts(self): ...
    def _add_function(self, function: modal.functions.Function, is_web_endpoint: bool): ...
    def _add_class(self, tag: str, cls: modal.cls.Cls): ...
    def _init_container(self, client: modal.client.Client, running_app: modal.running_app.RunningApp): ...
    @property
    def registered_functions(self) -> dict[str, modal.functions.Function]: ...
    @property
    def registered_classes(self) -> dict[str, modal.cls.Cls]: ...
    @property
    def registered_entrypoints(self) -> dict[str, LocalEntrypoint]: ...
    @property
    def registered_web_endpoints(self) -> list[str]: ...
    def local_entrypoint(
        self, _warn_parentheses_missing: typing.Any = None, *, name: typing.Optional[str] = None
    ) -> collections.abc.Callable[[collections.abc.Callable[..., typing.Any]], LocalEntrypoint]: ...
    def function(
        self,
        _warn_parentheses_missing: typing.Any = None,
        *,
        image: typing.Optional[modal.image.Image] = None,
        schedule: typing.Optional[modal.schedule.Schedule] = None,
        secrets: collections.abc.Sequence[modal.secret.Secret] = (),
        gpu: typing.Union[None, str, modal.gpu._GPUConfig, list[typing.Union[None, str, modal.gpu._GPUConfig]]] = None,
        serialized: bool = False,
        network_file_systems: dict[
            typing.Union[str, pathlib.PurePosixPath], modal.network_file_system.NetworkFileSystem
        ] = {},
        volumes: dict[
            typing.Union[str, pathlib.PurePosixPath],
            typing.Union[modal.volume.Volume, modal.cloud_bucket_mount.CloudBucketMount],
        ] = {},
        cpu: typing.Union[float, tuple[float, float], None] = None,
        memory: typing.Union[int, tuple[int, int], None] = None,
        ephemeral_disk: typing.Optional[int] = None,
        min_containers: typing.Optional[int] = None,
        max_containers: typing.Optional[int] = None,
        buffer_containers: typing.Optional[int] = None,
        scaledown_window: typing.Optional[int] = None,
        proxy: typing.Optional[modal.proxy.Proxy] = None,
        retries: typing.Union[int, modal.retries.Retries, None] = None,
        timeout: typing.Optional[int] = None,
        name: typing.Optional[str] = None,
        is_generator: typing.Optional[bool] = None,
        cloud: typing.Optional[str] = None,
        region: typing.Union[str, collections.abc.Sequence[str], None] = None,
        enable_memory_snapshot: bool = False,
        block_network: bool = False,
        restrict_modal_access: bool = False,
        max_inputs: typing.Optional[int] = None,
        i6pn: typing.Optional[bool] = None,
        include_source: typing.Optional[bool] = None,
        experimental_options: typing.Optional[dict[str, typing.Any]] = None,
        _experimental_scheduler_placement: typing.Optional[modal.scheduler_placement.SchedulerPlacement] = None,
        _experimental_proxy_ip: typing.Optional[str] = None,
        _experimental_custom_scaling_factor: typing.Optional[float] = None,
        _experimental_enable_gpu_snapshot: bool = False,
        keep_warm: typing.Optional[int] = None,
        concurrency_limit: typing.Optional[int] = None,
        container_idle_timeout: typing.Optional[int] = None,
        allow_concurrent_inputs: typing.Optional[int] = None,
        _experimental_buffer_containers: typing.Optional[int] = None,
        allow_cross_region_volumes: typing.Optional[bool] = None,
    ) -> _FunctionDecoratorType: ...
    @typing_extensions.dataclass_transform(
        field_specifiers=(modal.cls.parameter,),
        kw_only_default=True,
    )
    def cls(
        self,
        _warn_parentheses_missing: typing.Optional[bool] = None,
        *,
        image: typing.Optional[modal.image.Image] = None,
        secrets: collections.abc.Sequence[modal.secret.Secret] = (),
        gpu: typing.Union[None, str, modal.gpu._GPUConfig, list[typing.Union[None, str, modal.gpu._GPUConfig]]] = None,
        serialized: bool = False,
        network_file_systems: dict[
            typing.Union[str, pathlib.PurePosixPath], modal.network_file_system.NetworkFileSystem
        ] = {},
        volumes: dict[
            typing.Union[str, pathlib.PurePosixPath],
            typing.Union[modal.volume.Volume, modal.cloud_bucket_mount.CloudBucketMount],
        ] = {},
        cpu: typing.Union[float, tuple[float, float], None] = None,
        memory: typing.Union[int, tuple[int, int], None] = None,
        ephemeral_disk: typing.Optional[int] = None,
        min_containers: typing.Optional[int] = None,
        max_containers: typing.Optional[int] = None,
        buffer_containers: typing.Optional[int] = None,
        scaledown_window: typing.Optional[int] = None,
        proxy: typing.Optional[modal.proxy.Proxy] = None,
        retries: typing.Union[int, modal.retries.Retries, None] = None,
        timeout: typing.Optional[int] = None,
        cloud: typing.Optional[str] = None,
        region: typing.Union[str, collections.abc.Sequence[str], None] = None,
        enable_memory_snapshot: bool = False,
        block_network: bool = False,
        restrict_modal_access: bool = False,
        max_inputs: typing.Optional[int] = None,
        include_source: typing.Optional[bool] = None,
        experimental_options: typing.Optional[dict[str, typing.Any]] = None,
        _experimental_scheduler_placement: typing.Optional[modal.scheduler_placement.SchedulerPlacement] = None,
        _experimental_proxy_ip: typing.Optional[str] = None,
        _experimental_custom_scaling_factor: typing.Optional[float] = None,
        _experimental_enable_gpu_snapshot: bool = False,
        keep_warm: typing.Optional[int] = None,
        concurrency_limit: typing.Optional[int] = None,
        container_idle_timeout: typing.Optional[int] = None,
        allow_concurrent_inputs: typing.Optional[int] = None,
        _experimental_buffer_containers: typing.Optional[int] = None,
        allow_cross_region_volumes: typing.Optional[bool] = None,
    ) -> collections.abc.Callable[[typing.Union[CLS_T, modal.partial_function.PartialFunction]], CLS_T]: ...
    def include(self, /, other_app: App) -> typing_extensions.Self: ...

    class ___logs_spec(typing_extensions.Protocol[SUPERSELF]):
        def __call__(
            self, /, client: typing.Optional[modal.client.Client] = None
        ) -> typing.Generator[str, None, None]: ...
        def aio(
            self, /, client: typing.Optional[modal.client.Client] = None
        ) -> collections.abc.AsyncGenerator[str, None]: ...

    _logs: ___logs_spec[typing_extensions.Self]

    @classmethod
    def _get_container_app(cls) -> typing.Optional[App]: ...
    @classmethod
    def _reset_container_app(cls): ...

_default_image: modal.image._Image
