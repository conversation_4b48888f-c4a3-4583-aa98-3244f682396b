Metadata-Version: 2.4
Name: grpclib
Version: 0.4.7
Summary: Pure-Python gRPC implementation for asyncio
Home-page: https://github.com/vmagamedov/grpclib
Author: <PERSON>
Author-email: vlad<PERSON><PERSON>@magamedov.com
License: BSD-3-Clause
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Topic :: Internet :: WWW/HTTP :: HTTP Servers
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Requires-Dist: h2<5,>=3.1.0
Requires-Dist: multidict
Provides-Extra: protobuf
Requires-Dist: protobuf>=3.20.0; extra == "protobuf"
Dynamic: license-file
Dynamic: requires-python

Pure-Python gRPC implementation for asyncio
===========================================

.. image:: https://raw.githubusercontent.com/vshymanskyy/StandWithUkraine/7e1631d13476f1e870af0d5605b643fc14471a6d/banner-direct-single.svg
  :target: https://standforukraine.com

|project|_ |documentation|_ |version|_ |tag|_ |downloads|_ |license|_

This project is based on `hyper-h2`_ and **requires Python >= 3.7**.

.. contents::
  :local:

Example
~~~~~~~

See `examples`_ directory in the project's repository for all available
examples.

Client
------

.. code-block:: python3

  import asyncio

  from grpclib.client import Channel

  # generated by protoc
  from .helloworld_pb2 import HelloRequest, HelloReply
  from .helloworld_grpc import GreeterStub


  async def main():
      async with Channel('127.0.0.1', 50051) as channel:
          greeter = GreeterStub(channel)

          reply = await greeter.SayHello(HelloRequest(name='Dr. Strange'))
          print(reply.message)


  if __name__ == '__main__':
      asyncio.run(main())

Server
------

.. code-block:: python3

  import asyncio

  from grpclib.utils import graceful_exit
  from grpclib.server import Server

  # generated by protoc
  from .helloworld_pb2 import HelloReply
  from .helloworld_grpc import GreeterBase


  class Greeter(GreeterBase):

      async def SayHello(self, stream):
          request = await stream.recv_message()
          message = f'Hello, {request.name}!'
          await stream.send_message(HelloReply(message=message))


  async def main(*, host='127.0.0.1', port=50051):
      server = Server([Greeter()])
      # Note: graceful_exit isn't supported in Windows
      with graceful_exit([server]):
          await server.start(host, port)
          print(f'Serving on {host}:{port}')
          await server.wait_closed()


  if __name__ == '__main__':
      asyncio.run(main())

Installation
~~~~~~~~~~~~

.. code-block:: console

  $ pip3 install "grpclib[protobuf]"

Bug fixes and new features are frequently published via release candidates:

.. code-block:: console

  $ pip3 install --upgrade --pre "grpclib[protobuf]"

For the code generation you will also need a ``protoc`` compiler, which can be
installed with ``protobuf`` system package:

.. code-block:: console

  $ brew install protobuf  # example for macOS users
  $ protoc --version
  libprotoc ...


**Or** you can use ``protoc`` compiler from the ``grpcio-tools`` Python package:

.. code-block:: console

  $ pip3 install grpcio-tools
  $ python3 -m grpc_tools.protoc --version
  libprotoc ...

**Note:** ``grpcio`` and ``grpcio-tools`` packages are **not required in
runtime**, ``grpcio-tools`` package will be used only during code generation.

``protoc`` plugin
~~~~~~~~~~~~~~~~~

In order to use this library you will have to generate special stub files using
plugin provided, which can be used like this:

.. code-block:: console

  $ python3 -m grpc_tools.protoc -I. --python_out=. --grpclib_python_out=. helloworld/helloworld.proto
                                                      ^----- note -----^

This command will generate ``helloworld_pb2.py`` and ``helloworld_grpc.py``
files.

Plugin which implements ``--grpclib_python_out`` option should be available for
the ``protoc`` compiler as the ``protoc-gen-grpclib_python`` executable which
should be installed by ``pip`` into your ``$PATH`` during installation of the
``grpclib`` library.

Changed in v0.3.2: ``--python_grpc_out`` option was renamed into
``--grpclib_python_out``.

Contributing
~~~~~~~~~~~~

* Please submit an issue before working on a Pull Request
* Do not merge/squash/rebase your development branch while you work on a Pull
  Request, use rebase if this is really necessary
* You may use Tox_ in order to test and lint your changes, but it is Ok to rely
  on CI for this matter

.. _gRPC: http://www.grpc.io
.. _hyper-h2: https://github.com/python-hyper/hyper-h2
.. _grpcio: https://pypi.org/project/grpcio/
.. _Tox: https://tox.readthedocs.io/
.. _examples: https://github.com/vmagamedov/grpclib/tree/master/examples
.. |version| image:: https://img.shields.io/pypi/v/grpclib.svg?label=stable&color=blue
.. _version: https://pypi.org/project/grpclib/
.. |license| image:: https://img.shields.io/pypi/l/grpclib.svg?color=blue
.. _license: https://github.com/vmagamedov/grpclib/blob/master/LICENSE.txt
.. |tag| image:: https://img.shields.io/github/tag/vmagamedov/grpclib.svg?label=latest&color=blue
.. _tag: https://pypi.org/project/grpclib/#history
.. |project| image:: https://img.shields.io/badge/vmagamedov%2Fgrpclib-blueviolet.svg?logo=github&color=blue
.. _project: https://github.com/vmagamedov/grpclib
.. |documentation| image:: https://img.shields.io/badge/docs-grpclib.rtfd.io-blue.svg
.. _documentation: https://grpclib.readthedocs.io/en/latest/
.. |downloads| image:: https://static.pepy.tech/badge/grpclib/month
.. _downloads: https://pepy.tech/project/grpclib
